export interface DiscoverUser {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  avatarUrl?: string;
  photos: UserPhoto[];
  bio?: string;
  isOnline: boolean;
  lastActiveAt: string;
  location: {
    city: string;
    country: string;
    distance: number;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  interests: string[];
  occupation?: string;
  education?: string;
  height?: number;
  relationshipGoals: string[];
  lifestyle: {
    smoking?: 'never' | 'sometimes' | 'regularly';
    drinking?: 'never' | 'socially' | 'regularly';
    exercise?: 'never' | 'sometimes' | 'regularly';
    diet?: 'omnivore' | 'vegetarian' | 'vegan' | 'other';
  };
  verificationStatus: {
    phone: boolean;
    email: boolean;
    photo: boolean;
    document: boolean;
    social: boolean;
    gosuslugi: boolean;
  };
  compatibility?: {
    score: number;
    commonInterests: string[];
    reasons: string[];
    lifestyle: number;
    values: number;
    communication: number;
  };
  premium: {
    isActive: boolean;
    features: string[];
    expiresAt?: string;
  };
  statistics: {
    profileViews: number;
    likesReceived: number;
    matchesCount: number;
    responseRate: number;
  };
}

export interface UserPhoto {
  id: string;
  url: string;
  thumbnailUrl: string;
  isMain: boolean;
  order: number;
  isVerified: boolean;
  uploadedAt: string;
  metadata?: {
    width: number;
    height: number;
    fileSize: number;
    format: string;
  };
}

export interface DiscoverFilters {
  ageRange: {
    min: number;
    max: number;
  };
  distanceRange: number;
  interests?: string[];
  relationshipGoals?: string[];
  lifestyle?: {
    smoking?: string[];
    drinking?: string[];
    exercise?: string[];
    diet?: string[];
  };
  verifiedOnly?: boolean;
  onlineOnly?: boolean;
  hasPhotosOnly?: boolean;
  premiumOnly?: boolean;
  heightRange?: {
    min: number;
    max: number;
  };
  education?: string[];
  occupation?: string[];
  location?: {
    city?: string;
    country?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
      radius: number;
    };
  };
}

export interface DiscoverSettings {
  showMe: 'men' | 'women' | 'everyone';
  ageRange: {
    min: number;
    max: number;
  };
  maxDistance: number;
  showOnlyVerified: boolean;
  showOnlyOnline: boolean;
  showOnlyWithPhotos: boolean;
  hideAlreadyLiked: boolean;
  hideAlreadyPassed: boolean;
  smartRecommendations: boolean;
  boostProfile: boolean;
  invisibleMode: boolean;
}

export interface SwipeAction {
  userId: string;
  action: 'like' | 'super_like' | 'pass' | 'boost';
  timestamp: string;
  message?: string;
}

export interface SwipeResult {
  success: boolean;
  isMatch: boolean;
  match?: {
    id: string;
    userId: string;
    matchedAt: string;
    compatibilityScore: number;
  };
  error?: string;
  remainingLikes?: number;
  remainingSuperLikes?: number;
}

export interface RecommendationReason {
  type: 'common_interests' | 'location' | 'mutual_friends' | 'similar_preferences' | 'ai_prediction';
  description: string;
  weight: number;
}

export interface UserRecommendation {
  user: DiscoverUser;
  score: number;
  reasons: RecommendationReason[];
  category: 'top_pick' | 'recently_active' | 'new_user' | 'nearby' | 'similar_interests';
}

export interface DiscoverFeed {
  users: DiscoverUser[];
  recommendations: UserRecommendation[];
  hasMore: boolean;
  nextCursor?: string;
  filters: DiscoverFilters;
  totalCount: number;
}

export interface PopularProfile {
  user: DiscoverUser;
  popularityScore: number;
  trendingReason: 'most_liked' | 'most_viewed' | 'most_matched' | 'rising_star';
  rank: number;
  changeFromLastWeek: number;
}

export interface NearbyUser {
  user: DiscoverUser;
  distance: number;
  lastSeenAt: string;
  isCurrentlyNearby: boolean;
  approximateLocation?: string;
}

export interface OnlineUser {
  user: DiscoverUser;
  onlineSince: string;
  activity: 'browsing' | 'chatting' | 'updating_profile' | 'idle';
  responseTime: 'very_fast' | 'fast' | 'moderate' | 'slow';
}

export interface NewUser {
  user: DiscoverUser;
  joinedAt: string;
  daysOnPlatform: number;
  isNewToArea: boolean;
  welcomeBonus?: {
    type: 'free_super_likes' | 'free_boost' | 'premium_trial';
    value: number;
    expiresAt: string;
  };
}

export interface DiscoverStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  nearbyUsers: number;
  onlineUsers: number;
  verifiedUsers: number;
  premiumUsers: number;
  averageAge: number;
  genderDistribution: {
    men: number;
    women: number;
    other: number;
  };
  topInterests: {
    interest: string;
    count: number;
  }[];
  topLocations: {
    city: string;
    count: number;
  }[];
}

export interface UserInteraction {
  id: string;
  targetUserId: string;
  type: 'view' | 'like' | 'super_like' | 'pass' | 'message' | 'match';
  timestamp: string;
  metadata?: {
    viewDuration?: number;
    messageText?: string;
    swipeDirection?: 'left' | 'right' | 'up';
  };
}

export interface BoostSession {
  id: string;
  userId: string;
  type: 'profile_boost' | 'super_boost' | 'location_boost';
  startedAt: string;
  duration: number; // in minutes
  expiresAt: string;
  isActive: boolean;
  statistics: {
    viewsGenerated: number;
    likesReceived: number;
    matchesCreated: number;
    messagesReceived: number;
  };
  cost: {
    amount: number;
    currency: string;
    paymentMethod: string;
  };
}

export interface SuperLike {
  id: string;
  fromUserId: string;
  toUserId: string;
  message?: string;
  sentAt: string;
  isRead: boolean;
  response?: 'liked_back' | 'passed' | 'pending';
  respondedAt?: string;
}

export interface DiscoverContextType {
  // State
  feed: DiscoverUser[];
  recommendations: UserRecommendation[];
  popularProfiles: PopularProfile[];
  nearbyUsers: NearbyUser[];
  onlineUsers: OnlineUser[];
  newUsers: NewUser[];
  loading: boolean;
  error: string | null;
  filters: DiscoverFilters;
  settings: DiscoverSettings;
  stats: DiscoverStats | null;
  
  // Actions
  loadFeed: (filters?: DiscoverFilters) => Promise<void>;
  loadRecommendations: () => Promise<void>;
  loadPopularProfiles: () => Promise<void>;
  loadNearbyUsers: (radius?: number) => Promise<void>;
  loadOnlineUsers: () => Promise<void>;
  loadNewUsers: () => Promise<void>;
  swipeUser: (action: SwipeAction) => Promise<SwipeResult>;
  updateFilters: (filters: Partial<DiscoverFilters>) => Promise<void>;
  updateSettings: (settings: Partial<DiscoverSettings>) => Promise<void>;
  boostProfile: (type: 'profile_boost' | 'super_boost' | 'location_boost') => Promise<BoostSession>;
  sendSuperLike: (userId: string, message?: string) => Promise<SuperLike>;
  reportUser: (userId: string, reason: string) => Promise<void>;
  blockUser: (userId: string) => Promise<void>;
  refreshFeed: () => Promise<void>;
  
  // Real-time updates
  subscribeToNearbyUsers: () => void;
  unsubscribeFromNearbyUsers: () => void;
  subscribeToOnlineUsers: () => void;
  unsubscribeFromOnlineUsers: () => void;
}

export interface SearchCriteria {
  query?: string;
  ageRange?: {
    min: number;
    max: number;
  };
  location?: {
    city?: string;
    country?: string;
    radius?: number;
  };
  interests?: string[];
  occupation?: string;
  education?: string;
  relationshipGoals?: string[];
  verifiedOnly?: boolean;
  onlineOnly?: boolean;
  sortBy?: 'relevance' | 'distance' | 'age' | 'last_active' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult {
  users: DiscoverUser[];
  totalCount: number;
  searchTime: number;
  suggestions: string[];
  filters: SearchCriteria;
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface TrendingTopic {
  id: string;
  name: string;
  category: 'interest' | 'location' | 'event' | 'hashtag';
  userCount: number;
  growthRate: number;
  description?: string;
}

export interface CompatibilityTest {
  id: string;
  name: string;
  description: string;
  questions: {
    id: string;
    text: string;
    type: 'multiple_choice' | 'scale' | 'yes_no';
    options?: string[];
    weight: number;
  }[];
  estimatedTime: number; // in minutes
  isPremium: boolean;
}

export interface CompatibilityResult {
  testId: string;
  score: number;
  breakdown: {
    category: string;
    score: number;
    description: string;
  }[];
  recommendations: string[];
  compatibleUsers: {
    userId: string;
    compatibilityScore: number;
  }[];
}
