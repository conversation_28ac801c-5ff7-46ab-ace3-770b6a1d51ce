import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  IconButton,
  Avatar,
  Alert,
  CircularProgress,
  Menu,
  MenuItem,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Send as SendIcon,
  AttachFile as AttachIcon,
  Mic as MicIcon,
  VideoCall as VideoIcon,
  Call as CallIcon,
  MoreVert as MoreVertIcon,
  EmojiEmotions as EmojiIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import {
  getConversation,
  getMessages,
  sendMessage,
  markMessagesAsRead,
  setTyping,
  connectToChat,
  disconnectFromChat
} from '../../src/services/chatService';
import {
  Conversation,
  ChatMessage,
  SendMessageRequest,
  PaginatedResponse
} from '../../src/types/chat.types';

const ChatPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);

  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadConversation(id);
      loadMessages(id);

      // Connect to real-time chat
      connectToChat(id, handleNewMessage);

      return () => {
        disconnectFromChat(id);
      };
    }
  }, [user, router, id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadConversation = async (conversationId: string) => {
    try {
      const conv = await getConversation(conversationId);
      setConversation(conv);
    } catch (err: any) {
      setError('Ошибка загрузки беседы');
    }
  };

  const loadMessages = async (conversationId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response: PaginatedResponse<ChatMessage> = await getMessages(conversationId);
      setMessages(response.data);

      // Mark messages as read
      const unreadMessages = response.data.filter(m => !m.readAt && m.senderId !== user?.id);
      if (unreadMessages.length > 0) {
        await markMessagesAsRead(conversationId, unreadMessages.map(m => m.id));
      }
    } catch (err: any) {
      setError('Ошибка загрузки сообщений');
    } finally {
      setLoading(false);
    }
  };

  const handleNewMessage = (message: ChatMessage) => {
    setMessages(prev => [...prev, message]);

    // Mark as read if not from current user
    if (message.senderId !== user?.id && conversation) {
      markMessagesAsRead(conversation.id, [message.id]);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || !conversation || sending) return;

    try {
      setSending(true);
      setError(null);

      const request: SendMessageRequest = {
        conversationId: conversation.id,
        text: messageText.trim(),
        type: 'text'
      };

      const result = await sendMessage(request);

      if (result.success && result.message) {
        setMessages(prev => [...prev, result.message!]);
        setMessageText('');

        // Stop typing indicator
        if (isTyping) {
          setIsTyping(false);
          setTyping(conversation.id, false);
        }
      } else {
        setError(result.error || 'Ошибка отправки сообщения');
      }
    } catch (err: any) {
      setError('Ошибка отправки сообщения');
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (text: string) => {
    setMessageText(text);

    if (!conversation) return;

    // Send typing indicator
    if (text.length > 0 && !isTyping) {
      setIsTyping(true);
      setTyping(conversation.id, true);
    }

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        setTyping(conversation.id, false);
      }
    }, 2000);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatMessageTime = (dateString: string) => {
    const messageTime = new Date(dateString);
    return messageTime.toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOtherParticipant = () => {
    if (!conversation || !user) return null;
    return conversation.participants.find(p => p.id !== user.id);
  };

  const otherParticipant = getOtherParticipant();

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка чата - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка чата...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !conversation) {
    return (
      <>
        <Head>
          <title>Ошибка загрузки чата - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (!conversation || !otherParticipant) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`Чат с ${otherParticipant.firstName} - Likes & Love`}</title>
        <meta
          name="description"
          content={`Чат с ${otherParticipant.firstName} в приложении знакомств Likes & Love`}
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 1, md: 2 } }}>
            <Paper elevation={3} sx={{ height: '85vh', display: 'flex', flexDirection: 'column' }}>
              {/* Chat Header */}
              <Box sx={{
                p: 2,
                borderBottom: 1,
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                gap: 2
              }}>
                <IconButton onClick={() => router.back()}>
                  <ArrowBack />
                </IconButton>

                <Avatar
                  src={otherParticipant.avatarUrl}
                  sx={{ width: 40, height: 40 }}
                >
                  {otherParticipant.firstName[0]}
                </Avatar>

                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h6">
                      {otherParticipant.firstName}
                    </Typography>
                    {otherParticipant.verificationStatus.phone && (
                      <VerifiedIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    )}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {otherUserTyping ? 'печатает...' :
                     otherParticipant.isOnline ? 'онлайн' :
                     `был(а) в сети ${formatMessageTime(otherParticipant.lastActiveAt)}`}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton color="primary">
                    <CallIcon />
                  </IconButton>
                  <IconButton color="primary">
                    <VideoIcon />
                  </IconButton>
                  <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
                    <MoreVertIcon />
                  </IconButton>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ m: 2 }}>
                  {error}
                </Alert>
              )}

              {/* Messages */}
              <Box sx={{
                flexGrow: 1,
                overflow: 'auto',
                p: 2,
                display: 'flex',
                flexDirection: 'column'
              }}>
                {messages.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Начните разговор!
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Напишите первое сообщение {otherParticipant.firstName}
                    </Typography>
                  </Box>
                ) : (
                  <Fade in timeout={600}>
                    <Box>
                      {messages.map((message, index) => {
                        const isOwnMessage = message.senderId === user.id;
                        const showTime = index === 0 ||
                          new Date(message.sentAt).getTime() - new Date(messages[index - 1].sentAt).getTime() > 300000; // 5 minutes

                        return (
                          <Box key={message.id} sx={{ mb: 1 }}>
                            {showTime && (
                              <Box sx={{ textAlign: 'center', my: 2 }}>
                                <Typography variant="caption" color="text.secondary">
                                  {new Date(message.sentAt).toLocaleDateString('ru-RU', {
                                    day: 'numeric',
                                    month: 'long',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{
                              display: 'flex',
                              justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
                              mb: 0.5
                            }}>
                              <Box sx={{
                                maxWidth: '70%',
                                p: 1.5,
                                borderRadius: 2,
                                backgroundColor: isOwnMessage
                                  ? theme.palette.primary.main
                                  : theme.palette.grey[200],
                                color: isOwnMessage ? 'white' : 'text.primary',
                                wordBreak: 'break-word'
                              }}>
                                <Typography variant="body2">
                                  {message.text}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    opacity: 0.7,
                                    display: 'block',
                                    mt: 0.5,
                                    textAlign: 'right'
                                  }}
                                >
                                  {formatMessageTime(message.sentAt)}
                                  {isOwnMessage && message.readAt && ' ✓✓'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </Box>
                  </Fade>
                )}
              </Box>

              {/* Message Input */}
              <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}>
                  <IconButton size="small">
                    <AttachIcon />
                  </IconButton>

                  <TextField
                    fullWidth
                    multiline
                    maxRows={4}
                    placeholder="Напишите сообщение..."
                    value={messageText}
                    onChange={(e) => handleTyping(e.target.value)}
                    onKeyPress={handleKeyPress}
                    variant="outlined"
                    size="small"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton size="small">
                            <EmojiIcon />
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />

                  {messageText.trim() ? (
                    <IconButton
                      color="primary"
                      onClick={handleSendMessage}
                      disabled={sending}
                    >
                      {sending ? <CircularProgress size={24} /> : <SendIcon />}
                    </IconButton>
                  ) : (
                    <IconButton color="primary">
                      <MicIcon />
                    </IconButton>
                  )}
                </Box>
              </Box>
            </Paper>
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
        >
          <MenuItem onClick={() => router.push(`/users/${otherParticipant.id}`)}>
            Посмотреть профиль
          </MenuItem>
          <MenuItem onClick={() => setAnchorEl(null)}>
            Отключить уведомления
          </MenuItem>
          <MenuItem onClick={() => setAnchorEl(null)}>
            Очистить историю
          </MenuItem>
          <MenuItem onClick={() => setAnchorEl(null)} sx={{ color: 'error.main' }}>
            Заблокировать пользователя
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default ChatPage;
