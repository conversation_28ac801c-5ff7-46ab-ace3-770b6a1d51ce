export interface PlaceLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  district?: string;
  postalCode?: string;
  country: string;
  formattedAddress: string;
}

export interface PlaceContact {
  phone?: string;
  email?: string;
  website?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    vk?: string;
    telegram?: string;
  };
}

export interface PlaceWorkingHours {
  monday?: { open: string; close: string; isClosed?: boolean };
  tuesday?: { open: string; close: string; isClosed?: boolean };
  wednesday?: { open: string; close: string; isClosed?: boolean };
  thursday?: { open: string; close: string; isClosed?: boolean };
  friday?: { open: string; close: string; isClosed?: boolean };
  saturday?: { open: string; close: string; isClosed?: boolean };
  sunday?: { open: string; close: string; isClosed?: boolean };
  isAlwaysOpen?: boolean;
  specialHours?: {
    date: string;
    open?: string;
    close?: string;
    isClosed?: boolean;
    note?: string;
  }[];
}

export interface PlaceAmenity {
  id: string;
  name: string;
  icon: string;
  category: 'comfort' | 'accessibility' | 'payment' | 'services' | 'entertainment' | 'food' | 'other';
  isAvailable: boolean;
  description?: string;
}

export interface PlacePhoto {
  id: string;
  url: string;
  thumbnailUrl: string;
  caption?: string;
  uploadedBy: string;
  uploadedAt: string;
  isVerified: boolean;
  category: 'exterior' | 'interior' | 'food' | 'menu' | 'atmosphere' | 'other';
}

export interface PlaceReview {
  id: string;
  placeId: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  rating: number;
  title?: string;
  comment: string;
  aspects: {
    atmosphere: number;
    service: number;
    cleanliness: number;
    value: number;
    location: number;
  };
  photos: string[];
  visitDate?: string;
  createdAt: string;
  updatedAt: string;
  isVerified: boolean;
  helpfulCount: number;
  reportCount: number;
  response?: {
    text: string;
    respondedAt: string;
    respondedBy: string;
  };
}

export interface PlaceCategory {
  id: string;
  name: string;
  slug: string;
  icon: string;
  color: string;
  description?: string;
  parentId?: string;
  subcategories?: PlaceCategory[];
  placesCount: number;
}

export interface PlaceMenu {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  category: string;
  isAvailable: boolean;
  allergens?: string[];
  dietaryInfo?: ('vegetarian' | 'vegan' | 'gluten-free' | 'halal' | 'kosher')[];
  photos?: string[];
}

export interface Place {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  category: PlaceCategory;
  subcategory?: PlaceCategory;
  
  location: PlaceLocation;
  contact: PlaceContact;
  workingHours: PlaceWorkingHours;
  
  rating: {
    average: number;
    count: number;
    distribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
    aspects: {
      atmosphere: number;
      service: number;
      cleanliness: number;
      value: number;
      location: number;
    };
  };
  
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  averageCheck?: {
    min: number;
    max: number;
    currency: string;
  };
  
  photos: PlacePhoto[];
  amenities: PlaceAmenity[];
  tags: string[];
  
  capacity?: number;
  reservationRequired?: boolean;
  acceptsCards: boolean;
  acceptsCash: boolean;
  hasWifi: boolean;
  hasParking: boolean;
  isAccessible: boolean;
  allowsSmoking: boolean;
  isPetFriendly: boolean;
  hasDelivery: boolean;
  hasTakeaway: boolean;
  
  menu?: PlaceMenu[];
  
  verification: {
    isVerified: boolean;
    verifiedAt?: string;
    verifiedBy?: string;
    claimedBy?: string;
    claimedAt?: string;
  };
  
  statistics: {
    views: number;
    favorites: number;
    checkins: number;
    shares: number;
    meetingsHeld: number;
    eventsHeld: number;
  };
  
  status: 'active' | 'temporarily_closed' | 'permanently_closed' | 'coming_soon';
  
  createdAt: string;
  updatedAt: string;
  
  // User-specific data (populated when user is authenticated)
  userInteraction?: {
    isFavorite: boolean;
    hasVisited: boolean;
    hasReviewed: boolean;
    lastVisit?: string;
    userRating?: number;
  };
  
  // Distance from user (populated in location-based queries)
  distance?: {
    value: number;
    unit: 'km' | 'miles';
    walkingTime?: number;
    drivingTime?: number;
  };
}

export interface PlaceFilters {
  category?: string[];
  subcategory?: string[];
  priceRange?: Place['priceRange'][];
  rating?: {
    min: number;
    max?: number;
  };
  distance?: {
    radius: number;
    unit: 'km' | 'miles';
    center: {
      latitude: number;
      longitude: number;
    };
  };
  amenities?: string[];
  tags?: string[];
  workingHours?: {
    isOpenNow?: boolean;
    openAt?: string;
    day?: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  };
  features?: {
    hasWifi?: boolean;
    hasParking?: boolean;
    isAccessible?: boolean;
    isPetFriendly?: boolean;
    acceptsCards?: boolean;
    hasDelivery?: boolean;
    hasTakeaway?: boolean;
    reservationRequired?: boolean;
  };
  verification?: {
    verifiedOnly?: boolean;
    claimedOnly?: boolean;
  };
  sortBy?: 'relevance' | 'distance' | 'rating' | 'popularity' | 'price_low' | 'price_high' | 'newest' | 'name';
  sortOrder?: 'asc' | 'desc';
}

export interface PlaceSearchResult {
  places: Place[];
  totalCount: number;
  filters: PlaceFilters;
  suggestions: string[];
  facets: {
    categories: { name: string; count: number; slug: string }[];
    priceRanges: { range: Place['priceRange']; count: number }[];
    amenities: { name: string; count: number; id: string }[];
    districts: { name: string; count: number }[];
  };
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  mapBounds?: {
    northeast: { latitude: number; longitude: number };
    southwest: { latitude: number; longitude: number };
  };
}

export interface CreatePlaceRequest {
  name: string;
  description: string;
  shortDescription?: string;
  categoryId: string;
  subcategoryId?: string;
  location: Omit<PlaceLocation, 'formattedAddress'>;
  contact: PlaceContact;
  workingHours: PlaceWorkingHours;
  priceRange: Place['priceRange'];
  averageCheck?: Place['averageCheck'];
  amenities: string[];
  tags: string[];
  capacity?: number;
  reservationRequired?: boolean;
  acceptsCards: boolean;
  acceptsCash: boolean;
  hasWifi: boolean;
  hasParking: boolean;
  isAccessible: boolean;
  allowsSmoking: boolean;
  isPetFriendly: boolean;
  hasDelivery: boolean;
  hasTakeaway: boolean;
  photos?: File[];
  menu?: Omit<PlaceMenu, 'id'>[];
}

export interface UpdatePlaceRequest {
  name?: string;
  description?: string;
  shortDescription?: string;
  contact?: PlaceContact;
  workingHours?: PlaceWorkingHours;
  priceRange?: Place['priceRange'];
  averageCheck?: Place['averageCheck'];
  amenities?: string[];
  tags?: string[];
  capacity?: number;
  reservationRequired?: boolean;
  acceptsCards?: boolean;
  acceptsCash?: boolean;
  hasWifi?: boolean;
  hasParking?: boolean;
  isAccessible?: boolean;
  allowsSmoking?: boolean;
  isPetFriendly?: boolean;
  hasDelivery?: boolean;
  hasTakeaway?: boolean;
  status?: Place['status'];
}

export interface CreateReviewRequest {
  placeId: string;
  rating: number;
  title?: string;
  comment: string;
  aspects: PlaceReview['aspects'];
  visitDate?: string;
  photos?: File[];
}

export interface PlaceCheckIn {
  id: string;
  placeId: string;
  place: Place;
  userId: string;
  checkedInAt: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  note?: string;
  photos?: string[];
  isPrivate: boolean;
}

export interface PlaceSuggestion {
  id: string;
  type: 'nearby' | 'popular' | 'trending' | 'similar' | 'recommended' | 'friend_activity';
  place: Place;
  reason: string;
  score: number;
  expiresAt: string;
}

export interface PlaceStatistics {
  totalPlaces: number;
  verifiedPlaces: number;
  categoriesCount: number;
  averageRating: number;
  totalReviews: number;
  totalCheckins: number;
  popularCategories: {
    category: PlaceCategory;
    count: number;
  }[];
  topRatedPlaces: Place[];
  trendingPlaces: Place[];
  recentlyAdded: Place[];
  userStats?: {
    favoritePlaces: number;
    reviewsWritten: number;
    placesVisited: number;
    checkinsCount: number;
  };
}

export interface PlaceContextType {
  // State
  places: Place[];
  favoritePlace: Place[];
  nearbyPlaces: Place[];
  categories: PlaceCategory[];
  loading: boolean;
  error: string | null;
  filters: PlaceFilters;
  userLocation: { latitude: number; longitude: number } | null;
  
  // Actions
  loadPlaces: (filters?: PlaceFilters) => Promise<void>;
  loadFavoritePlaces: () => Promise<void>;
  loadNearbyPlaces: (radius?: number) => Promise<void>;
  loadCategories: () => Promise<void>;
  getPlace: (id: string) => Promise<Place>;
  createPlace: (request: CreatePlaceRequest) => Promise<Place>;
  updatePlace: (id: string, request: UpdatePlaceRequest) => Promise<Place>;
  deletePlace: (id: string) => Promise<void>;
  addToFavorites: (placeId: string) => Promise<void>;
  removeFromFavorites: (placeId: string) => Promise<void>;
  checkInToPlace: (placeId: string, note?: string, photos?: File[]) => Promise<PlaceCheckIn>;
  createReview: (request: CreateReviewRequest) => Promise<PlaceReview>;
  updateReview: (reviewId: string, request: Partial<CreateReviewRequest>) => Promise<PlaceReview>;
  deleteReview: (reviewId: string) => Promise<void>;
  reportPlace: (placeId: string, reason: string, details?: string) => Promise<void>;
  reportReview: (reviewId: string, reason: string, details?: string) => Promise<void>;
  searchPlaces: (query: string, filters?: PlaceFilters) => Promise<PlaceSearchResult>;
  getSuggestions: (type?: PlaceSuggestion['type']) => Promise<PlaceSuggestion[]>;
  updateFilters: (filters: Partial<PlaceFilters>) => void;
  getUserLocation: () => Promise<{ latitude: number; longitude: number }>;
  getStatistics: () => Promise<PlaceStatistics>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
