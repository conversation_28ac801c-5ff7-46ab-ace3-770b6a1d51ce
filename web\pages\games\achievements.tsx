import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Grid,
  Avatar,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ArrowBack,
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  Lock as LockIcon,
  Share as ShareIcon,
  TrendingUp as TrendingIcon,
  Psychology as PsychologyIcon,
  Favorite as HeartIcon,
  Quiz as QuizIcon,
  People as PeopleIcon,
  Timer as TimerIcon,
  LocalFireDepartment as FireIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для достижений
interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'games' | 'social' | 'profile' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  isUnlocked: boolean;
  unlockedAt?: string;
  progress?: {
    current: number;
    total: number;
  };
  requirements: string[];
}

interface UserStats {
  totalPoints: number;
  level: number;
  nextLevelProgress: number;
  totalAchievements: number;
  unlockedAchievements: number;
  rank: number;
  streakDays: number;
}

interface Leaderboard {
  userId: string;
  name: string;
  avatar: string;
  points: number;
  level: number;
  rank: number;
}

const AchievementsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [leaderboard, setLeaderboard] = useState<Leaderboard[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  const tabs = [
    { label: 'Все', value: 'all' },
    { label: 'Игры', value: 'games' },
    { label: 'Социальные', value: 'social' },
    { label: 'Профиль', value: 'profile' },
    { label: 'Особые', value: 'special' }
  ];

  // Загрузка данных при монтировании компонента
  useEffect(() => {
    loadAchievements();
    loadUserStats();
    loadLeaderboard();
  }, []);

  const loadAchievements = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения достижений
      // const response = await getAchievements();
      
      // Мок данные
      const mockAchievements: Achievement[] = [
        {
          id: 'first_game',
          title: 'Первая игра',
          description: 'Сыграйте в любую игру впервые',
          icon: '🎮',
          category: 'games',
          rarity: 'common',
          points: 10,
          isUnlocked: true,
          unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          requirements: ['Завершить любую игру']
        },
        {
          id: 'compatibility_master',
          title: 'Мастер совместимости',
          description: 'Пройдите тест совместимости 5 раз',
          icon: '💕',
          category: 'games',
          rarity: 'rare',
          points: 50,
          isUnlocked: true,
          unlockedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          progress: { current: 5, total: 5 },
          requirements: ['Пройти тест совместимости 5 раз']
        },
        {
          id: 'social_butterfly',
          title: 'Социальная бабочка',
          description: 'Получите 100 лайков от других пользователей',
          icon: '🦋',
          category: 'social',
          rarity: 'epic',
          points: 100,
          isUnlocked: false,
          progress: { current: 67, total: 100 },
          requirements: ['Получить 100 лайков']
        },
        {
          id: 'profile_perfectionist',
          title: 'Перфекционист профиля',
          description: 'Заполните профиль на 100%',
          icon: '✨',
          category: 'profile',
          rarity: 'rare',
          points: 75,
          isUnlocked: true,
          unlockedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          requirements: ['Заполнить все поля профиля', 'Загрузить минимум 3 фото']
        },
        {
          id: 'question_master',
          title: 'Мастер вопросов',
          description: 'Ответьте на 50 вопросов в играх',
          icon: '❓',
          category: 'games',
          rarity: 'rare',
          points: 60,
          isUnlocked: false,
          progress: { current: 23, total: 50 },
          requirements: ['Ответить на 50 вопросов']
        },
        {
          id: 'streak_week',
          title: 'Недельная серия',
          description: 'Заходите в приложение 7 дней подряд',
          icon: '🔥',
          category: 'special',
          rarity: 'common',
          points: 25,
          isUnlocked: true,
          unlockedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          requirements: ['Заходить 7 дней подряд']
        },
        {
          id: 'legendary_lover',
          title: 'Легендарный любовник',
          description: 'Достигните 95%+ совместимости с кем-то',
          icon: '👑',
          category: 'special',
          rarity: 'legendary',
          points: 200,
          isUnlocked: false,
          requirements: ['Получить 95%+ совместимости']
        },
        {
          id: 'chat_champion',
          title: 'Чемпион чатов',
          description: 'Отправьте 1000 сообщений',
          icon: '💬',
          category: 'social',
          rarity: 'epic',
          points: 150,
          isUnlocked: false,
          progress: { current: 342, total: 1000 },
          requirements: ['Отправить 1000 сообщений']
        }
      ];

      setAchievements(mockAchievements);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке достижений');
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    try {
      // Здесь будет вызов API для получения статистики пользователя
      // const response = await getUserStats();
      
      // Мок данные
      const mockStats: UserStats = {
        totalPoints: 1250,
        level: 8,
        nextLevelProgress: 65,
        totalAchievements: 25,
        unlockedAchievements: 12,
        rank: 1247,
        streakDays: 7
      };

      setUserStats(mockStats);
    } catch (err) {
      console.error('Ошибка при загрузке статистики:', err);
    }
  };

  const loadLeaderboard = async () => {
    try {
      // Здесь будет вызов API для получения таблицы лидеров
      // const response = await getLeaderboard();
      
      // Мок данные
      const mockLeaderboard: Leaderboard[] = [
        { userId: 'user1', name: 'Анна Петрова', avatar: '/avatars/anna.jpg', points: 2850, level: 15, rank: 1 },
        { userId: 'user2', name: 'Михаил Иванов', avatar: '/avatars/mikhail.jpg', points: 2340, level: 12, rank: 2 },
        { userId: 'user3', name: 'Елена Сидорова', avatar: '/avatars/elena.jpg', points: 1890, level: 10, rank: 3 },
        { userId: 'user4', name: 'Дмитрий Козлов', avatar: '/avatars/dmitry.jpg', points: 1650, level: 9, rank: 4 },
        { userId: 'current', name: user?.name || 'Вы', avatar: user?.avatar || '', points: 1250, level: 8, rank: 1247 }
      ];

      setLeaderboard(mockLeaderboard);
    } catch (err) {
      console.error('Ошибка при загрузке таблицы лидеров:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleAchievementClick = (achievement: Achievement) => {
    setSelectedAchievement(achievement);
    setDetailDialogOpen(true);
  };

  const handleShareAchievement = async (achievement: Achievement) => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Достижение: ${achievement.title}`,
          text: `Я получил достижение "${achievement.title}" в Likes & Love!`,
          url: window.location.href
        });
      } else {
        await navigator.clipboard.writeText(
          `Я получил достижение "${achievement.title}" в Likes & Love! ${window.location.origin}/games/achievements`
        );
        setSuccess('Ссылка скопирована в буфер обмена');
      }
    } catch (err) {
      console.error('Ошибка при шаринге:', err);
    }
  };

  const getFilteredAchievements = () => {
    const tabValue = tabs[activeTab].value;
    if (tabValue === 'all') return achievements;
    return achievements.filter(achievement => achievement.category === tabValue);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'grey';
      case 'rare': return 'primary';
      case 'epic': return 'secondary';
      case 'legendary': return 'warning';
      default: return 'default';
    }
  };

  const getRarityLabel = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'Обычное';
      case 'rare': return 'Редкое';
      case 'epic': return 'Эпическое';
      case 'legendary': return 'Легендарное';
      default: return rarity;
    }
  };

  const handleBack = () => {
    router.push('/games');
  };

  if (!user) {
    return (
      <Layout title="Достижения">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  const filteredAchievements = getFilteredAchievements();

  return (
    <>
      <Head>
        <title>Достижения - Likes & Love</title>
        <meta name="description" content="Просматривайте свои достижения и прогресс в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Достижения">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Достижения
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Ваши награды и прогресс в играх
              </Typography>
            </Box>

            {/* Статистика пользователя */}
            {userStats && (
              <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                <CardContent>
                  <Grid container spacing={3} alignItems="center">
                    <Grid item xs={12} md={8}>
                      <Box sx={{ color: 'white' }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                          Ваша статистика
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {userStats.level}
                              </Typography>
                              <Typography variant="body2">Уровень</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {userStats.totalPoints}
                              </Typography>
                              <Typography variant="body2">Очки</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {userStats.unlockedAchievements}
                              </Typography>
                              <Typography variant="body2">Достижения</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                #{userStats.rank}
                              </Typography>
                              <Typography variant="body2">Рейтинг</Typography>
                            </Box>
                          </Grid>
                        </Grid>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Прогресс до {userStats.level + 1} уровня
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={userStats.nextLevelProgress}
                            sx={{ 
                              height: 8, 
                              borderRadius: 4,
                              backgroundColor: 'rgba(255,255,255,0.3)',
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: 'white'
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', color: 'white' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>
                          <FireIcon />
                          <Typography variant="h5" fontWeight="bold">
                            {userStats.streakDays} дней
                          </Typography>
                        </Box>
                        <Typography variant="body2">
                          Текущая серия
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Табы категорий */}
            <Box sx={{ mb: 4 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                {tabs.map((tab, index) => (
                  <Tab
                    key={tab.value}
                    label={`${tab.label} ${tab.value === 'all' ? 
                      `(${achievements.length})` : 
                      `(${achievements.filter(a => a.category === tab.value).length})`}`}
                  />
                ))}
              </Tabs>
            </Box>

            {/* Сетка достижений */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {filteredAchievements.map((achievement) => (
                  <Grid item xs={12} sm={6} md={4} key={achievement.id}>
                    <Card 
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        position: 'relative',
                        opacity: achievement.isUnlocked ? 1 : 0.6,
                        '&:hover': {
                          transform: 'scale(1.02)',
                          transition: 'transform 0.2s',
                          boxShadow: theme.shadows[8]
                        }
                      }}
                      onClick={() => handleAchievementClick(achievement)}
                    >
                      {/* Статус блокировки */}
                      {!achievement.isUnlocked && (
                        <Box sx={{ 
                          position: 'absolute', 
                          top: 8, 
                          right: 8, 
                          zIndex: 1,
                          backgroundColor: 'rgba(0,0,0,0.7)',
                          borderRadius: '50%',
                          p: 0.5
                        }}>
                          <LockIcon sx={{ color: 'white', fontSize: 20 }} />
                        </Box>
                      )}

                      <CardContent sx={{ textAlign: 'center' }}>
                        {/* Иконка достижения */}
                        <Box sx={{ 
                          fontSize: 48, 
                          mb: 2,
                          filter: achievement.isUnlocked ? 'none' : 'grayscale(100%)'
                        }}>
                          {achievement.icon}
                        </Box>

                        {/* Название и редкость */}
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {achievement.title}
                        </Typography>
                        <Chip 
                          label={getRarityLabel(achievement.rarity)}
                          color={getRarityColor(achievement.rarity) as any}
                          size="small"
                          sx={{ mb: 2 }}
                        />

                        {/* Описание */}
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {achievement.description}
                        </Typography>

                        {/* Прогресс */}
                        {achievement.progress && !achievement.isUnlocked && (
                          <Box sx={{ mb: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="caption">Прогресс</Typography>
                              <Typography variant="caption">
                                {achievement.progress.current}/{achievement.progress.total}
                              </Typography>
                            </Box>
                            <LinearProgress 
                              variant="determinate" 
                              value={(achievement.progress.current / achievement.progress.total) * 100}
                              sx={{ height: 6, borderRadius: 3 }}
                            />
                          </Box>
                        )}

                        {/* Очки и дата */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <StarIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                            <Typography variant="caption">
                              {achievement.points} очков
                            </Typography>
                          </Box>
                          {achievement.isUnlocked && achievement.unlockedAt && (
                            <Typography variant="caption" color="text.secondary">
                              {new Date(achievement.unlockedAt).toLocaleDateString()}
                            </Typography>
                          )}
                        </Box>

                        {/* Кнопка поделиться */}
                        {achievement.isUnlocked && (
                          <Button
                            size="small"
                            startIcon={<ShareIcon />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleShareAchievement(achievement);
                            }}
                            sx={{ mt: 1 }}
                          >
                            Поделиться
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Пустое состояние */}
            {!loading && filteredAchievements.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <TrophyIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                  Достижения не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  В этой категории пока нет достижений
                </Typography>
              </Box>
            )}

            {/* Таблица лидеров */}
            {leaderboard.length > 0 && (
              <Card sx={{ mt: 4 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                    Таблица лидеров
                  </Typography>
                  {leaderboard.map((leader, index) => (
                    <Box 
                      key={leader.userId}
                      sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 2, 
                        py: 1,
                        backgroundColor: leader.userId === 'current' ? 'primary.light' : 'transparent',
                        borderRadius: 1,
                        px: leader.userId === 'current' ? 1 : 0
                      }}
                    >
                      <Typography variant="h6" fontWeight="bold" sx={{ minWidth: 30 }}>
                        #{leader.rank}
                      </Typography>
                      <Avatar src={leader.avatar} sx={{ width: 40, height: 40 }} />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {leader.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Уровень {leader.level}
                        </Typography>
                      </Box>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {leader.points} очков
                      </Typography>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Диалог деталей достижения */}
            <Dialog
              open={detailDialogOpen}
              onClose={() => setDetailDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              {selectedAchievement && (
                <>
                  <DialogTitle sx={{ textAlign: 'center' }}>
                    <Box sx={{ fontSize: 64, mb: 2 }}>
                      {selectedAchievement.icon}
                    </Box>
                    <Typography variant="h5" fontWeight="bold">
                      {selectedAchievement.title}
                    </Typography>
                    <Chip 
                      label={getRarityLabel(selectedAchievement.rarity)}
                      color={getRarityColor(selectedAchievement.rarity) as any}
                      sx={{ mt: 1 }}
                    />
                  </DialogTitle>
                  <DialogContent>
                    <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
                      {selectedAchievement.description}
                    </Typography>

                    {selectedAchievement.progress && !selectedAchievement.isUnlocked && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                          Прогресс
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">
                            {selectedAchievement.progress.current} из {selectedAchievement.progress.total}
                          </Typography>
                          <Typography variant="body2">
                            {Math.round((selectedAchievement.progress.current / selectedAchievement.progress.total) * 100)}%
                          </Typography>
                        </Box>
                        <LinearProgress 
                          variant="determinate" 
                          value={(selectedAchievement.progress.current / selectedAchievement.progress.total) * 100}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    )}

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                        Требования
                      </Typography>
                      {selectedAchievement.requirements.map((requirement, index) => (
                        <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                          • {requirement}
                        </Typography>
                      ))}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <StarIcon color="warning" />
                        <Typography variant="body2">
                          {selectedAchievement.points} очков
                        </Typography>
                      </Box>
                      {selectedAchievement.isUnlocked && selectedAchievement.unlockedAt && (
                        <Typography variant="body2" color="text.secondary">
                          Получено: {new Date(selectedAchievement.unlockedAt).toLocaleDateString()}
                        </Typography>
                      )}
                    </Box>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={() => setDetailDialogOpen(false)}>
                      Закрыть
                    </Button>
                    {selectedAchievement.isUnlocked && (
                      <Button
                        onClick={() => handleShareAchievement(selectedAchievement)}
                        variant="contained"
                        startIcon={<ShareIcon />}
                      >
                        Поделиться
                      </Button>
                    )}
                  </DialogActions>
                </>
              )}
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default AchievementsPage;
