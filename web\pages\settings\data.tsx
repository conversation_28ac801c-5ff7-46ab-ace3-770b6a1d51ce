import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
  useTheme,
  useMediaQuery,
  Fade,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  ArrowBack,
  Storage as StorageIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  CloudDownload as CloudDownloadIcon,
  DeleteForever as DeleteForeverIcon,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
  FileDownload as FileDownloadIcon,
  Folder as FolderIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSettings } from '../../src/contexts/SettingsContext';

const DataManagementPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    exportData,
    deleteAccount,
    deactivateAccount,
    cancelAccountDeletion,
    loadSettings 
  } = useSettings();

  const [success, setSuccess] = useState<string | null>(null);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    format: 'json' as 'json' | 'csv' | 'pdf',
    includeMessages: true,
    includePhotos: true,
    includeMatches: true,
    includeProfile: true
  });
  const [deactivateForm, setDeactivateForm] = useState({
    reason: '',
    feedback: '',
    password: ''
  });
  const [deleteForm, setDeleteForm] = useState({
    reason: '',
    feedback: '',
    password: '',
    confirmDeletion: false
  });
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  const handleExportData = async () => {
    try {
      setIsExporting(true);
      setExportProgress(0);
      
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 500);

      const exportRequest = await exportData(exportOptions);
      
      clearInterval(progressInterval);
      setExportProgress(100);
      
      setSuccess('Запрос на экспорт данных создан. Ссылка для скачивания будет отправлена на email.');
      setShowExportDialog(false);
    } catch (err: any) {
      // Error handled by context
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const handleDeactivateAccount = async () => {
    try {
      await deactivateAccount({
        reason: deactivateForm.reason,
        feedback: deactivateForm.feedback,
        password: deactivateForm.password
      });
      setSuccess('Аккаунт деактивирован. Вы можете восстановить его в течение 30 дней.');
      setShowDeactivateDialog(false);
    } catch (err: any) {
      // Error handled by context
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await deleteAccount({
        reason: deleteForm.reason,
        feedback: deleteForm.feedback,
        password: deleteForm.password,
        confirmDeletion: deleteForm.confirmDeletion
      });
      setSuccess('Запрос на удаление аккаунта создан. Удаление произойдет через 30 дней.');
      setShowDeleteDialog(false);
    } catch (err: any) {
      // Error handled by context
    }
  };

  const handleCancelDeletion = async () => {
    try {
      await cancelAccountDeletion();
      setSuccess('Удаление аккаунта отменено');
      loadSettings();
    } catch (err: any) {
      // Error handled by context
    }
  };

  const dataCategories = [
    {
      name: 'Профиль',
      description: 'Личная информация, фотографии, описание',
      size: '2.5 МБ',
      items: 12
    },
    {
      name: 'Сообщения',
      description: 'История переписок и медиафайлы',
      size: '45.2 МБ',
      items: 1247
    },
    {
      name: 'Совпадения',
      description: 'Список совпадений и взаимодействий',
      size: '1.8 МБ',
      items: 89
    },
    {
      name: 'Активность',
      description: 'История просмотров, лайков, действий',
      size: '3.1 МБ',
      items: 2156
    },
    {
      name: 'Настройки',
      description: 'Предпочтения и конфигурация аккаунта',
      size: '0.1 МБ',
      items: 1
    }
  ];

  const exportFormats = [
    { value: 'json', label: 'JSON', description: 'Структурированные данные для разработчиков' },
    { value: 'csv', label: 'CSV', description: 'Таблицы для Excel и других программ' },
    { value: 'pdf', label: 'PDF', description: 'Читаемый отчет для печати' }
  ];

  const deactivationReasons = [
    'Временный перерыв',
    'Нашел(а) отношения',
    'Слишком много времени тратится',
    'Проблемы с конфиденциальностью',
    'Неудовлетворен(а) сервисом',
    'Другое'
  ];

  const deletionReasons = [
    'Больше не нужно приложение',
    'Проблемы с конфиденциальностью',
    'Слишком много спама',
    'Нашел(а) отношения',
    'Переход на другой сервис',
    'Другое'
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Управление данными - Likes & Love</title>
        <meta 
          name="description" 
          content="Экспорт данных и управление аккаунтом в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <StorageIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Управление данными
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {/* Account Deletion Warning */}
            {settings?.account.deletionScheduledAt && (
              <Alert severity="warning" sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      Аккаунт запланирован к удалению
                    </Typography>
                    <Typography variant="body2">
                      Удаление произойдет: {new Date(settings.account.deletionScheduledAt).toLocaleDateString('ru-RU')}
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleCancelDeletion}
                  >
                    Отменить удаление
                  </Button>
                </Box>
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка данных...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* Data Overview */}
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <FolderIcon color="primary" />
                        <Typography variant="h6">Ваши данные</Typography>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Обзор данных, хранящихся в вашем аккаунте
                      </Typography>
                      
                      <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Категория</TableCell>
                              <TableCell>Описание</TableCell>
                              <TableCell align="right">Размер</TableCell>
                              <TableCell align="right">Элементов</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dataCategories.map((category) => (
                              <TableRow key={category.name}>
                                <TableCell component="th" scope="row">
                                  <Typography variant="body2" fontWeight="medium">
                                    {category.name}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2" color="text.secondary">
                                    {category.description}
                                  </Typography>
                                </TableCell>
                                <TableCell align="right">
                                  <Typography variant="body2">
                                    {category.size}
                                  </Typography>
                                </TableCell>
                                <TableCell align="right">
                                  <Typography variant="body2">
                                    {category.items}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                      
                      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                        <Button
                          variant="contained"
                          startIcon={<DownloadIcon />}
                          onClick={() => setShowExportDialog(true)}
                          size="large"
                        >
                          Экспортировать данные
                        </Button>
                      </Box>
                    </AccordionDetails>
                  </Accordion>

                  {/* Export History */}
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <CloudDownloadIcon color="primary" />
                        <Typography variant="h6">История экспорта</Typography>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      {settings?.account.exportRequests && settings.account.exportRequests.length > 0 ? (
                        <List>
                          {settings.account.exportRequests.map((request, index) => (
                            <React.Fragment key={request.id}>
                              <ListItem>
                                <ListItemIcon>
                                  <DescriptionIcon />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`Экспорт ${request.format.toUpperCase()}`}
                                  secondary={
                                    <Box>
                                      <Typography variant="body2" color="text.secondary">
                                        Создан: {new Date(request.requestedAt).toLocaleDateString('ru-RU')}
                                      </Typography>
                                      {request.fileSize && (
                                        <Typography variant="caption" color="text.secondary">
                                          Размер: {(request.fileSize / 1024 / 1024).toFixed(1)} МБ
                                        </Typography>
                                      )}
                                    </Box>
                                  }
                                />
                                <ListItemSecondaryAction>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Chip
                                      label={
                                        request.status === 'completed' ? 'Готов' :
                                        request.status === 'processing' ? 'Обработка' :
                                        request.status === 'failed' ? 'Ошибка' : 'Ожидание'
                                      }
                                      color={
                                        request.status === 'completed' ? 'success' :
                                        request.status === 'processing' ? 'warning' :
                                        request.status === 'failed' ? 'error' : 'default'
                                      }
                                      size="small"
                                    />
                                    {request.status === 'completed' && request.downloadUrl && (
                                      <Button
                                        size="small"
                                        startIcon={<FileDownloadIcon />}
                                        onClick={() => window.open(request.downloadUrl, '_blank')}
                                      >
                                        Скачать
                                      </Button>
                                    )}
                                  </Box>
                                </ListItemSecondaryAction>
                              </ListItem>
                              {index < settings.account.exportRequests.length - 1 && <Divider />}
                            </React.Fragment>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                          История экспорта пуста
                        </Typography>
                      )}
                    </AccordionDetails>
                  </Accordion>

                  {/* Account Management */}
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <SecurityIcon color="primary" />
                        <Typography variant="h6">Управление аккаунтом</Typography>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Alert severity="warning" sx={{ mb: 3 }}>
                        <Typography variant="body2">
                          <strong>Внимание:</strong> Действия в этом разделе необратимы или имеют серьезные последствия.
                        </Typography>
                      </Alert>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <ScheduleIcon color="warning" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Деактивировать аккаунт"
                            secondary="Временно скрыть профиль. Можно восстановить в течение 30 дней."
                          />
                          <ListItemSecondaryAction>
                            <Button
                              variant="outlined"
                              color="warning"
                              onClick={() => setShowDeactivateDialog(true)}
                              disabled={settings?.account.accountStatus !== 'active'}
                            >
                              Деактивировать
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <DeleteForeverIcon color="error" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Удалить аккаунт"
                            secondary="Безвозвратно удалить все данные. Можно отменить в течение 30 дней."
                          />
                          <ListItemSecondaryAction>
                            <Button
                              variant="outlined"
                              color="error"
                              onClick={() => setShowDeleteDialog(true)}
                              disabled={settings?.account.accountStatus !== 'active'}
                            >
                              Удалить
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </AccordionDetails>
                  </Accordion>

                  {/* Privacy Information */}
                  <Card sx={{ mt: 4, backgroundColor: 'background.default' }}>
                    <CardHeader
                      avatar={<InfoIcon color="primary" />}
                      title="Информация о конфиденциальности"
                      titleTypographyProps={{ variant: 'h6' }}
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Мы серьезно относимся к защите ваших данных:
                      </Typography>

                      <Box component="ul" sx={{ pl: 2, m: 0 }}>
                        <Typography component="li" variant="body2" color="text.secondary">
                          Данные шифруются при передаче и хранении
                        </Typography>
                        <Typography component="li" variant="body2" color="text.secondary">
                          Доступ к данным имеют только авторизованные сотрудники
                        </Typography>
                        <Typography component="li" variant="body2" color="text.secondary">
                          Мы не продаем ваши данные третьим лицам
                        </Typography>
                        <Typography component="li" variant="body2" color="text.secondary">
                          Вы можете запросить удаление данных в любое время
                        </Typography>
                      </Box>

                      <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => router.push('/privacy-policy')}
                        >
                          Политика конфиденциальности
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => router.push('/terms')}
                        >
                          Условия использования
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Fade>
            )}

            {/* Export Data Dialog */}
            <Dialog
              open={showExportDialog}
              onClose={() => setShowExportDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Экспорт данных</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Выберите формат и данные для экспорта:
                  </Typography>

                  <TextField
                    select
                    fullWidth
                    label="Формат файла"
                    value={exportOptions.format}
                    onChange={(e) => setExportOptions({ ...exportOptions, format: e.target.value as any })}
                    sx={{ mt: 2, mb: 2 }}
                  >
                    {exportFormats.map((format) => (
                      <MenuItem key={format.value} value={format.value}>
                        <Box>
                          <Typography variant="body2">{format.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {format.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </TextField>

                  <Typography variant="body2" gutterBottom>
                    Включить в экспорт:
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={exportOptions.includeProfile}
                          onChange={(e) => setExportOptions({ ...exportOptions, includeProfile: e.target.checked })}
                        />
                      }
                      label="Данные профиля"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={exportOptions.includeMessages}
                          onChange={(e) => setExportOptions({ ...exportOptions, includeMessages: e.target.checked })}
                        />
                      }
                      label="Сообщения"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={exportOptions.includePhotos}
                          onChange={(e) => setExportOptions({ ...exportOptions, includePhotos: e.target.checked })}
                        />
                      }
                      label="Фотографии"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={exportOptions.includeMatches}
                          onChange={(e) => setExportOptions({ ...exportOptions, includeMatches: e.target.checked })}
                        />
                      }
                      label="Совпадения"
                    />
                  </Box>

                  {isExporting && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Подготовка данных... {exportProgress}%
                      </Typography>
                      <LinearProgress variant="determinate" value={exportProgress} />
                    </Box>
                  )}
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowExportDialog(false)} disabled={isExporting}>
                  Отмена
                </Button>
                <Button
                  onClick={handleExportData}
                  variant="contained"
                  disabled={isExporting}
                >
                  {isExporting ? 'Экспорт...' : 'Создать экспорт'}
                </Button>
              </DialogActions>
            </Dialog>

            {/* Deactivate Account Dialog */}
            <Dialog
              open={showDeactivateDialog}
              onClose={() => setShowDeactivateDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Деактивировать аккаунт</DialogTitle>
              <DialogContent>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Ваш профиль будет скрыт, но данные сохранятся. Вы сможете восстановить аккаунт в течение 30 дней.
                </Alert>

                <TextField
                  select
                  fullWidth
                  label="Причина деактивации"
                  value={deactivateForm.reason}
                  onChange={(e) => setDeactivateForm({ ...deactivateForm, reason: e.target.value })}
                  sx={{ mb: 2 }}
                >
                  {deactivationReasons.map((reason) => (
                    <MenuItem key={reason} value={reason}>
                      {reason}
                    </MenuItem>
                  ))}
                </TextField>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Дополнительные комментарии (необязательно)"
                  value={deactivateForm.feedback}
                  onChange={(e) => setDeactivateForm({ ...deactivateForm, feedback: e.target.value })}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  type="password"
                  label="Подтвердите паролем"
                  value={deactivateForm.password}
                  onChange={(e) => setDeactivateForm({ ...deactivateForm, password: e.target.value })}
                />
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowDeactivateDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleDeactivateAccount}
                  variant="contained"
                  color="warning"
                  disabled={!deactivateForm.reason || !deactivateForm.password}
                >
                  Деактивировать
                </Button>
              </DialogActions>
            </Dialog>

            {/* Delete Account Dialog */}
            <Dialog
              open={showDeleteDialog}
              onClose={() => setShowDeleteDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Удалить аккаунт</DialogTitle>
              <DialogContent>
                <Alert severity="error" sx={{ mb: 2 }}>
                  <strong>Внимание!</strong> Это действие приведет к безвозвратному удалению всех ваших данных через 30 дней.
                </Alert>

                <TextField
                  select
                  fullWidth
                  label="Причина удаления"
                  value={deleteForm.reason}
                  onChange={(e) => setDeleteForm({ ...deleteForm, reason: e.target.value })}
                  sx={{ mb: 2 }}
                >
                  {deletionReasons.map((reason) => (
                    <MenuItem key={reason} value={reason}>
                      {reason}
                    </MenuItem>
                  ))}
                </TextField>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Что можно улучшить? (необязательно)"
                  value={deleteForm.feedback}
                  onChange={(e) => setDeleteForm({ ...deleteForm, feedback: e.target.value })}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  type="password"
                  label="Подтвердите паролем"
                  value={deleteForm.password}
                  onChange={(e) => setDeleteForm({ ...deleteForm, password: e.target.value })}
                  sx={{ mb: 2 }}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={deleteForm.confirmDeletion}
                      onChange={(e) => setDeleteForm({ ...deleteForm, confirmDeletion: e.target.checked })}
                    />
                  }
                  label="Я понимаю, что это действие необратимо"
                />
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowDeleteDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleDeleteAccount}
                  variant="contained"
                  color="error"
                  disabled={!deleteForm.reason || !deleteForm.password || !deleteForm.confirmDeletion}
                >
                  Удалить аккаунт
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default DataManagementPage;
