import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Star as StarIcon,
  Diamond as DiamondIcon,
  Receipt as ReceiptIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Home as HomeIcon,
  Favorite as FavoriteIcon,
  Visibility as VisibilityIcon,
  Chat as ChatIcon,
  VideoCall as VideoCallIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  Support as SupportIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useSubscription } from '../../../src/contexts/SubscriptionContext';
import { formatCurrency } from '../../../src/services/subscriptionService';

const PaymentSuccessPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { currentSubscription, loadCurrentSubscription } = useSubscription();
  
  const { 
    transactionId, 
    planId, 
    amount, 
    currency = 'RUB',
    billingCycle 
  } = router.query;

  const [loading, setLoading] = useState(true);
  const [confettiVisible, setConfettiVisible] = useState(true);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // Load updated subscription data
    const loadData = async () => {
      await loadCurrentSubscription();
      setLoading(false);
    };

    loadData();

    // Hide confetti after animation
    const timer = setTimeout(() => {
      setConfettiVisible(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [user, router]);

  const handleDownloadReceipt = () => {
    // In real implementation, this would download the receipt
    window.open(`/api/receipts/${transactionId}`, '_blank');
  };

  const handleShareSuccess = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Likes & Love Premium',
        text: 'Я получил Premium подписку в Likes & Love!',
        url: window.location.origin
      });
    }
  };

  const getFeaturesByPlan = (planName: string) => {
    const commonFeatures = [
      { name: 'Безлимитные лайки', icon: <FavoriteIcon color="error" /> },
      { name: 'Супер-лайки', icon: <StarIcon sx={{ color: '#ffc107' }} /> },
      { name: 'Буст профиля', icon: <TrendingUpIcon color="warning" /> },
      { name: 'Режим инкогнито', icon: <VisibilityIcon color="action" /> },
      { name: 'Уведомления о прочтении', icon: <CheckIcon color="success" /> },
      { name: 'Приоритетная поддержка', icon: <SupportIcon color="primary" /> }
    ];

    const premiumFeatures = [
      ...commonFeatures,
      { name: 'Видеозвонки', icon: <VideoCallIcon color="secondary" /> },
      { name: 'Расширенные фильтры', icon: <SecurityIcon color="info" /> }
    ];

    const vipFeatures = [
      ...premiumFeatures,
      { name: 'Глобальный паспорт', icon: <DiamondIcon color="primary" /> },
      { name: 'Персональный менеджер', icon: <VerifiedIcon color="success" /> },
      { name: 'Эксклюзивные функции', icon: <StarIcon sx={{ color: '#9c27b0' }} /> }
    ];

    switch (planName?.toLowerCase()) {
      case 'vip':
        return vipFeatures;
      case 'premium':
        return premiumFeatures;
      default:
        return commonFeatures;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Оплата прошла успешно - Likes & Love</title>
        <meta 
          name="description" 
          content="Спасибо за оформление подписки в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        {/* Confetti Effect */}
        {confettiVisible && (
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              pointerEvents: 'none',
              zIndex: 9999,
              background: `
                radial-gradient(circle at 20% 80%, #ffc107 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #e91e63 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #2196f3 0%, transparent 50%)
              `,
              opacity: 0.1,
              animation: 'confetti 3s ease-out'
            }}
          />
        )}

        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Обработка платежа...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={800}>
                <Box>
                  {/* Success Header */}
                  <Box sx={{ textAlign: 'center', mb: 4 }}>
                    <Zoom in timeout={600}>
                      <CheckIcon 
                        sx={{ 
                          fontSize: 120, 
                          color: 'success.main',
                          mb: 2,
                          filter: 'drop-shadow(0 4px 8px rgba(76, 175, 80, 0.3))'
                        }} 
                      />
                    </Zoom>
                    
                    <Typography variant="h3" gutterBottom fontWeight="bold" color="success.main">
                      Поздравляем!
                    </Typography>
                    
                    <Typography variant="h5" gutterBottom>
                      Ваша подписка успешно оформлена
                    </Typography>
                    
                    <Typography variant="body1" color="text.secondary">
                      Теперь у вас есть доступ ко всем Premium функциям
                    </Typography>
                  </Box>

                  {/* Payment Details */}
                  <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      <ReceiptIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Детали платежа
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          Номер транзакции
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {transactionId || 'TXN-' + Date.now()}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          Сумма
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {amount ? formatCurrency(Number(amount), currency as string) : 'N/A'}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          План подписки
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {currentSubscription?.plan.displayName || 'Premium'}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          Период
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {billingCycle === 'yearly' ? 'Годовая' : 'Месячная'}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">
                          Дата и время
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {new Date().toLocaleString('ru-RU')}
                        </Typography>
                      </Grid>
                    </Grid>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={handleDownloadReceipt}
                      >
                        Скачать чек
                      </Button>
                      
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<ShareIcon />}
                        onClick={handleShareSuccess}
                      >
                        Поделиться
                      </Button>
                    </Box>
                  </Paper>

                  {/* Subscription Benefits */}
                  <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      <DiamondIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Ваши новые возможности
                    </Typography>
                    
                    <Grid container spacing={2}>
                      {getFeaturesByPlan(currentSubscription?.plan.name).map((feature, index) => (
                        <Grid item xs={12} sm={6} key={index}>
                          <Zoom in timeout={800 + index * 100}>
                            <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                {feature.icon}
                                <Typography variant="body1">
                                  {feature.name}
                                </Typography>
                              </Box>
                            </Card>
                          </Zoom>
                        </Grid>
                      ))}
                    </Grid>
                  </Paper>

                  {/* Next Steps */}
                  <Paper 
                    elevation={3} 
                    sx={{ 
                      p: 3, 
                      mb: 4,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white'
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Что дальше?
                    </Typography>
                    
                    <List>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <CheckIcon sx={{ color: 'white' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Обновите свой профиль"
                          secondary="Добавьте больше фотографий и информации о себе"
                          secondaryTypographyProps={{ sx: { color: 'rgba(255,255,255,0.8)' } }}
                        />
                      </ListItem>
                      
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <CheckIcon sx={{ color: 'white' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Начните знакомиться"
                          secondary="Используйте новые функции для поиска идеальной пары"
                          secondaryTypographyProps={{ sx: { color: 'rgba(255,255,255,0.8)' } }}
                        />
                      </ListItem>
                      
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <CheckIcon sx={{ color: 'white' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Изучите Premium функции"
                          secondary="Откройте все возможности вашей подписки"
                          secondaryTypographyProps={{ sx: { color: 'rgba(255,255,255,0.8)' } }}
                        />
                      </ListItem>
                    </List>
                  </Paper>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      size="large"
                      startIcon={<HomeIcon />}
                      onClick={() => router.push('/')}
                      sx={{ minWidth: 200 }}
                    >
                      На главную
                    </Button>
                    
                    <Button
                      variant="outlined"
                      size="large"
                      startIcon={<DiamondIcon />}
                      onClick={() => router.push('/subscription/premium')}
                      sx={{ minWidth: 200 }}
                    >
                      Изучить функции
                    </Button>
                    
                    <Button
                      variant="outlined"
                      size="large"
                      startIcon={<StarIcon />}
                      onClick={() => router.push('/discover')}
                      sx={{ minWidth: 200 }}
                    >
                      Начать знакомства
                    </Button>
                  </Box>

                  {/* Thank You Message */}
                  <Box sx={{ textAlign: 'center', mt: 6, p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Спасибо за доверие!
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Мы рады, что вы выбрали Likes & Love для поиска своей второй половинки.
                      Желаем вам удачи в знакомствах и надеемся, что вы найдете свою любовь!
                    </Typography>
                  </Box>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>

      {/* Custom CSS for confetti animation */}
      <style jsx>{`
        @keyframes confetti {
          0% {
            opacity: 0.3;
            transform: translateY(-100vh) rotate(0deg);
          }
          50% {
            opacity: 0.1;
            transform: translateY(0) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: translateY(100vh) rotate(360deg);
          }
        }
      `}</style>
    </>
  );
};

export default PaymentSuccessPage;
