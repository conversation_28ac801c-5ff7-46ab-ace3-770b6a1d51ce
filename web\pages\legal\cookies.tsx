import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  FormGroup
} from '@mui/material';
import {
  ArrowBack,
  ExpandMore as ExpandMoreIcon,
  <PERSON>ie as CookieIcon,
  Settings as SettingsIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Ads as AdsIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';

// Типы для cookie
interface CookieCategory {
  id: string;
  name: string;
  description: string;
  essential: boolean;
  enabled: boolean;
  cookies: CookieInfo[];
}

interface CookieInfo {
  name: string;
  purpose: string;
  duration: string;
  type: 'first-party' | 'third-party';
}

interface CookieSection {
  id: string;
  title: string;
  content: string[];
  important?: boolean;
}

const CookiesPolicyPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [expandedSection, setExpandedSection] = useState<string | false>('overview');
  const [cookieSettings, setCookieSettings] = useState<{ [key: string]: boolean }>({
    essential: true,
    functional: true,
    analytics: false,
    marketing: false
  });

  const lastUpdated = '15 декабря 2024 г.';

  // Категории cookies
  const cookieCategories: CookieCategory[] = [
    {
      id: 'essential',
      name: 'Необходимые cookies',
      description: 'Эти файлы cookie необходимы для работы сайта и не могут быть отключены.',
      essential: true,
      enabled: true,
      cookies: [
        {
          name: 'session_id',
          purpose: 'Поддержание сессии пользователя',
          duration: 'Сессия',
          type: 'first-party'
        },
        {
          name: 'csrf_token',
          purpose: 'Защита от CSRF-атак',
          duration: 'Сессия',
          type: 'first-party'
        },
        {
          name: 'auth_token',
          purpose: 'Аутентификация пользователя',
          duration: '30 дней',
          type: 'first-party'
        }
      ]
    },
    {
      id: 'functional',
      name: 'Функциональные cookies',
      description: 'Эти файлы cookie обеспечивают расширенную функциональность и персонализацию.',
      essential: false,
      enabled: cookieSettings.functional,
      cookies: [
        {
          name: 'user_preferences',
          purpose: 'Сохранение пользовательских настроек',
          duration: '1 год',
          type: 'first-party'
        },
        {
          name: 'language',
          purpose: 'Запоминание выбранного языка',
          duration: '1 год',
          type: 'first-party'
        },
        {
          name: 'theme',
          purpose: 'Сохранение темы оформления',
          duration: '1 год',
          type: 'first-party'
        }
      ]
    },
    {
      id: 'analytics',
      name: 'Аналитические cookies',
      description: 'Эти файлы cookie помогают нам понять, как посетители взаимодействуют с сайтом.',
      essential: false,
      enabled: cookieSettings.analytics,
      cookies: [
        {
          name: '_ga',
          purpose: 'Google Analytics - основной cookie',
          duration: '2 года',
          type: 'third-party'
        },
        {
          name: '_ga_*',
          purpose: 'Google Analytics - идентификатор свойства',
          duration: '2 года',
          type: 'third-party'
        },
        {
          name: 'yandex_metrika',
          purpose: 'Яндекс.Метрика - аналитика',
          duration: '1 год',
          type: 'third-party'
        }
      ]
    },
    {
      id: 'marketing',
      name: 'Маркетинговые cookies',
      description: 'Эти файлы cookie используются для показа релевантной рекламы.',
      essential: false,
      enabled: cookieSettings.marketing,
      cookies: [
        {
          name: 'facebook_pixel',
          purpose: 'Facebook Pixel для ретаргетинга',
          duration: '90 дней',
          type: 'third-party'
        },
        {
          name: 'google_ads',
          purpose: 'Google Ads для показа рекламы',
          duration: '90 дней',
          type: 'third-party'
        },
        {
          name: 'vk_pixel',
          purpose: 'VK Pixel для ретаргетинга',
          duration: '90 дней',
          type: 'third-party'
        }
      ]
    }
  ];

  // Разделы политики cookies
  const cookieSections: CookieSection[] = [
    {
      id: 'overview',
      title: '1. Что такое cookies',
      important: true,
      content: [
        'Файлы cookie - это небольшие текстовые файлы, которые сохраняются на вашем устройстве при посещении веб-сайтов.',
        'Они широко используются для обеспечения работы веб-сайтов или повышения эффективности их работы, а также для предоставления информации владельцам сайтов.',
        'Cookies могут быть "постоянными" (остаются на устройстве до истечения срока действия) или "сессионными" (удаляются при закрытии браузера).',
        'Мы используем как собственные cookies (first-party), так и cookies третьих сторон (third-party).'
      ]
    },
    {
      id: 'usage',
      title: '2. Как мы используем cookies',
      content: [
        'Мы используем файлы cookie для различных целей:',
        '• Обеспечения основных функций сайта (аутентификация, безопасность)',
        '• Запоминания ваших предпочтений и настроек',
        '• Анализа использования сайта и улучшения пользовательского опыта',
        '• Персонализации контента и рекламы',
        '• Обеспечения безопасности и предотвращения мошенничества'
      ]
    },
    {
      id: 'types',
      title: '3. Типы cookies',
      content: [
        'Мы используем следующие типы файлов cookie:'
      ]
    },
    {
      id: 'control',
      title: '4. Управление cookies',
      important: true,
      content: [
        'Вы можете управлять файлами cookie несколькими способами:',
        '• Через настройки на этой странице',
        '• Через настройки вашего браузера',
        '• Через настройки приложения',
        '• Используя инструменты отказа от рекламных cookies'
      ]
    },
    {
      id: 'browser_settings',
      title: '5. Настройки браузера',
      content: [
        'Большинство браузеров позволяют управлять файлами cookie через настройки:',
        '• Chrome: Настройки > Конфиденциальность и безопасность > Файлы cookie',
        '• Firefox: Настройки > Приватность и защита > Файлы cookie',
        '• Safari: Настройки > Конфиденциальность > Файлы cookie',
        '• Edge: Настройки > Файлы cookie и разрешения сайтов',
        'Обратите внимание: отключение cookies может ограничить функциональность сайта.'
      ]
    },
    {
      id: 'third_party',
      title: '6. Cookies третьих сторон',
      content: [
        'Мы используем сервисы третьих сторон, которые могут устанавливать свои cookies:',
        '• Google Analytics - для анализа трафика',
        '• Яндекс.Метрика - для веб-аналитики',
        '• Facebook Pixel - для рекламы и аналитики',
        '• Google Ads - для показа релевантной рекламы',
        'Эти сервисы имеют собственные политики конфиденциальности.'
      ]
    },
    {
      id: 'updates',
      title: '7. Обновления политики',
      content: [
        'Мы можем обновлять данную Политику cookies время от времени.',
        'О существенных изменениях мы уведомим вас через приложение или веб-сайт.',
        'Рекомендуем периодически просматривать эту страницу.',
        'Дата последнего обновления указана в начале документа.'
      ]
    }
  ];

  const handleSectionExpand = (sectionId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? sectionId : false);
  };

  const handleCookieSettingChange = (category: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setCookieSettings(prev => ({
      ...prev,
      [category]: event.target.checked
    }));
  };

  const handleSaveSettings = () => {
    // Здесь будет логика сохранения настроек cookies
    console.log('Сохранение настроек cookies:', cookieSettings);
    // Можно показать уведомление об успешном сохранении
  };

  const handleAcceptAll = () => {
    setCookieSettings({
      essential: true,
      functional: true,
      analytics: true,
      marketing: true
    });
    handleSaveSettings();
  };

  const handleRejectAll = () => {
    setCookieSettings({
      essential: true,
      functional: false,
      analytics: false,
      marketing: false
    });
    handleSaveSettings();
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Здесь будет логика скачивания PDF версии
    console.log('Скачивание PDF версии политики cookies');
  };

  const handleBack = () => {
    router.back();
  };

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'essential': return <SecurityIcon />;
      case 'functional': return <SettingsIcon />;
      case 'analytics': return <AnalyticsIcon />;
      case 'marketing': return <AdsIcon />;
      default: return <CookieIcon />;
    }
  };

  return (
    <>
      <Head>
        <title>Политика cookies - Likes & Love</title>
        <meta name="description" content="Политика использования файлов cookie в приложении знакомств Likes & Love" />
        <meta name="keywords" content="cookies, файлы cookie, отслеживание, приватность, Likes & Love" />
      </Head>

      <Layout title="Политика cookies">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Политика cookies
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Как мы используем файлы cookie
              </Typography>
            </Box>

            {/* Информация о документе */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Последнее обновление: {lastUpdated}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PrintIcon />}
                      onClick={handlePrint}
                    >
                      Печать
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={handleDownload}
                    >
                      PDF
                    </Button>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Alert severity="info">
                  <Typography variant="body2">
                    Эта политика объясняет, как мы используем файлы cookie и аналогичные технологии. 
                    Вы можете управлять своими предпочтениями в разделе настроек ниже.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>

            {/* Настройки cookies */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Настройки cookies
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Управляйте тем, какие типы файлов cookie мы можем использовать
                </Typography>

                <FormGroup>
                  {cookieCategories.map((category) => (
                    <Box key={category.id} sx={{ mb: 3, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getCategoryIcon(category.id)}
                          <Typography variant="subtitle1" fontWeight="bold">
                            {category.name}
                          </Typography>
                          {category.essential && (
                            <Chip label="Обязательные" size="small" color="error" />
                          )}
                        </Box>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={category.enabled}
                              onChange={handleCookieSettingChange(category.id)}
                              disabled={category.essential}
                            />
                          }
                          label=""
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {category.description}
                      </Typography>
                      
                      {/* Таблица cookies */}
                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Название</TableCell>
                              <TableCell>Назначение</TableCell>
                              <TableCell>Срок действия</TableCell>
                              <TableCell>Тип</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {category.cookies.map((cookie, index) => (
                              <TableRow key={index}>
                                <TableCell sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                                  {cookie.name}
                                </TableCell>
                                <TableCell>{cookie.purpose}</TableCell>
                                <TableCell>{cookie.duration}</TableCell>
                                <TableCell>
                                  <Chip 
                                    label={cookie.type === 'first-party' ? '1st party' : '3rd party'}
                                    size="small"
                                    color={cookie.type === 'first-party' ? 'primary' : 'default'}
                                  />
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  ))}
                </FormGroup>

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button variant="contained" onClick={handleSaveSettings}>
                    Сохранить настройки
                  </Button>
                  <Button variant="outlined" onClick={handleAcceptAll}>
                    Принять все
                  </Button>
                  <Button variant="outlined" onClick={handleRejectAll}>
                    Отклонить все
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Разделы политики */}
            {cookieSections.map((section) => (
              <Accordion
                key={section.id}
                expanded={expandedSection === section.id}
                onChange={handleSectionExpand(section.id)}
                sx={{ mb: 2 }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`${section.id}-content`}
                  id={`${section.id}-header`}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <CookieIcon color="primary" />
                    <Typography variant="h6" fontWeight="bold" sx={{ flexGrow: 1 }}>
                      {section.title}
                    </Typography>
                    {section.important && (
                      <Chip 
                        label="Важно" 
                        color="warning" 
                        size="small"
                        icon={<WarningIcon />}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ pl: 5 }}>
                    {section.content.map((paragraph, index) => (
                      <Typography 
                        key={index} 
                        variant="body2" 
                        sx={{ 
                          mb: 1.5,
                          lineHeight: 1.6,
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {paragraph}
                      </Typography>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}

            {/* Дополнительные ресурсы */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Дополнительная информация
                </Typography>
                <Typography variant="body2" sx={{ mb: 3 }}>
                  Для получения дополнительной информации о защите данных и конфиденциальности:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/legal/privacy')}
                  >
                    Политика конфиденциальности
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/legal/terms')}
                  >
                    Условия использования
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/settings/privacy')}
                  >
                    Настройки приватности
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/help/contact')}
                  >
                    Связаться с нами
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Согласие */}
            <Alert severity="success" sx={{ mt: 4 }}>
              <Typography variant="body2">
                <strong>Ваше согласие:</strong> Продолжая использовать наш сайт, вы соглашаетесь на использование 
                файлов cookie в соответствии с данной политикой. Вы можете изменить свои настройки в любое время.
              </Typography>
            </Alert>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CookiesPolicyPage;
