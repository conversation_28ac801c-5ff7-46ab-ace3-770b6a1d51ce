import axios from 'axios';
import { config } from '../config';
import {
  Meeting,
  MeetingRequest,
  MeetingInvitation,
  MeetingNotification,
  MeetingStatistics,
  MeetingSearchResult,
  MeetingTemplate,
  MeetingSuggestion,
  MeetingChat,
  MeetingReminder,
  CreateMeetingRequest,
  UpdateMeetingRequest,
  MeetingFilters,
  PaginatedResponse
} from '../types/meetings.types';

const meetingsApi = axios.create({
  baseURL: `${config.api.baseUrl}/meetings`,
  withCredentials: true,
});

// Meetings CRUD
export const getMeetings = async (
  filters?: MeetingFilters,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Meeting>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await meetingsApi.get(`/?${params.toString()}`);
  return data;
};

export const getMeeting = async (id: string): Promise<Meeting> => {
  const { data } = await meetingsApi.get(`/${id}`);
  return data.meeting;
};

export const createMeeting = async (request: CreateMeetingRequest): Promise<Meeting> => {
  const formData = new FormData();
  
  // Add basic fields
  Object.entries(request).forEach(([key, value]) => {
    if (key === 'photos' && value) {
      (value as File[]).forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });
    } else if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    }
  });

  const { data } = await meetingsApi.post('/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.meeting;
};

export const updateMeeting = async (id: string, request: UpdateMeetingRequest): Promise<Meeting> => {
  const { data } = await meetingsApi.put(`/${id}`, request);
  return data.meeting;
};

export const deleteMeeting = async (id: string): Promise<void> => {
  await meetingsApi.delete(`/${id}`);
};

export const cancelMeeting = async (id: string, reason?: string): Promise<void> => {
  await meetingsApi.post(`/${id}/cancel`, { reason });
};

// Meeting participation
export const joinMeeting = async (id: string, message?: string): Promise<void> => {
  await meetingsApi.post(`/${id}/join`, { message });
};

export const leaveMeeting = async (id: string): Promise<void> => {
  await meetingsApi.post(`/${id}/leave`);
};

export const updateParticipantStatus = async (
  meetingId: string,
  status: 'confirmed' | 'maybe' | 'declined'
): Promise<void> => {
  await meetingsApi.put(`/${meetingId}/status`, { status });
};

// Meeting requests
export const getMeetingRequests = async (
  type: 'sent' | 'received' = 'received',
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<MeetingRequest>> => {
  const { data } = await meetingsApi.get(`/requests?type=${type}&page=${page}&limit=${limit}`);
  return data;
};

export const respondToMeetingRequest = async (
  requestId: string,
  status: 'approved' | 'declined',
  message?: string
): Promise<void> => {
  await meetingsApi.put(`/requests/${requestId}`, { status, message });
};

export const cancelMeetingRequest = async (requestId: string): Promise<void> => {
  await meetingsApi.delete(`/requests/${requestId}`);
};

// Meeting invitations
export const getMeetingInvitations = async (
  type: 'sent' | 'received' = 'received',
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<MeetingInvitation>> => {
  const { data } = await meetingsApi.get(`/invitations?type=${type}&page=${page}&limit=${limit}`);
  return data;
};

export const inviteToMeeting = async (
  meetingId: string,
  userIds: string[],
  message?: string
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/invite`, { userIds, message });
};

export const respondToInvitation = async (
  invitationId: string,
  status: 'accepted' | 'declined',
  message?: string
): Promise<void> => {
  await meetingsApi.put(`/invitations/${invitationId}`, { status, message });
};

// My meetings
export const getMyMeetings = async (
  type: 'organized' | 'participating' | 'all' = 'all',
  status?: Meeting['status'],
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Meeting>> => {
  const params = new URLSearchParams({
    type,
    page: page.toString(),
    limit: limit.toString()
  });

  if (status) {
    params.append('status', status);
  }

  const { data } = await meetingsApi.get(`/my?${params.toString()}`);
  return data;
};

export const getUpcomingMeetings = async (limit: number = 10): Promise<Meeting[]> => {
  const { data } = await meetingsApi.get(`/upcoming?limit=${limit}`);
  return data.meetings;
};

// Search and discovery
export const searchMeetings = async (
  query: string,
  filters?: MeetingFilters,
  page: number = 1,
  limit: number = 20
): Promise<MeetingSearchResult> => {
  const params = new URLSearchParams({
    query,
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await meetingsApi.get(`/search?${params.toString()}`);
  return data;
};

export const getMeetingSuggestions = async (limit: number = 10): Promise<MeetingSuggestion[]> => {
  const { data } = await meetingsApi.get(`/suggestions?limit=${limit}`);
  return data.suggestions;
};

export const getTrendingMeetings = async (
  timeframe: 'day' | 'week' | 'month' = 'week',
  limit: number = 20
): Promise<Meeting[]> => {
  const { data } = await meetingsApi.get(`/trending?timeframe=${timeframe}&limit=${limit}`);
  return data.meetings;
};

export const getNearbyMeetings = async (
  latitude: number,
  longitude: number,
  radius: number = 25,
  limit: number = 20
): Promise<Meeting[]> => {
  const { data } = await meetingsApi.get(
    `/nearby?lat=${latitude}&lng=${longitude}&radius=${radius}&limit=${limit}`
  );
  return data.meetings;
};

// Meeting templates
export const getMeetingTemplates = async (): Promise<MeetingTemplate[]> => {
  const { data } = await meetingsApi.get('/templates');
  return data.templates;
};

export const createMeetingFromTemplate = async (
  templateId: string,
  customizations: Partial<CreateMeetingRequest>
): Promise<Meeting> => {
  const { data } = await meetingsApi.post(`/templates/${templateId}/create`, customizations);
  return data.meeting;
};

// Meeting chat
export const getMeetingChat = async (meetingId: string): Promise<MeetingChat> => {
  const { data } = await meetingsApi.get(`/${meetingId}/chat`);
  return data.chat;
};

export const sendMeetingChatMessage = async (
  meetingId: string,
  message: string
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/chat/messages`, { message });
};

// Notifications
export const getMeetingNotifications = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<MeetingNotification>> => {
  const { data } = await meetingsApi.get(`/notifications?page=${page}&limit=${limit}`);
  return data;
};

export const markNotificationAsRead = async (notificationId: string): Promise<void> => {
  await meetingsApi.put(`/notifications/${notificationId}/read`);
};

export const markAllNotificationsAsRead = async (): Promise<void> => {
  await meetingsApi.put('/notifications/read-all');
};

// Reminders
export const getMeetingReminders = async (meetingId: string): Promise<MeetingReminder[]> => {
  const { data } = await meetingsApi.get(`/${meetingId}/reminders`);
  return data.reminders;
};

export const createMeetingReminder = async (
  meetingId: string,
  type: 'push' | 'email' | 'sms',
  minutesBefore: number
): Promise<MeetingReminder> => {
  const { data } = await meetingsApi.post(`/${meetingId}/reminders`, {
    type,
    minutesBefore
  });
  return data.reminder;
};

export const deleteMeetingReminder = async (reminderId: string): Promise<void> => {
  await meetingsApi.delete(`/reminders/${reminderId}`);
};

// Feedback and rating
export const rateMeeting = async (
  meetingId: string,
  rating: number,
  comment?: string
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/rate`, { rating, comment });
};

export const getMeetingReviews = async (
  meetingId: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponse<any>> => {
  const { data } = await meetingsApi.get(`/${meetingId}/reviews?page=${page}&limit=${limit}`);
  return data;
};

// Reporting
export const reportMeeting = async (
  meetingId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/report`, { reason, details });
};

export const reportMeetingParticipant = async (
  meetingId: string,
  participantId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/participants/${participantId}/report`, {
    reason,
    details
  });
};

// Statistics
export const getMeetingStatistics = async (): Promise<MeetingStatistics> => {
  const { data } = await meetingsApi.get('/statistics');
  return data.statistics;
};

export const getMeetingAnalytics = async (meetingId: string): Promise<any> => {
  const { data } = await meetingsApi.get(`/${meetingId}/analytics`);
  return data.analytics;
};

// Categories and tags
export const getMeetingCategories = async (): Promise<string[]> => {
  const { data } = await meetingsApi.get('/categories');
  return data.categories;
};

export const getPopularTags = async (limit: number = 50): Promise<{ tag: string; count: number }[]> => {
  const { data } = await meetingsApi.get(`/tags/popular?limit=${limit}`);
  return data.tags;
};

// Location services
export const searchVenues = async (
  query: string,
  latitude?: number,
  longitude?: number,
  radius?: number
): Promise<any[]> => {
  const params = new URLSearchParams({ query });
  
  if (latitude && longitude) {
    params.append('lat', latitude.toString());
    params.append('lng', longitude.toString());
  }
  
  if (radius) {
    params.append('radius', radius.toString());
  }

  const { data } = await meetingsApi.get(`/venues/search?${params.toString()}`);
  return data.venues;
};

export const getVenueDetails = async (venueId: string): Promise<any> => {
  const { data } = await meetingsApi.get(`/venues/${venueId}`);
  return data.venue;
};

// Calendar integration
export const exportMeetingToCalendar = async (
  meetingId: string,
  format: 'ics' | 'google' | 'outlook'
): Promise<string> => {
  const { data } = await meetingsApi.get(`/${meetingId}/export/${format}`);
  return data.url || data.content;
};

export const syncWithCalendar = async (
  provider: 'google' | 'outlook' | 'apple',
  accessToken: string
): Promise<void> => {
  await meetingsApi.post('/calendar/sync', { provider, accessToken });
};

// Bulk operations
export const bulkUpdateMeetings = async (
  meetingIds: string[],
  updates: Partial<UpdateMeetingRequest>
): Promise<void> => {
  await meetingsApi.put('/bulk-update', { meetingIds, updates });
};

export const bulkDeleteMeetings = async (meetingIds: string[]): Promise<void> => {
  await meetingsApi.delete('/bulk-delete', { data: { meetingIds } });
};

// Meeting check-in
export const checkInToMeeting = async (
  meetingId: string,
  checkInCode?: string,
  location?: { latitude: number; longitude: number }
): Promise<void> => {
  await meetingsApi.post(`/${meetingId}/check-in`, { checkInCode, location });
};

export const generateCheckInCode = async (meetingId: string): Promise<string> => {
  const { data } = await meetingsApi.post(`/${meetingId}/check-in/generate`);
  return data.checkInCode;
};

// Meeting sharing
export const shareMeeting = async (
  meetingId: string,
  platform: 'facebook' | 'twitter' | 'linkedin' | 'whatsapp' | 'telegram' | 'copy'
): Promise<string> => {
  const { data } = await meetingsApi.post(`/${meetingId}/share`, { platform });
  return data.shareUrl;
};

export const getMeetingShareStats = async (meetingId: string): Promise<any> => {
  const { data } = await meetingsApi.get(`/${meetingId}/share/stats`);
  return data.stats;
};
