import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Favorite as LikeIcon,
  Close as PassIcon,
  Star as SuperLikeIcon,
  Refresh as RefreshIcon,
  Tune as FilterIcon,
  LocationOn as LocationIcon,
  Cake as AgeIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Verified as VerifiedIcon,
  Undo as UndoIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import {
  getDiscoverFeed,
  swipeUser,
  undoLastSwipe,
  getSuperLikesCount,
  refreshDiscoverFeed
} from '../../src/services/discoverService';
import {
  DiscoverUser,
  SwipeAction,
  SwipeResult
} from '../../src/types/discover.types';

const DiscoverPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [users, setUsers] = useState<DiscoverUser[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [swiping, setSwiping] = useState(false);
  const [superLikesCount, setSuperLikesCount] = useState(0);
  const [matchDialog, setMatchDialog] = useState<{
    open: boolean;
    match?: SwipeResult['match'];
  }>({ open: false });
  const [superLikeDialog, setSuperLikeDialog] = useState(false);
  const [superLikeMessage, setSuperLikeMessage] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadUsers();
    loadSuperLikesCount();
  }, [user, router]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getDiscoverFeed();
      setUsers(response.data);
      setCurrentUserIndex(0);
    } catch (err: any) {
      setError('Ошибка загрузки пользователей');
    } finally {
      setLoading(false);
    }
  };

  const loadSuperLikesCount = async () => {
    try {
      const count = await getSuperLikesCount();
      setSuperLikesCount(count.available);
    } catch (err: any) {
      // Не показываем ошибку для счетчика
    }
  };

  const handleSwipe = async (action: 'like' | 'pass' | 'super_like', message?: string) => {
    const currentUser = users[currentUserIndex];
    if (!currentUser || swiping) return;

    try {
      setSwiping(true);
      setError(null);

      const swipeAction: SwipeAction = {
        userId: currentUser.id,
        action,
        timestamp: new Date().toISOString(),
        message
      };

      const result = await swipeUser(swipeAction);

      if (result.success) {
        if (result.isMatch && result.match) {
          setMatchDialog({ open: true, match: result.match });
        }

        if (action === 'super_like') {
          setSuperLikesCount(result.remainingSuperLikes || 0);
        }

        // Move to next user
        setCurrentUserIndex(prev => prev + 1);

        // Load more users if running low
        if (currentUserIndex >= users.length - 3) {
          loadMoreUsers();
        }
      } else {
        setError(result.error || 'Ошибка при свайпе');
      }
    } catch (err: any) {
      setError('Ошибка при свайпе');
    } finally {
      setSwiping(false);
    }
  };

  const loadMoreUsers = async () => {
    try {
      const response = await getDiscoverFeed();
      setUsers(prev => [...prev, ...response.data]);
    } catch (err: any) {
      // Не показываем ошибку для дозагрузки
    }
  };

  const handleUndo = async () => {
    try {
      const result = await undoLastSwipe();
      if (result.success) {
        setCurrentUserIndex(prev => Math.max(0, prev - 1));
        setSuccess(result.message);
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка отмены действия');
    }
  };

  const handleRefresh = async () => {
    try {
      setLoading(true);
      const newUsers = await refreshDiscoverFeed();
      setUsers(newUsers);
      setCurrentUserIndex(0);
    } catch (err: any) {
      setError('Ошибка обновления ленты');
    } finally {
      setLoading(false);
    }
  };

  const handleSuperLike = () => {
    if (superLikesCount <= 0) {
      setError('У вас закончились супер-лайки');
      return;
    }
    setSuperLikeDialog(true);
  };

  const handleSuperLikeConfirm = () => {
    handleSwipe('super_like', superLikeMessage.trim() || undefined);
    setSuperLikeDialog(false);
    setSuperLikeMessage('');
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const currentUser = users[currentUserIndex];
    }
  };

  const handleAction = async (action: 'like' | 'pass' | 'superlike') => {
    if (currentIndex >= cards.length) return;
    
    const currentCard = cards[currentIndex];
    
    if (action === 'like' && dailyLikesRemaining <= 0) {
      router.push('/subscription');
      return;
    }
    
    if (action === 'superlike' && superLikesRemaining <= 0) {
      router.push('/coins/purchase');
      return;
    }

    try {
      const response = await axios.post(`/api/discover/${currentCard.id}/${action}`);
      
      if (response.data.match) {
        // Показать уведомление о матче
        router.push(`/matches/${response.data.matchId}`);
      }
      
      if (action === 'like') {
        setDailyLikesRemaining(dailyLikesRemaining - 1);
      } else if (action === 'superlike') {
        setSuperLikesRemaining(superLikesRemaining - 1);
      }
      
      setUndoStack([...undoStack, { card: currentCard, action: action === 'superlike' ? 'like' : action }]);
      setCurrentIndex(currentIndex + 1);
      
      // Загрузить больше карточек если нужно
      if (currentIndex >= cards.length - 3) {
        loadMoreCards();
      }
    } catch (err) {
      setError('Ошибка отправки действия');
    }
  };

  const handleUndo = async () => {
    if (undoStack.length === 0 || currentIndex === 0) return;
    
    const lastAction = undoStack[undoStack.length - 1];
    
    try {
      await axios.post(`/api/discover/${lastAction.card.id}/undo`);
      
      if (lastAction.action === 'like') {
        setDailyLikesRemaining(dailyLikesRemaining + 1);
      }
      
      setUndoStack(undoStack.slice(0, -1));
      setCurrentIndex(currentIndex - 1);
    } catch (err) {
      setError('Ошибка отмены действия');
    }
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const offsetX = e.clientX - dragStart.x;
    const offsetY = e.clientY - dragStart.y;
    setDragOffset({ x: offsetX, y: offsetY });
  };

  const handleMouseUp = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    const threshold = 100;
    if (Math.abs(dragOffset.x) > threshold) {
      if (dragOffset.x > 0) {
        handleAction('like');
      } else {
        handleAction('pass');
      }
    }
    
    setDragOffset({ x: 0, y: 0 });
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <CircularProgress />
            <Typography sx={{ mt: 2 }}>Загрузка профилей...</Typography>
          </Box>
        </Container>
      </Layout>
    );
  }

  const currentCard = cards[currentIndex];

  return (
    <Layout title="Знакомства">
      <Container maxWidth="sm">
        <Box sx={{ mt: 2, mb: 2 }}>
          {/* Верхняя панель */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Chip
                icon={<Favorite />}
                label={`${dailyLikesRemaining} лайков`}
                color={dailyLikesRemaining > 0 ? 'primary' : 'default'}
              />
              <Chip
                icon={<Star />}
                label={`${superLikesRemaining} супер`}
                color={superLikesRemaining > 0 ? 'secondary' : 'default'}
              />
            </Box>
            
            <IconButton onClick={() => setFiltersOpen(true)}>
              <Badge badgeContent={Object.keys(filters).length} color="primary">
                <FilterList />
              </Badge>
            </IconButton>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {/* Карточки */}
          <Box sx={{ position: 'relative', height: '70vh' }}>
            <AnimatePresence>
              {currentCard ? (
                <motion.div
                  key={currentCard.id}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ 
                    scale: 1, 
                    opacity: 1,
                    x: dragOffset.x,
                    y: dragOffset.y,
                    rotate: dragOffset.x * 0.1
                  }}
                  exit={{ 
                    x: dragOffset.x > 0 ? 300 : -300, 
                    opacity: 0,
                    transition: { duration: 0.3 }
                  }}
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    cursor: isDragging ? 'grabbing' : 'grab'
                  }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                >
                  <Card 
                    ref={cardRef}
                    sx={{ 
                      height: '100%', 
                      position: 'relative',
                      overflow: 'hidden',
                      userSelect: 'none'
                    }}
                  >
                    <CardMedia
                      component="img"
                      height="100%"
                      image={currentCard.photos.find(p => p.isMain)?.url || currentCard.photos[0]?.url}
                      alt={currentCard.profile.firstName}
                      sx={{ objectFit: 'cover' }}
                    />
                    
                    {/* Индикаторы действий */}
                    {dragOffset.x > 50 && (
                      <Box sx={{
                        position: 'absolute',
                        top: 50,
                        left: 20,
                        transform: 'rotate(-30deg)'
                      }}>
                        <Typography variant="h3" sx={{ 
                          color: 'success.main',
                          fontWeight: 'bold',
                          textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
                        }}>
                          LIKE
                        </Typography>
                      </Box>
                    )}
                    
                    {dragOffset.x < -50 && (
                      <Box sx={{
                        position: 'absolute',
                        top: 50,
                        right: 20,
                        transform: 'rotate(30deg)'
                      }}>
                        <Typography variant="h3" sx={{ 
                          color: 'error.main',
                          fontWeight: 'bold',
                          textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
                        }}>
                          PASS
                        </Typography>
                      </Box>
                    )}
                    
                    {/* Информация о пользователе */}
                    <Box sx={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%)',
                      color: 'white',
                      p: 3
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="h5">
                          {currentCard.profile.firstName}, {calculateAge(currentCard.profile.birthDate)}
                        </Typography>
                        {currentCard.isVerified && (
                          <Chip
                            icon={<AutoAwesome />}
                            label="Проверен"
                            size="small"
                            color="primary"
                          />
                        )}
                      </Box>
                      
                      {currentCard.profile.location?.city && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                          <LocationOn fontSize="small" />
                          <Typography variant="body2">
                            {currentCard.profile.location.city}
                            {currentCard.profile.location.distance && 
                              ` • ${currentCard.profile.location.distance} км`}
                          </Typography>
                        </Box>
                      )}
                      
                      {currentCard.profile.bio && (
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {currentCard.profile.bio.length > 100 
                            ? `${currentCard.profile.bio.substring(0, 100)}...` 
                            : currentCard.profile.bio}
                        </Typography>
                      )}
                      
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {currentCard.profile.work && (
                          <Chip
                            icon={<Work fontSize="small" />}
                            label={currentCard.profile.work}
                            size="small"
                            sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                          />
                        )}
                        {currentCard.profile.education && (
                          <Chip
                            icon={<School fontSize="small" />}
                            label={currentCard.profile.education}
                            size="small"
                            sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                          />
                        )}
                        {currentCard.profile.height && (
                          <Chip
                            icon={<Height fontSize="small" />}
                            label={`${currentCard.profile.height} см`}
                            size="small"
                            sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                          />
                        )}
                      </Box>
                      
                      <IconButton
                        sx={{ 
                          position: 'absolute', 
                          bottom: 10, 
                          right: 10,
                          color: 'white'
                        }}
                        onClick={() => router.push(`/profile/${currentCard.id}`)}
                      >
                        <Info />
                      </IconButton>
                    </Box>
                  </Card>
                </motion.div>
              ) : (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center', 
                  justifyContent: 'center',
                  height: '100%'
                }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Профили закончились
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Refresh />}
                    onClick={loadCards}
                  >
                    Обновить
                  </Button>
                </Box>
              )}
            </AnimatePresence>
          </Box>

          {/* Кнопки действий */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: 3, 
            mt: 3 
          }}>
            <Tooltip title="Отменить">
              <span>
                <Fab
                  size="small"
                  onClick={handleUndo}
                  disabled={undoStack.length === 0}
                >
                  <Undo />
                </Fab>
              </span>
            </Tooltip>
            
            <Tooltip title="Пропустить">
              <Fab
                color="default"
                onClick={() => handleAction('pass')}
                disabled={!currentCard}
              >
                <Close />
              </Fab>
            </Tooltip>
            
            <Tooltip title="Супер лайк">
              <span>
                <Fab
                  size="small"
                  color="secondary"
                  onClick={() => handleAction('superlike')}
                  disabled={!currentCard || superLikesRemaining <= 0}
                >
                  <Star />
                </Fab>
              </span>
            </Tooltip>
            
            <Tooltip title="Лайк">
              <span>
                <Fab
                  color="primary"
                  onClick={() => handleAction('like')}
                  disabled={!currentCard || dailyLikesRemaining <= 0}
                >
                  <Favorite />
                </Fab>
              </span>
            </Tooltip>
          </Box>
        </Box>
      </Container>

      {/* Drawer с фильтрами */}
      <Drawer
        anchor="right"
        open={filtersOpen}
        onClose={() => setFiltersOpen(false)}
      >
        <Box sx={{ width: 300, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Фильтры поиска
          </Typography>
          
          <Divider sx={{ mb: 2 }} />
          
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel>Возраст</FormLabel>
            <Slider
              value={filters.ageRange}
              onChange={(e, value) => setFilters({ ...filters, ageRange: value as [number, number] })}
              valueLabelDisplay="auto"
              min={18}
              max={80}
              marks={[
                { value: 18, label: '18' },
                { value: 80, label: '80' }
              ]}
            />
          </FormControl>
          
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel>Расстояние (км)</FormLabel>
            <Slider
              value={filters.distance}
              onChange={(e, value) => setFilters({ ...filters, distance: value as number })}
              valueLabelDisplay="auto"
              min={1}
              max={200}
              marks={[
                { value: 1, label: '1' },
                { value: 200, label: '200' }
              ]}
            />
          </FormControl>
          
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel>Пол</FormLabel>
            <RadioGroup
              value={filters.gender}
              onChange={(e) => setFilters({ ...filters, gender: e.target.value })}
            >
              <FormControlLabel value="all" control={<Radio />} label="Все" />
              <FormControlLabel value="male" control={<Radio />} label="Мужчины" />
              <FormControlLabel value="female" control={<Radio />} label="Женщины" />
            </RadioGroup>
          </FormControl>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.hasPhoto}
                onChange={(e) => setFilters({ ...filters, hasPhoto: e.target.checked })}
              />
            }
            label="Только с фото"
            sx={{ mb: 1 }}
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={filters.isVerified}
                onChange={(e) => setFilters({ ...filters, isVerified: e.target.checked })}
              />
            }
            label="Только проверенные"
            sx={{ mb: 3 }}
          />
          
          <Button
            variant="contained"
            fullWidth
            onClick={() => {
              setFiltersOpen(false);
              loadCards();
            }}
          >
            Применить фильтры
          </Button>
        </Box>
      </Drawer>
    </Layout>
  );
};

export default DiscoverPage;
