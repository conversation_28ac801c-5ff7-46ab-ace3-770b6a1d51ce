import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import {
  UserSettings,
  SecurityEvent,
  PrivacyReport,
  SettingsContextType,
  UpdatePrivacySettingsRequest,
  UpdateNotificationSettingsRequest,
  UpdatePreferencesRequest,
  UpdateSecuritySettingsRequest,
  Enable2FARequest,
  Verify2FARequest,
  ChangePasswordRequest,
  ChangeEmailRequest,
  ChangePhoneRequest,
  DeactivateAccountRequest,
  DeleteAccountRequest,
  BlockUserRequest,
  ReportUserRequest,
  ExportDataRequest,
  DataExportRequest
} from '../types/settings.types';
import * as settingsService from '../services/settingsService';

// State interface
interface SettingsState {
  settings: UserSettings | null;
  securityEvents: SecurityEvent[];
  privacyReport: PrivacyReport | null;
  loading: boolean;
  error: string | null;
}

// Action types
type SettingsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SETTINGS'; payload: UserSettings | null }
  | { type: 'SET_SECURITY_EVENTS'; payload: SecurityEvent[] }
  | { type: 'SET_PRIVACY_REPORT'; payload: PrivacyReport | null }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<UserSettings> }
  | { type: 'ADD_SECURITY_EVENT'; payload: SecurityEvent }
  | { type: 'ACKNOWLEDGE_SECURITY_EVENT'; payload: string }
  | { type: 'CLEAR_SECURITY_EVENTS' };

// Initial state
const initialState: SettingsState = {
  settings: null,
  securityEvents: [],
  privacyReport: null,
  loading: false,
  error: null,
};

// Reducer
const settingsReducer = (state: SettingsState, action: SettingsAction): SettingsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    
    case 'SET_SECURITY_EVENTS':
      return { ...state, securityEvents: action.payload };
    
    case 'SET_PRIVACY_REPORT':
      return { ...state, privacyReport: action.payload };
    
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: state.settings ? { ...state.settings, ...action.payload } : null
      };
    
    case 'ADD_SECURITY_EVENT':
      return {
        ...state,
        securityEvents: [action.payload, ...state.securityEvents]
      };
    
    case 'ACKNOWLEDGE_SECURITY_EVENT':
      return {
        ...state,
        securityEvents: state.securityEvents.map(event =>
          event.id === action.payload ? { ...event, acknowledged: true } : event
        )
      };
    
    case 'CLEAR_SECURITY_EVENTS':
      return { ...state, securityEvents: [] };
    
    default:
      return state;
  }
};

// Create context
const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

// Provider component
interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(settingsReducer, initialState);

  // Helper function to handle async operations
  const handleAsync = async <T,>(
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    showLoading: boolean = true
  ): Promise<T | null> => {
    try {
      if (showLoading) {
        dispatch({ type: 'SET_LOADING', payload: true });
      }
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const result = await operation();
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return null;
    } finally {
      if (showLoading) {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  };

  // Load settings
  const loadSettings = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.getUserSettings(),
      (settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })
    );
  };

  // Load security events
  const loadSecurityEvents = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.getSecurityEvents(),
      (response) => dispatch({ type: 'SET_SECURITY_EVENTS', payload: response.data })
    );
  };

  // Load privacy report
  const loadPrivacyReport = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.getPrivacyReport(),
      (report) => dispatch({ type: 'SET_PRIVACY_REPORT', payload: report })
    );
  };

  // Update privacy settings
  const updatePrivacySettings = async (request: UpdatePrivacySettingsRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.updatePrivacySettings(request),
      (settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })
    );
  };

  // Update notification settings
  const updateNotificationSettings = async (request: UpdateNotificationSettingsRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.updateNotificationSettings(request),
      (settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })
    );
  };

  // Update preferences
  const updatePreferences = async (request: UpdatePreferencesRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.updatePreferences(request),
      (settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })
    );
  };

  // Update security settings
  const updateSecuritySettings = async (request: UpdateSecuritySettingsRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.updateSecuritySettings(request),
      (settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })
    );
  };

  // Enable 2FA
  const enable2FA = async (request: Enable2FARequest): Promise<{ qrCode?: string; backupCodes: string[] }> => {
    const result = await handleAsync(
      () => settingsService.enable2FA(request),
      () => {
        // Reload settings to get updated 2FA status
        loadSettings();
      }
    );
    return result!;
  };

  // Verify 2FA
  const verify2FA = async (request: Verify2FARequest): Promise<void> => {
    await handleAsync(
      () => settingsService.verify2FA(request),
      () => {
        // Reload settings to get updated 2FA status
        loadSettings();
      }
    );
  };

  // Disable 2FA
  const disable2FA = async (password: string): Promise<void> => {
    await handleAsync(
      () => settingsService.disable2FA(password),
      () => {
        // Reload settings to get updated 2FA status
        loadSettings();
      }
    );
  };

  // Change password
  const changePassword = async (request: ChangePasswordRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.changePassword(request)
    );
  };

  // Change email
  const changeEmail = async (request: ChangeEmailRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.changeEmail(request)
    );
  };

  // Change phone
  const changePhone = async (request: ChangePhoneRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.changePhone(request)
    );
  };

  // Add trusted device
  const addTrustedDevice = async (deviceName: string): Promise<void> => {
    await handleAsync(
      () => settingsService.addTrustedDevice(deviceName),
      () => {
        // Reload settings to get updated trusted devices
        loadSettings();
      }
    );
  };

  // Remove trusted device
  const removeTrustedDevice = async (deviceId: string): Promise<void> => {
    await handleAsync(
      () => settingsService.removeTrustedDevice(deviceId),
      () => {
        // Reload settings to get updated trusted devices
        loadSettings();
      }
    );
  };

  // Terminate session
  const terminateSession = async (sessionId: string): Promise<void> => {
    await handleAsync(
      () => settingsService.terminateSession(sessionId)
    );
  };

  // Terminate all sessions
  const terminateAllSessions = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.terminateAllSessions()
    );
  };

  // Block user
  const blockUser = async (request: BlockUserRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.blockUser(request),
      () => {
        // Reload settings to get updated blocked users list
        loadSettings();
      }
    );
  };

  // Unblock user
  const unblockUser = async (userId: string): Promise<void> => {
    await handleAsync(
      () => settingsService.unblockUser(userId),
      () => {
        // Reload settings to get updated blocked users list
        loadSettings();
      }
    );
  };

  // Report user
  const reportUser = async (request: ReportUserRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.reportUser(request)
    );
  };

  // Export data
  const exportData = async (request: ExportDataRequest): Promise<DataExportRequest> => {
    const result = await handleAsync(
      () => settingsService.exportData(request)
    );
    return result!;
  };

  // Deactivate account
  const deactivateAccount = async (request: DeactivateAccountRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.deactivateAccount(request),
      () => {
        // Reload settings to get updated account status
        loadSettings();
      }
    );
  };

  // Reactivate account
  const reactivateAccount = async (password: string): Promise<void> => {
    await handleAsync(
      () => settingsService.reactivateAccount(password),
      () => {
        // Reload settings to get updated account status
        loadSettings();
      }
    );
  };

  // Delete account
  const deleteAccount = async (request: DeleteAccountRequest): Promise<void> => {
    await handleAsync(
      () => settingsService.deleteAccount(request),
      () => {
        // Reload settings to get updated account status
        loadSettings();
      }
    );
  };

  // Cancel account deletion
  const cancelAccountDeletion = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.cancelAccountDeletion(),
      () => {
        // Reload settings to get updated account status
        loadSettings();
      }
    );
  };

  // Acknowledge security event
  const acknowledgeSecurityEvent = async (eventId: string): Promise<void> => {
    await handleAsync(
      () => settingsService.acknowledgeSecurityEvent(eventId),
      () => dispatch({ type: 'ACKNOWLEDGE_SECURITY_EVENT', payload: eventId }),
      false // Don't show loading for this quick action
    );
  };

  // Clear security events
  const clearSecurityEvents = async (): Promise<void> => {
    await handleAsync(
      () => settingsService.clearSecurityEvents(),
      () => dispatch({ type: 'CLEAR_SECURITY_EVENTS' })
    );
  };

  // Load initial data on mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([
        loadSettings(),
        loadSecurityEvents(),
        loadPrivacyReport()
      ]);
    };

    loadInitialData();
  }, []);

  const contextValue: SettingsContextType = {
    // State
    settings: state.settings,
    securityEvents: state.securityEvents,
    privacyReport: state.privacyReport,
    loading: state.loading,
    error: state.error,

    // Actions
    loadSettings,
    loadSecurityEvents,
    loadPrivacyReport,
    updatePrivacySettings,
    updateNotificationSettings,
    updatePreferences,
    updateSecuritySettings,
    enable2FA,
    verify2FA,
    disable2FA,
    changePassword,
    changeEmail,
    changePhone,
    addTrustedDevice,
    removeTrustedDevice,
    terminateSession,
    terminateAllSessions,
    blockUser,
    unblockUser,
    reportUser,
    exportData,
    deactivateAccount,
    reactivateAccount,
    deleteAccount,
    cancelAccountDeletion,
    acknowledgeSecurityEvent,
    clearSecurityEvents,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};

// Hook to use settings context
export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export default SettingsContext;
