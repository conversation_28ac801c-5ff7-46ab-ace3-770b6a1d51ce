import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  ArrowBack,
  Security as SecurityIcon,
  Lock as LockIcon,
  Shield as ShieldIcon,
  Smartphone as SmartphoneIcon,
  Computer as ComputerIcon,
  Logout as LogoutIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ChevronRight as ChevronRightIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';

// Типы для настроек безопасности
interface SecuritySettings {
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  suspiciousActivityAlerts: boolean;
  deviceManagement: boolean;
  sessionTimeout: number;
}

interface LoginSession {
  id: string;
  device: string;
  browser: string;
  location: string;
  ipAddress: string;
  lastActive: string;
  isCurrent: boolean;
}

const SecuritySettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [sessionsDialogOpen, setSessionsDialogOpen] = useState(false);
  const [twoFactorDialogOpen, setTwoFactorDialogOpen] = useState(false);

  // Мок данные для сессий
  const [sessions, setSessions] = useState<LoginSession[]>([
    {
      id: '1',
      device: 'iPhone 14 Pro',
      browser: 'Safari',
      location: 'Москва, Россия',
      ipAddress: '***********',
      lastActive: '2024-01-15T10:30:00Z',
      isCurrent: true
    },
    {
      id: '2',
      device: 'MacBook Pro',
      browser: 'Chrome',
      location: 'Москва, Россия',
      ipAddress: '***********',
      lastActive: '2024-01-14T15:20:00Z',
      isCurrent: false
    }
  ]);

  const [settings, setSettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    loginNotifications: true,
    suspiciousActivityAlerts: true,
    deviceManagement: true,
    sessionTimeout: 30
  });

  const handleSettingChange = (setting: keyof SecuritySettings, value: boolean | number) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Здесь будет вызов API для сохранения настроек
      // await updateSecuritySettings(settings);

      setSuccess('Настройки безопасности успешно обновлены');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      setLoading(true);
      // Здесь будет вызов API для завершения сессии
      // await terminateSession(sessionId);
      
      setSessions(prev => prev.filter(session => session.id !== sessionId));
      setSuccess('Сессия успешно завершена');
    } catch (err: any) {
      setError(err.message || 'Ошибка при завершении сессии');
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      setLoading(true);
      // Здесь будет вызов API для завершения всех сессий
      // await terminateAllSessions();
      
      setSessions(prev => prev.filter(session => session.isCurrent));
      setSuccess('Все сессии успешно завершены');
      setSessionsDialogOpen(false);
    } catch (err: any) {
      setError(err.message || 'Ошибка при завершении сессий');
    } finally {
      setLoading(false);
    }
  };

  const handleEnable2FA = () => {
    setTwoFactorDialogOpen(true);
  };

  const handleDisable2FA = async () => {
    try {
      setLoading(true);
      // Здесь будет вызов API для отключения 2FA
      // await disable2FA();
      
      setSettings(prev => ({ ...prev, twoFactorEnabled: false }));
      setSuccess('Двухфакторная аутентификация отключена');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отключении 2FA');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Layout title="Настройки безопасности">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Настройки безопасности - Likes & Love</title>
        <meta name="description" content="Управление настройками безопасности в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Настройки безопасности">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton 
                onClick={() => router.back()} 
                sx={{ display: { xs: 'flex', md: 'none' } }}
              >
                <ArrowBack />
              </IconButton>
              <SecurityIcon color="primary" sx={{ fontSize: 32 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                Настройки безопасности
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <Grid container spacing={3}>
              {/* Двухфакторная аутентификация */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <ShieldIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Двухфакторная аутентификация
                      </Typography>
                      {settings.twoFactorEnabled ? (
                        <Chip icon={<CheckCircleIcon />} label="Включена" color="success" size="small" />
                      ) : (
                        <Chip icon={<WarningIcon />} label="Отключена" color="warning" size="small" />
                      )}
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Дополнительный уровень защиты вашего аккаунта. Рекомендуется для всех пользователей.
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      {settings.twoFactorEnabled ? (
                        <>
                          <Button
                            variant="outlined"
                            onClick={() => router.push('/settings/security/2fa')}
                            startIcon={<ShieldIcon />}
                          >
                            Управление
                          </Button>
                          <Button
                            variant="outlined"
                            color="error"
                            onClick={handleDisable2FA}
                            disabled={loading}
                          >
                            Отключить
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="contained"
                          onClick={handleEnable2FA}
                          startIcon={<ShieldIcon />}
                        >
                          Включить 2FA
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Пароль */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <LockIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Пароль
                      </Typography>
                    </Box>

                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Изменить пароль"
                          secondary="Последнее изменение: 2 недели назад"
                        />
                        <ListItemSecondaryAction>
                          <Button
                            onClick={() => router.push('/settings/account/password')}
                            endIcon={<ChevronRightIcon />}
                          >
                            Изменить
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Активные сессии */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <ComputerIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Активные сессии
                      </Typography>
                      <Chip label={`${sessions.length} устройств`} size="small" />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Управляйте устройствами, с которых выполнен вход в ваш аккаунт
                    </Typography>

                    <Button
                      variant="outlined"
                      onClick={() => setSessionsDialogOpen(true)}
                      startIcon={<ComputerIcon />}
                      fullWidth={isMobile}
                    >
                      Управление сессиями
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Container>

        {/* Диалог управления сессиями */}
        <Dialog
          open={sessionsDialogOpen}
          onClose={() => setSessionsDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ComputerIcon />
              Активные сессии
            </Box>
          </DialogTitle>
          <DialogContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Устройство</TableCell>
                    <TableCell>Местоположение</TableCell>
                    <TableCell>Последняя активность</TableCell>
                    <TableCell>Действия</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {session.device.includes('iPhone') || session.device.includes('Android') ? (
                            <SmartphoneIcon />
                          ) : (
                            <ComputerIcon />
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {session.device}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {session.browser}
                            </Typography>
                          </Box>
                          {session.isCurrent && (
                            <Chip label="Текущая" size="small" color="primary" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{session.location}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {session.ipAddress}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(session.lastActive).toLocaleString('ru-RU')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {!session.isCurrent && (
                          <IconButton
                            onClick={() => handleTerminateSession(session.id)}
                            color="error"
                            size="small"
                          >
                            <LogoutIcon />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Alert severity="warning" sx={{ mt: 2 }}>
              Если вы видите незнакомые устройства, немедленно завершите их сессии и смените пароль
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSessionsDialogOpen(false)}>
              Закрыть
            </Button>
            <Button
              onClick={handleTerminateAllSessions}
              color="error"
              variant="outlined"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <LogoutIcon />}
            >
              Завершить все сессии
            </Button>
          </DialogActions>
        </Dialog>

        {/* Диалог настройки 2FA */}
        <Dialog
          open={twoFactorDialogOpen}
          onClose={() => setTwoFactorDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ShieldIcon />
              Настройка двухфакторной аутентификации
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography sx={{ mb: 2 }}>
              Для настройки 2FA вам потребуется приложение-аутентификатор, например:
            </Typography>
            <List>
              <ListItem>
                <ListItemText primary="• Google Authenticator" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• Microsoft Authenticator" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• Authy" />
              </ListItem>
            </List>
            <Alert severity="info" sx={{ mt: 2 }}>
              После включения 2FA вам потребуется код из приложения при каждом входе в аккаунт
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setTwoFactorDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              onClick={() => {
                setTwoFactorDialogOpen(false);
                router.push('/settings/security/2fa/setup');
              }}
              variant="contained"
              startIcon={<ShieldIcon />}
            >
              Продолжить
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default SecuritySettingsPage;
