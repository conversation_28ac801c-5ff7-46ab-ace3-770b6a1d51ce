export interface Like {
  id: string;
  fromUserId: string;
  toUserId: string;
  fromUser?: UserProfile;
  toUser?: UserProfile;
  type: 'like' | 'super_like' | 'pass';
  message?: string;
  createdAt: string;
  isRead: boolean;
  isMutual: boolean;
}

export interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  avatarUrl?: string;
  isOnline: boolean;
  lastActiveAt: string;
  bio?: string;
  location?: {
    city: string;
    country: string;
    distance?: number;
  };
  interests: string[];
  verificationStatus: {
    phone: boolean;
    email: boolean;
    photo: boolean;
    document: boolean;
  };
  photos: ProfilePhoto[];
  compatibility?: {
    score: number;
    commonInterests: string[];
    reasons: string[];
  };
}

export interface ProfilePhoto {
  id: string;
  url: string;
  thumbnailUrl: string;
  isMain: boolean;
  order: number;
}

export interface Match {
  id: string;
  user1Id: string;
  user2Id: string;
  user1: UserProfile;
  user2: UserProfile;
  matchedAt: string;
  lastMessageAt?: string;
  lastMessage?: {
    id: string;
    text: string;
    senderId: string;
    sentAt: string;
    isRead: boolean;
  };
  isRead: boolean;
  compatibilityScore: number;
  commonInterests: string[];
  status: 'active' | 'archived' | 'blocked';
  unreadMessagesCount: number;
}

export interface LikeActionRequest {
  targetUserId: string;
  type: 'like' | 'super_like' | 'pass';
  message?: string;
}

export interface LikeActionResponse {
  success: boolean;
  message: string;
  isMatch: boolean;
  match?: Match;
  error?: string;
}

export interface LikesStats {
  totalReceived: number;
  totalSent: number;
  totalMutual: number;
  todayReceived: number;
  todaySent: number;
  superLikesReceived: number;
  superLikesSent: number;
  passesGiven: number;
  matchRate: number;
  responseRate: number;
}

export interface FilterOptions {
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'matchedAt' | 'lastActiveAt' | 'compatibility';
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
  isRead?: boolean;
  type?: 'like' | 'super_like' | 'pass';
  verifiedOnly?: boolean;
  onlineOnly?: boolean;
  minAge?: number;
  maxAge?: number;
  maxDistance?: number;
  hasPhotos?: boolean;
  status?: 'active' | 'archived' | 'blocked';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface LikesContextType {
  receivedLikes: Like[];
  sentLikes: Like[];
  mutualLikes: Like[];
  matches: Match[];
  stats: LikesStats | null;
  loading: boolean;
  error: string | null;
  sendLike: (request: LikeActionRequest) => Promise<LikeActionResponse>;
  markLikesAsRead: (likeIds: string[]) => Promise<void>;
  markMatchesAsRead: (matchIds: string[]) => Promise<void>;
  loadReceivedLikes: (filters?: FilterOptions) => Promise<PaginatedResponse<Like>>;
  loadSentLikes: (filters?: FilterOptions) => Promise<PaginatedResponse<Like>>;
  loadMutualLikes: (filters?: FilterOptions) => Promise<PaginatedResponse<Like>>;
  loadMatches: (filters?: FilterOptions) => Promise<PaginatedResponse<Match>>;
  refreshStats: () => Promise<void>;
}

export interface MatchDetailsResponse {
  match: Match;
  conversation: {
    id: string;
    messages: Message[];
    canSendMessage: boolean;
    isBlocked: boolean;
  };
  suggestions: {
    iceBreakers: string[];
    commonTopics: string[];
    meetingPlaces: string[];
  };
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  text: string;
  type: 'text' | 'image' | 'voice' | 'video' | 'location' | 'sticker';
  attachments?: MessageAttachment[];
  sentAt: string;
  deliveredAt?: string;
  readAt?: string;
  isEdited: boolean;
  editedAt?: string;
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
  };
  reactions?: MessageReaction[];
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'voice' | 'video' | 'file';
  url: string;
  thumbnailUrl?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number; // for voice/video
  width?: number; // for images/videos
  height?: number; // for images/videos
}

export interface MessageReaction {
  id: string;
  userId: string;
  emoji: string;
  createdAt: string;
}

export interface SendMessageRequest {
  conversationId: string;
  text?: string;
  type: 'text' | 'image' | 'voice' | 'video' | 'location' | 'sticker';
  attachments?: File[];
  replyToMessageId?: string;
}

export interface SendMessageResponse {
  success: boolean;
  message?: Message;
  error?: string;
}

export interface CompatibilityAnalysis {
  overallScore: number;
  categories: {
    interests: {
      score: number;
      common: string[];
      total: number;
    };
    lifestyle: {
      score: number;
      factors: string[];
    };
    values: {
      score: number;
      alignment: string[];
    };
    communication: {
      score: number;
      style: string;
    };
  };
  strengths: string[];
  challenges: string[];
  recommendations: string[];
}

export interface MeetingSuggestion {
  id: string;
  type: 'coffee' | 'restaurant' | 'activity' | 'event' | 'walk' | 'cultural';
  title: string;
  description: string;
  location: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
    distance: number;
  };
  estimatedCost: {
    min: number;
    max: number;
    currency: string;
  };
  duration: number; // minutes
  rating: number;
  photos: string[];
  tags: string[];
  availability: {
    days: string[];
    timeSlots: string[];
  };
}

export interface IceBreaker {
  id: string;
  text: string;
  category: 'question' | 'compliment' | 'joke' | 'observation' | 'shared_interest';
  basedOn: string[]; // what it's based on (common interests, photos, etc.)
  popularity: number;
  successRate: number;
}
