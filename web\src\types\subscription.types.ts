export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string;
  features: SubscriptionFeature[];
  pricing: {
    monthly: {
      price: number;
      currency: string;
      originalPrice?: number;
      discount?: number;
    };
    yearly: {
      price: number;
      currency: string;
      originalPrice?: number;
      discount?: number;
      monthlyEquivalent: number;
    };
    lifetime?: {
      price: number;
      currency: string;
      originalPrice?: number;
      discount?: number;
    };
  };
  limits: SubscriptionLimits;
  badge?: {
    text: string;
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  };
  isPopular: boolean;
  isRecommended: boolean;
  order: number;
  status: 'active' | 'deprecated' | 'coming_soon';
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionFeature {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'communication' | 'discovery' | 'profile' | 'premium' | 'analytics' | 'support';
  isHighlight: boolean;
  isUnlimited?: boolean;
  limit?: number;
  unit?: string;
}

export interface SubscriptionLimits {
  dailyLikes: number | 'unlimited';
  dailySuperLikes: number | 'unlimited';
  dailyBoosts: number | 'unlimited';
  monthlyRewinds: number | 'unlimited';
  profileViews: number | 'unlimited';
  advancedFilters: boolean;
  readReceipts: boolean;
  onlineStatus: boolean;
  incognitoMode: boolean;
  prioritySupport: boolean;
  adFree: boolean;
  profileBoost: boolean;
  topPicksAccess: boolean;
  passportFeature: boolean;
  videoCallMinutes: number | 'unlimited';
  messageStorage: number | 'unlimited'; // in MB
  photoUploads: number | 'unlimited';
  customBadges: boolean;
  analyticsAccess: boolean;
  exportData: boolean;
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'cancelled' | 'expired' | 'pending' | 'suspended';
  billingCycle: 'monthly' | 'yearly' | 'lifetime';
  startDate: string;
  endDate?: string;
  nextBillingDate?: string;
  autoRenew: boolean;
  trialEndDate?: string;
  isTrialActive: boolean;
  cancelledAt?: string;
  cancellationReason?: string;
  suspendedAt?: string;
  suspensionReason?: string;
  usage: SubscriptionUsage;
  paymentMethod?: PaymentMethod;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionUsage {
  period: {
    start: string;
    end: string;
  };
  limits: {
    dailyLikes: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
    dailySuperLikes: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
    dailyBoosts: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
    monthlyRewinds: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
    videoCallMinutes: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
    messageStorage: {
      used: number; // in MB
      limit: number | 'unlimited';
    };
    photoUploads: {
      used: number;
      limit: number | 'unlimited';
      resetAt: string;
    };
  };
  features: {
    [key: string]: {
      used: number;
      limit: number | 'unlimited';
      lastUsed?: string;
    };
  };
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer' | 'crypto';
  provider: string;
  isDefault: boolean;
  details: {
    // For cards
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    // For PayPal
    email?: string;
    // For bank transfer
    bankName?: string;
    accountNumber?: string;
    // For crypto
    walletAddress?: string;
    currency?: string;
  };
  billingAddress?: {
    country: string;
    city: string;
    state?: string;
    postalCode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PaymentTransaction {
  id: string;
  userId: string;
  subscriptionId?: string;
  type: 'subscription' | 'one_time' | 'refund' | 'chargeback';
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  amount: number;
  currency: string;
  description: string;
  paymentMethod: PaymentMethod;
  paymentProvider: string;
  providerTransactionId: string;
  metadata: {
    planId?: string;
    billingCycle?: string;
    promoCode?: string;
    discount?: number;
    tax?: number;
    fees?: number;
  };
  receipt?: {
    url: string;
    number: string;
  };
  refund?: {
    amount: number;
    reason: string;
    refundedAt: string;
    refundTransactionId: string;
  };
  failureReason?: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PromoCode {
  id: string;
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed_amount' | 'free_trial';
  value: number; // percentage (0-100) or fixed amount
  currency?: string;
  applicablePlans: string[];
  restrictions: {
    minAmount?: number;
    maxDiscount?: number;
    firstTimeOnly: boolean;
    oneTimeUse: boolean;
    maxUses?: number;
    currentUses: number;
  };
  validFrom: string;
  validUntil: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionAnalytics {
  overview: {
    totalRevenue: number;
    monthlyRecurringRevenue: number;
    annualRecurringRevenue: number;
    activeSubscriptions: number;
    churnRate: number;
    averageRevenuePerUser: number;
    lifetimeValue: number;
  };
  planDistribution: {
    planId: string;
    planName: string;
    activeSubscriptions: number;
    revenue: number;
    percentage: number;
  }[];
  revenueHistory: {
    date: string;
    revenue: number;
    subscriptions: number;
    newSubscriptions: number;
    cancelledSubscriptions: number;
  }[];
  conversionFunnel: {
    visitors: number;
    trialStarts: number;
    trialToSubscription: number;
    conversionRate: number;
  };
  retentionCohorts: {
    cohort: string;
    month0: number;
    month1: number;
    month3: number;
    month6: number;
    month12: number;
  }[];
}

// Request/Response types
export interface CreateSubscriptionRequest {
  planId: string;
  billingCycle: 'monthly' | 'yearly' | 'lifetime';
  paymentMethodId: string;
  promoCode?: string;
  autoRenew?: boolean;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  billingCycle?: 'monthly' | 'yearly' | 'lifetime';
  autoRenew?: boolean;
}

export interface CancelSubscriptionRequest {
  reason: string;
  feedback?: string;
  effectiveDate?: 'immediately' | 'end_of_period';
}

export interface CreatePaymentMethodRequest {
  type: PaymentMethod['type'];
  provider: string;
  token: string; // Payment provider token
  isDefault?: boolean;
  billingAddress?: PaymentMethod['billingAddress'];
}

export interface ProcessPaymentRequest {
  amount: number;
  currency: string;
  paymentMethodId: string;
  description: string;
  metadata?: Record<string, any>;
}

export interface ValidatePromoCodeRequest {
  code: string;
  planId: string;
  billingCycle: string;
}

export interface ValidatePromoCodeResponse {
  isValid: boolean;
  promoCode?: PromoCode;
  discount?: {
    type: 'percentage' | 'fixed_amount' | 'free_trial';
    value: number;
    amount: number;
    currency?: string;
  };
  error?: string;
}

// Context types
export interface SubscriptionContextType {
  // State
  currentSubscription: UserSubscription | null;
  availablePlans: SubscriptionPlan[];
  paymentMethods: PaymentMethod[];
  transactionHistory: PaymentTransaction[];
  usage: SubscriptionUsage | null;
  loading: boolean;
  error: string | null;

  // Actions
  loadCurrentSubscription: () => Promise<void>;
  loadAvailablePlans: () => Promise<void>;
  loadPaymentMethods: () => Promise<void>;
  loadTransactionHistory: () => Promise<void>;
  loadUsage: () => Promise<void>;
  
  createSubscription: (request: CreateSubscriptionRequest) => Promise<UserSubscription>;
  updateSubscription: (request: UpdateSubscriptionRequest) => Promise<UserSubscription>;
  cancelSubscription: (request: CancelSubscriptionRequest) => Promise<void>;
  reactivateSubscription: () => Promise<UserSubscription>;
  
  addPaymentMethod: (request: CreatePaymentMethodRequest) => Promise<PaymentMethod>;
  updatePaymentMethod: (id: string, updates: Partial<PaymentMethod>) => Promise<PaymentMethod>;
  deletePaymentMethod: (id: string) => Promise<void>;
  setDefaultPaymentMethod: (id: string) => Promise<void>;
  
  processPayment: (request: ProcessPaymentRequest) => Promise<PaymentTransaction>;
  validatePromoCode: (request: ValidatePromoCodeRequest) => Promise<ValidatePromoCodeResponse>;
  
  getFeatureUsage: (feature: string) => { used: number; limit: number | 'unlimited'; canUse: boolean };
  hasFeature: (feature: string) => boolean;
  isFeatureUnlimited: (feature: string) => boolean;
  getRemainingUsage: (feature: string) => number | 'unlimited';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
