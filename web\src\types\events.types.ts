export interface EventOrganizer {
  id: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  isVerified: boolean;
  rating: number;
  eventsCount: number;
  followersCount: number;
  bio?: string;
}

export interface EventParticipant {
  id: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  isOnline: boolean;
  verificationStatus: {
    phone: boolean;
    email: boolean;
    photo: boolean;
    document: boolean;
  };
  status: 'going' | 'interested' | 'maybe' | 'not_going';
  registeredAt: string;
  checkedIn: boolean;
  checkedInAt?: string;
}

export interface EventLocation {
  type: 'physical' | 'online' | 'hybrid';
  name: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  venue?: {
    id: string;
    name: string;
    type: 'conference_center' | 'hotel' | 'restaurant' | 'club' | 'outdoor' | 'cultural' | 'sports' | 'other';
    capacity: number;
    rating: number;
    amenities: string[];
    photos: string[];
    contactInfo?: {
      phone?: string;
      email?: string;
      website?: string;
    };
  };
  onlineDetails?: {
    platform: 'zoom' | 'meet' | 'teams' | 'youtube' | 'twitch' | 'other';
    link: string;
    streamKey?: string;
    requiresRegistration: boolean;
  };
}

export interface EventTicket {
  id: string;
  type: 'free' | 'paid' | 'donation';
  name: string;
  description?: string;
  price: number;
  currency: string;
  quantity: number;
  sold: number;
  maxPerPerson: number;
  salesStart: string;
  salesEnd: string;
  isActive: boolean;
  benefits?: string[];
}

export interface Event {
  id: string;
  title: string;
  description: string;
  shortDescription?: string;
  type: 'conference' | 'workshop' | 'meetup' | 'party' | 'concert' | 'sports' | 'cultural' | 'networking' | 'educational' | 'charity' | 'other';
  category: string;
  subcategory?: string;
  
  status: 'draft' | 'published' | 'active' | 'completed' | 'cancelled' | 'postponed';
  visibility: 'public' | 'private' | 'invite_only';
  
  organizer: EventOrganizer;
  coOrganizers?: EventOrganizer[];
  
  startDate: string;
  endDate: string;
  timezone: string;
  duration: number; // in minutes
  
  location: EventLocation;
  
  capacity: number;
  participants: EventParticipant[];
  waitingList: EventParticipant[];
  
  tickets: EventTicket[];
  
  tags: string[];
  ageRestriction?: {
    min: number;
    max?: number;
  };
  
  requirements?: {
    verifiedOnly: boolean;
    premiumOnly: boolean;
    inviteOnly: boolean;
    approvalRequired: boolean;
    interests?: string[];
  };
  
  media: {
    coverImage: string;
    photos: string[];
    videos?: string[];
    livestreamUrl?: string;
  };
  
  agenda?: EventAgendaItem[];
  speakers?: EventSpeaker[];
  sponsors?: EventSponsor[];
  
  socialLinks?: {
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter?: string;
    telegram?: string;
  };
  
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  
  statistics: {
    views: number;
    interested: number;
    going: number;
    shares: number;
    checkIns: number;
  };
  
  settings: {
    allowComments: boolean;
    allowSharing: boolean;
    showParticipants: boolean;
    enableChat: boolean;
    enableNetworking: boolean;
    sendReminders: boolean;
    collectFeedback: boolean;
  };
  
  feedback?: {
    rating: number;
    reviewsCount: number;
    reviews: EventReview[];
  };
  
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    endDate?: string;
    occurrences?: number;
  };
}

export interface EventAgendaItem {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  speaker?: EventSpeaker;
  location?: string;
  type: 'presentation' | 'workshop' | 'break' | 'networking' | 'panel' | 'other';
}

export interface EventSpeaker {
  id: string;
  firstName: string;
  lastName: string;
  title?: string;
  company?: string;
  bio: string;
  avatarUrl?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  topics: string[];
  rating: number;
}

export interface EventSponsor {
  id: string;
  name: string;
  logo: string;
  website?: string;
  tier: 'platinum' | 'gold' | 'silver' | 'bronze' | 'partner';
  description?: string;
}

export interface EventReview {
  id: string;
  eventId: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  rating: number;
  comment?: string;
  aspects: {
    organization: number;
    content: number;
    venue: number;
    networking: number;
    value: number;
  };
  createdAt: string;
  isVerified: boolean;
  helpfulCount: number;
}

export interface CreateEventRequest {
  title: string;
  description: string;
  shortDescription?: string;
  type: Event['type'];
  category: string;
  subcategory?: string;
  visibility: Event['visibility'];
  startDate: string;
  endDate: string;
  timezone: string;
  location: EventLocation;
  capacity: number;
  tickets: Omit<EventTicket, 'id' | 'sold'>[];
  tags: string[];
  ageRestriction?: Event['ageRestriction'];
  requirements?: Event['requirements'];
  coverImage?: File;
  photos?: File[];
  agenda?: Omit<EventAgendaItem, 'id'>[];
  speakers?: Omit<EventSpeaker, 'id' | 'rating'>[];
  sponsors?: Omit<EventSponsor, 'id'>[];
  socialLinks?: Event['socialLinks'];
  settings: Event['settings'];
  isRecurring: boolean;
  recurringPattern?: Event['recurringPattern'];
}

export interface UpdateEventRequest {
  title?: string;
  description?: string;
  shortDescription?: string;
  startDate?: string;
  endDate?: string;
  location?: EventLocation;
  capacity?: number;
  tags?: string[];
  ageRestriction?: Event['ageRestriction'];
  requirements?: Event['requirements'];
  settings?: Event['settings'];
  status?: Event['status'];
}

export interface EventFilters {
  type?: Event['type'][];
  category?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
  location?: {
    type?: EventLocation['type'];
    radius?: number;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    city?: string;
  };
  price?: {
    type?: 'free' | 'paid';
    maxPrice?: number;
  };
  capacity?: {
    min?: number;
    max?: number;
  };
  requirements?: {
    ageRange?: { min: number; max: number };
    verifiedOnly?: boolean;
    premiumOnly?: boolean;
  };
  tags?: string[];
  organizer?: string;
  status?: Event['status'][];
  sortBy?: 'date' | 'popularity' | 'distance' | 'created' | 'price' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

export interface EventSearchResult {
  events: Event[];
  totalCount: number;
  filters: EventFilters;
  suggestions: string[];
  facets: {
    categories: { name: string; count: number }[];
    locations: { name: string; count: number }[];
    organizers: { name: string; count: number }[];
    priceRanges: { range: string; count: number }[];
  };
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface EventRegistration {
  id: string;
  eventId: string;
  event: Event;
  userId: string;
  ticketId?: string;
  ticket?: EventTicket;
  status: 'registered' | 'confirmed' | 'cancelled' | 'refunded' | 'waitlisted';
  registeredAt: string;
  confirmedAt?: string;
  cancelledAt?: string;
  checkInCode: string;
  isCheckedIn: boolean;
  checkedInAt?: string;
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentId?: string;
  amount?: number;
  currency?: string;
}

export interface EventNotification {
  id: string;
  type: 'reminder' | 'update' | 'cancellation' | 'new_event' | 'registration_confirmed' | 'check_in';
  eventId: string;
  event: Event;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  scheduledAt?: string;
  actionUrl?: string;
}

export interface EventStatistics {
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  cancelledEvents: number;
  totalParticipants: number;
  averageRating: number;
  popularCategories: {
    category: string;
    count: number;
  }[];
  popularLocations: {
    location: string;
    count: number;
  }[];
  monthlyStats: {
    month: string;
    events: number;
    participants: number;
    revenue: number;
  }[];
  topOrganizers: {
    organizer: EventOrganizer;
    eventsCount: number;
    participantsCount: number;
    rating: number;
  }[];
}

export interface EventContextType {
  // State
  events: Event[];
  myEvents: Event[];
  registrations: EventRegistration[];
  notifications: EventNotification[];
  loading: boolean;
  error: string | null;
  filters: EventFilters;
  
  // Actions
  loadEvents: (filters?: EventFilters) => Promise<void>;
  loadMyEvents: () => Promise<void>;
  loadRegistrations: () => Promise<void>;
  getEvent: (id: string) => Promise<Event>;
  createEvent: (request: CreateEventRequest) => Promise<Event>;
  updateEvent: (id: string, request: UpdateEventRequest) => Promise<Event>;
  deleteEvent: (id: string) => Promise<void>;
  registerForEvent: (eventId: string, ticketId?: string) => Promise<EventRegistration>;
  cancelRegistration: (registrationId: string) => Promise<void>;
  checkInToEvent: (eventId: string, checkInCode: string) => Promise<void>;
  rateEvent: (eventId: string, rating: number, comment?: string, aspects?: EventReview['aspects']) => Promise<void>;
  reportEvent: (eventId: string, reason: string, details?: string) => Promise<void>;
  searchEvents: (query: string, filters?: EventFilters) => Promise<EventSearchResult>;
  updateFilters: (filters: Partial<EventFilters>) => void;
  markNotificationAsRead: (id: string) => Promise<void>;
  getStatistics: () => Promise<EventStatistics>;
  followOrganizer: (organizerId: string) => Promise<void>;
  unfollowOrganizer: (organizerId: string) => Promise<void>;
  shareEvent: (eventId: string, platform: string) => Promise<void>;
}

export interface EventTemplate {
  id: string;
  name: string;
  description: string;
  type: Event['type'];
  category: string;
  defaultDuration: number;
  defaultCapacity: number;
  suggestedTags: string[];
  defaultSettings: Event['settings'];
  isPopular: boolean;
  usageCount: number;
}

export interface EventSuggestion {
  id: string;
  type: 'similar_interests' | 'nearby_location' | 'popular_organizer' | 'trending' | 'friend_activity';
  event: Event;
  reason: string;
  score: number;
  expiresAt: string;
}

export interface EventChat {
  id: string;
  eventId: string;
  participants: EventParticipant[];
  messages: EventChatMessage[];
  isActive: boolean;
  createdAt: string;
}

export interface EventChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  text: string;
  type: 'text' | 'system' | 'announcement';
  sentAt: string;
  isEdited: boolean;
  editedAt?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
