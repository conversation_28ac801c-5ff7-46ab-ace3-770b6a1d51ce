import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  Star as StarIcon,
  MoreVert as MoreVertIcon,
  Message as MessageIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getMutualLikes, 
  getLikesStats 
} from '../../src/services/likesService';
import { Like, FilterOptions, LikesStats } from '../../src/types/likes.types';

const MutualLikesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [likes, setLikes] = useState<Like[]>([]);
  const [stats, setStats] = useState<LikesStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedLike, setSelectedLike] = useState<Like | null>(null);

  // Pagination and filters
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'createdAt' | 'lastActiveAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [filterType, setFilterType] = useState<'all' | 'like' | 'super_like'>('all');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>(undefined);
  const [filterOnline, setFilterOnline] = useState<boolean | undefined>(undefined);

  const limit = 20;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadLikes();
    loadStats();
  }, [user, router, page, sortBy, sortOrder, filterType, filterVerified, filterOnline]);

  const loadLikes = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy,
        sortOrder,
        verifiedOnly: filterVerified,
        onlineOnly: filterOnline
      };

      if (filterType !== 'all') {
        filters.type = filterType as any;
      }

      const response = await getMutualLikes(filters);
      setLikes(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки взаимных лайков');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getLikesStats();
      setStats(statsData);
    } catch (err: any) {
      // Не показываем ошибку для статистики
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, like: Like) => {
    setAnchorEl(event.currentTarget);
    setSelectedLike(like);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedLike(null);
  };

  const handleViewProfile = (userId: string) => {
    router.push(`/users/${userId}`);
    handleMenuClose();
  };

  const handleSendMessage = (userId: string) => {
    router.push(`/chat?userId=${userId}`);
    handleMenuClose();
  };

  const handleStartChat = (like: Like) => {
    // Определяем с кем начать чат (не с самим собой)
    const otherUserId = like.fromUserId === user?.id ? like.toUserId : like.fromUserId;
    router.push(`/chat?userId=${otherUserId}`);
  };

  const formatTime = (createdAt: string) => {
    const now = new Date();
    const likeTime = new Date(createdAt);
    const diffInHours = Math.floor((now.getTime() - likeTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getOtherUser = (like: Like) => {
    // Возвращаем данные другого пользователя (не текущего)
    if (like.fromUserId === user?.id) {
      return like.toUser;
    }
    return like.fromUser;
  };

  const getOtherUserId = (like: Like) => {
    return like.fromUserId === user?.id ? like.toUserId : like.fromUserId;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Взаимные лайки - Likes & Love</title>
        <meta 
          name="description" 
          content="Посмотрите взаимные лайки в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/likes/mutual" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <CheckCircleIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'success.main' }} />
                    Взаимные лайки
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stats ? `${stats.totalMutual} взаимных лайков` : 'Загрузка статистики...'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Полученные
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes/sent')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Отправленные
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/matches')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Совпадения
                  </Button>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {/* Stats card */}
              {stats && (
                <Card sx={{ mb: 3, background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.light})` }}>
                  <CardContent sx={{ textAlign: 'center', color: 'white' }}>
                    <Typography variant="h4" gutterBottom>
                      {stats.totalMutual}
                    </Typography>
                    <Typography variant="h6" gutterBottom>
                      Взаимных лайков
                    </Typography>
                    <Typography variant="body2">
                      Из {stats.totalSent} отправленных лайков {stats.totalMutual} стали взаимными
                      ({Math.round((stats.totalMutual / Math.max(stats.totalSent, 1)) * 100)}% успеха)
                    </Typography>
                  </CardContent>
                </Card>
              )}

              {/* Filters */}
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Сортировка</InputLabel>
                      <Select
                        value={`${sortBy}-${sortOrder}`}
                        label="Сортировка"
                        onChange={(e) => {
                          const [newSortBy, newSortOrder] = e.target.value.split('-');
                          setSortBy(newSortBy as any);
                          setSortOrder(newSortOrder as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="createdAt-desc">Сначала новые</MenuItem>
                        <MenuItem value="createdAt-asc">Сначала старые</MenuItem>
                        <MenuItem value="lastActiveAt-desc">По активности</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Тип</InputLabel>
                      <Select
                        value={filterType}
                        label="Тип"
                        onChange={(e) => {
                          setFilterType(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="like">Лайки</MenuItem>
                        <MenuItem value="super_like">Супер-лайки</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Верификация</InputLabel>
                      <Select
                        value={filterVerified === undefined ? 'all' : filterVerified ? 'verified' : 'unverified'}
                        label="Верификация"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterVerified(
                            value === 'all' ? undefined : value === 'verified'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="verified">Верифицированные</MenuItem>
                        <MenuItem value="unverified">Неверифицированные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Активность</InputLabel>
                      <Select
                        value={filterOnline === undefined ? 'all' : filterOnline ? 'online' : 'offline'}
                        label="Активность"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterOnline(
                            value === 'all' ? undefined : value === 'online'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="online">Онлайн</MenuItem>
                        <MenuItem value="offline">Оффлайн</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка взаимных лайков...
                  </Typography>
                </Box>
              ) : likes.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <CheckCircleIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    У вас пока нет взаимных лайков
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Отправляйте лайки понравившимся людям, чтобы получить взаимность
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/discover')}
                  >
                    Начать знакомства
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Likes grid */}
                    <Grid container spacing={2}>
                      {likes.map((like) => {
                        const otherUser = getOtherUser(like);
                        const otherUserId = getOtherUserId(like);
                        
                        return (
                          <Grid item xs={12} sm={6} md={4} lg={3} key={like.id}>
                            <Card 
                              sx={{ 
                                border: `2px solid ${theme.palette.success.main}`,
                                '&:hover': {
                                  boxShadow: theme.shadows[4]
                                }
                              }}
                            >
                              <CardContent sx={{ pb: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                  <Badge
                                    overlap="circular"
                                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                    badgeContent={
                                      otherUser?.isOnline ? (
                                        <Box
                                          sx={{
                                            width: 12,
                                            height: 12,
                                            borderRadius: '50%',
                                            backgroundColor: 'success.main',
                                            border: '2px solid white'
                                          }}
                                        />
                                      ) : null
                                    }
                                  >
                                    <Avatar
                                      src={otherUser?.avatarUrl}
                                      sx={{ width: 56, height: 56 }}
                                    >
                                      {otherUser?.firstName[0]}
                                    </Avatar>
                                  </Badge>
                                  <Box sx={{ ml: 2, flexGrow: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Typography variant="subtitle2" noWrap>
                                        {otherUser?.firstName}
                                      </Typography>
                                      {otherUser?.verificationStatus.phone && (
                                        <VerifiedIcon 
                                          sx={{ 
                                            ml: 0.5, 
                                            fontSize: 16, 
                                            color: 'primary.main' 
                                          }} 
                                        />
                                      )}
                                    </Box>
                                    <Typography variant="caption" color="text.secondary">
                                      {otherUser?.age} лет
                                    </Typography>
                                  </Box>
                                  <IconButton
                                    size="small"
                                    onClick={(e) => handleMenuOpen(e, like)}
                                  >
                                    <MoreVertIcon />
                                  </IconButton>
                                </Box>

                                <Box sx={{ mb: 1 }}>
                                  <Chip
                                    icon={<CheckCircleIcon />}
                                    label="Взаимный лайк"
                                    size="small"
                                    color="success"
                                    sx={{ mr: 1 }}
                                  />
                                  <Typography variant="caption" color="text.secondary">
                                    {formatTime(like.createdAt)}
                                  </Typography>
                                </Box>

                                {like.type === 'super_like' && (
                                  <Chip
                                    icon={<StarIcon />}
                                    label="Супер-лайк"
                                    size="small"
                                    color="warning"
                                    variant="outlined"
                                    sx={{ mb: 1 }}
                                  />
                                )}

                                {like.message && (
                                  <Typography variant="body2" sx={{ mb: 1, fontStyle: 'italic' }}>
                                    "{like.message}"
                                  </Typography>
                                )}

                                {otherUser?.isOnline ? (
                                  <Typography variant="caption" color="success.main">
                                    Онлайн
                                  </Typography>
                                ) : (
                                  <Typography variant="caption" color="text.secondary">
                                    <ScheduleIcon sx={{ fontSize: 12, mr: 0.5, verticalAlign: 'middle' }} />
                                    {formatTime(otherUser?.lastActiveAt || '')}
                                  </Typography>
                                )}
                              </CardContent>

                              <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                                <Button
                                  size="small"
                                  variant="contained"
                                  onClick={() => handleStartChat(like)}
                                  startIcon={<MessageIcon />}
                                  fullWidth
                                  sx={{
                                    background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.light})`
                                  }}
                                >
                                  Написать
                                </Button>
                              </CardActions>
                            </Card>
                          </Grid>
                        );
                      })}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}

              {/* Call to action */}
              {likes.length > 0 && (
                <Alert severity="success" sx={{ mt: 4 }}>
                  <Typography variant="body2">
                    🎉 <strong>Поздравляем!</strong> У вас есть взаимные лайки! 
                    Не теряйте время - начните общение и найдите свою любовь.
                  </Typography>
                </Alert>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Context menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedLike && handleViewProfile(getOtherUserId(selectedLike))}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Посмотреть профиль
          </MenuItem>
          <MenuItem onClick={() => selectedLike && handleStartChat(selectedLike)}>
            <MessageIcon sx={{ mr: 1 }} />
            Написать сообщение
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default MutualLikesPage;
