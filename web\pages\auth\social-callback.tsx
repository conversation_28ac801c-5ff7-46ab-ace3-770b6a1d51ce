import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Stack,
  Button
} from '@mui/material';
import { CheckCircle, Error, Security } from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { handleSocialCallback } from '../../src/services/authService';

const SocialCallbackPage: React.FC = () => {
  const router = useRouter();
  const { updateUser } = useAuth();
  const { provider, code, state, error } = router.query;
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'requires_2fa'>('loading');
  const [message, setMessage] = useState('');
  const [userEmail, setUserEmail] = useState('');

  useEffect(() => {
    if (router.isReady) {
      handleSocialCallback();
    }
  }, [router.isReady, provider, code, state, error]);

  const handleSocialCallback = async () => {
    // Проверяем наличие ошибки в URL
    if (error) {
      setStatus('error');
      setMessage(getErrorMessage(error as string));
      return;
    }

    // Проверяем обязательные параметры
    if (!provider || !code) {
      setStatus('error');
      setMessage('Неверные параметры авторизации');
      return;
    }

    try {
      setStatus('loading');
      
      const response = await axios.post('/api/auth/social/callback', {
        provider,
        code,
        state
      });

      const { success, requires2FA, user, accessToken, refreshToken } = response.data;

      if (requires2FA) {
        setStatus('requires_2fa');
        setUserEmail(user?.email);
        return;
      }

      if (success && accessToken) {
        // Сохраняем токены
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
        
        // Устанавливаем заголовок авторизации
        axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        
        // Обновляем пользователя в контексте
        updateUser(user);
        
        setStatus('success');
        setMessage('Авторизация прошла успешно!');
        
        // Переход на главную страницу через 2 секунды
        setTimeout(() => {
          router.push('/discover');
        }, 2000);
      } else {
        setStatus('error');
        setMessage('Ошибка авторизации');
      }
    } catch (error: any) {
      setStatus('error');
      setMessage(error.response?.data?.message || 'Произошла ошибка при авторизации');
    }
  };

  const getErrorMessage = (error: string) => {
    switch (error) {
      case 'access_denied':
        return 'Доступ запрещен. Авторизация была отменена.';
      case 'invalid_request':
        return 'Неверный запрос авторизации.';
      case 'unauthorized_client':
        return 'Неавторизованный клиент.';
      case 'unsupported_response_type':
        return 'Неподдерживаемый тип ответа.';
      case 'invalid_scope':
        return 'Неверная область доступа.';
      case 'server_error':
        return 'Ошибка сервера авторизации.';
      case 'temporarily_unavailable':
        return 'Сервис временно недоступен.';
      default:
        return `Ошибка авторизации: ${error}`;
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'google':
        return 'Google';
      case 'vk':
        return 'ВКонтакте';
      case 'yandex':
        return 'Яндекс';
      case 'sber':
        return 'Сбер ID';
      case 'gosuslugi':
        return 'Госуслуги';
      default:
        return provider;
    }
  };

  const redirectTo2FA = () => {
    router.push(`/auth/2fa-verify?email=${userEmail}&returnUrl=/discover`);
  };

  const retryAuth = () => {
    router.push('/auth/login');
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <Stack spacing={3} alignItems="center">
            <CircularProgress size={60} />
            <Typography variant="h6">
              Завершение авторизации...
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Обрабатываем данные от {getProviderName(provider as string)}
            </Typography>
          </Stack>
        );

      case 'success':
        return (
          <Stack spacing={3} alignItems="center">
            <CheckCircle color="success" sx={{ fontSize: 80 }} />
            <Typography variant="h5" color="success.main">
              Авторизация успешна!
            </Typography>
            <Typography variant="body1" textAlign="center">
              Вы успешно авторизовались через {getProviderName(provider as string)}. 
              Переходим в приложение...
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/discover')}
            >
              Перейти в приложение
            </Button>
          </Stack>
        );

      case 'requires_2fa':
        return (
          <Stack spacing={3} alignItems="center">
            <Security color="primary" sx={{ fontSize: 80 }} />
            <Typography variant="h6">
              Требуется двухфакторная аутентификация
            </Typography>
            <Typography variant="body1" textAlign="center">
              Для завершения входа необходимо ввести код из приложения-аутентификатора.
            </Typography>
            <Button
              variant="contained"
              onClick={redirectTo2FA}
              startIcon={<Security />}
            >
              Ввести код 2FA
            </Button>
          </Stack>
        );

      case 'error':
        return (
          <Stack spacing={3} alignItems="center">
            <Error color="error" sx={{ fontSize: 80 }} />
            <Typography variant="h6" color="error">
              Ошибка авторизации
            </Typography>
            <Alert severity="error" sx={{ width: '100%' }}>
              {message}
            </Alert>
            <Typography variant="body2" textAlign="center">
              Попробуйте войти другим способом или обратитесь в поддержку.
            </Typography>
            <Stack direction="row" spacing={2}>
              <Button
                variant="contained"
                onClick={retryAuth}
              >
                Попробовать снова
              </Button>
              <Button
                variant="outlined"
                onClick={() => router.push('/help/contact')}
              >
                Связаться с поддержкой
              </Button>
            </Stack>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Социальная авторизация - Likes & Love</title>
        <meta
          name="description"
          content="Завершение авторизации через социальные сети в приложении знакомств Likes & Love"
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Typography variant="h4" align="center" gutterBottom>
                Социальная авторизация
              </Typography>

              {renderContent()}

              {status !== 'loading' && (
                <Box sx={{ mt: 4, textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    Проблемы с авторизацией? Попробуйте очистить cookies браузера
                    или воспользуйтесь другим способом входа.
                  </Typography>
                </Box>
              )}
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SocialCallbackPage;
