import { ReactNode } from 'react';
import { Box, Container, Stepper, Step, StepLabel, Paper } from '@mui/material';
import { OnboardingSteps } from '@/constants/onboarding';
import { useAuthContext } from '@/contexts/AuthContext';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

interface OnboardingLayoutProps {
  children: ReactNode;
  activeStep: number;
}

export default function OnboardingLayout({ children, activeStep }: OnboardingLayoutProps) {
  const { user, loading } = useAuthContext();
  const router = useRouter();
  
  // Защита роута - только для авторизованных пользователей
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login?redirect=' + encodeURIComponent(router.asPath));
    }
  }, [user, loading, router]);
  
  if (loading || !user) {
    return null; // Или показать лоадер
  }
  
  return (
    <Box
      sx={{
        minHeight: '100vh',
        py: 4,
        backgroundColor: (theme) => theme.palette.grey[50]
      }}
    >
      <Container maxWidth="lg">
        <Paper 
          elevation={1} 
          sx={{ 
            p: 3, 
            mb: 4, 
            borderRadius: 2 
          }}
        >
          <Stepper activeStep={activeStep} alternativeLabel>
            {OnboardingSteps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>
        
        {children}
      </Container>
    </Box>
  );
}