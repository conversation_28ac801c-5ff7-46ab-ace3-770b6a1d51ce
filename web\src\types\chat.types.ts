export interface ChatUser {
  id: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  isOnline: boolean;
  lastActiveAt: string;
  verificationStatus: {
    phone: boolean;
    email: boolean;
    photo: boolean;
    document: boolean;
  };
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  text?: string;
  type: 'text' | 'image' | 'voice' | 'video' | 'file' | 'location' | 'sticker' | 'gif' | 'system';
  attachments?: MessageAttachment[];
  sentAt: string;
  deliveredAt?: string;
  readAt?: string;
  isEdited: boolean;
  editedAt?: string;
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
  };
  reactions?: MessageReaction[];
  metadata?: {
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
    voiceDuration?: number;
    videoDuration?: number;
    fileSize?: number;
    fileName?: string;
  };
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'voice' | 'video' | 'file';
  url: string;
  thumbnailUrl?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number;
  width?: number;
  height?: number;
}

export interface MessageReaction {
  id: string;
  userId: string;
  emoji: string;
  createdAt: string;
}

export interface Conversation {
  id: string;
  type: 'private' | 'group';
  participants: ChatUser[];
  lastMessage?: ChatMessage;
  lastMessageAt?: string;
  unreadCount: number;
  isArchived: boolean;
  isMuted: boolean;
  isPinned: boolean;
  createdAt: string;
  updatedAt: string;
  metadata?: {
    groupName?: string;
    groupDescription?: string;
    groupAvatarUrl?: string;
    groupAdminIds?: string[];
    isEncrypted?: boolean;
    expiresAt?: string;
  };
}

export interface SendMessageRequest {
  conversationId: string;
  text?: string;
  type: 'text' | 'image' | 'voice' | 'video' | 'file' | 'location' | 'sticker' | 'gif';
  attachments?: File[];
  replyToMessageId?: string;
  metadata?: {
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  };
}

export interface SendMessageResponse {
  success: boolean;
  message?: ChatMessage;
  error?: string;
}

export interface CreateConversationRequest {
  participantIds: string[];
  type: 'private' | 'group';
  groupName?: string;
  groupDescription?: string;
  initialMessage?: string;
}

export interface CreateConversationResponse {
  success: boolean;
  conversation?: Conversation;
  error?: string;
}

export interface CallSession {
  id: string;
  conversationId: string;
  type: 'audio' | 'video';
  initiatorId: string;
  participantIds: string[];
  status: 'initiating' | 'ringing' | 'active' | 'ended' | 'declined' | 'missed';
  startedAt?: string;
  endedAt?: string;
  duration?: number;
  quality?: {
    audioQuality: number;
    videoQuality: number;
    connectionStability: number;
  };
}

export interface CallRequest {
  conversationId: string;
  type: 'audio' | 'video';
  participantIds: string[];
}

export interface CallResponse {
  success: boolean;
  session?: CallSession;
  error?: string;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  isTyping: boolean;
  timestamp: string;
}

export interface OnlineStatus {
  userId: string;
  isOnline: boolean;
  lastActiveAt: string;
}

export interface ChatSettings {
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    preview: boolean;
  };
  privacy: {
    readReceipts: boolean;
    lastSeen: boolean;
    onlineStatus: boolean;
  };
  appearance: {
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    bubbleStyle: 'modern' | 'classic';
  };
  security: {
    encryptionEnabled: boolean;
    autoDeleteMessages: boolean;
    autoDeleteDuration: number; // in days
  };
}

export interface ChatFilter {
  type?: 'all' | 'unread' | 'archived' | 'pinned' | 'muted';
  searchQuery?: string;
  dateFrom?: string;
  dateTo?: string;
  participantId?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ChatContextType {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: ChatMessage[];
  loading: boolean;
  error: string | null;
  typingUsers: TypingIndicator[];
  onlineUsers: OnlineStatus[];
  
  // Actions
  loadConversations: (filter?: ChatFilter) => Promise<void>;
  loadMessages: (conversationId: string, page?: number) => Promise<void>;
  sendMessage: (request: SendMessageRequest) => Promise<SendMessageResponse>;
  createConversation: (request: CreateConversationRequest) => Promise<CreateConversationResponse>;
  markAsRead: (conversationId: string, messageIds: string[]) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  editMessage: (messageId: string, newText: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, reactionId: string) => Promise<void>;
  archiveConversation: (conversationId: string) => Promise<void>;
  unarchiveConversation: (conversationId: string) => Promise<void>;
  muteConversation: (conversationId: string, duration?: number) => Promise<void>;
  unmuteConversation: (conversationId: string) => Promise<void>;
  pinConversation: (conversationId: string) => Promise<void>;
  unpinConversation: (conversationId: string) => Promise<void>;
  blockUser: (userId: string) => Promise<void>;
  unblockUser: (userId: string) => Promise<void>;
  reportConversation: (conversationId: string, reason: string) => Promise<void>;
  
  // Real-time events
  setTyping: (conversationId: string, isTyping: boolean) => void;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  
  // Call functionality
  initiateCall: (request: CallRequest) => Promise<CallResponse>;
  acceptCall: (sessionId: string) => Promise<void>;
  declineCall: (sessionId: string) => Promise<void>;
  endCall: (sessionId: string) => Promise<void>;
}

export interface GroupChatInfo {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  participantCount: number;
  adminIds: string[];
  createdAt: string;
  settings: {
    isPublic: boolean;
    allowInvites: boolean;
    requireApproval: boolean;
    maxParticipants: number;
  };
}

export interface GroupChatMember {
  user: ChatUser;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: string;
  permissions: {
    canSendMessages: boolean;
    canSendMedia: boolean;
    canAddMembers: boolean;
    canRemoveMembers: boolean;
    canEditGroup: boolean;
  };
}

export interface VoiceNote {
  id: string;
  url: string;
  duration: number;
  waveform: number[];
  transcription?: string;
}

export interface Sticker {
  id: string;
  url: string;
  packId: string;
  packName: string;
  keywords: string[];
}

export interface StickerPack {
  id: string;
  name: string;
  description: string;
  thumbnailUrl: string;
  stickers: Sticker[];
  isPremium: boolean;
  isOwned: boolean;
}

export interface ChatStatistics {
  totalConversations: number;
  totalMessages: number;
  unreadConversations: number;
  unreadMessages: number;
  averageResponseTime: number;
  mostActiveConversation: {
    conversationId: string;
    messageCount: number;
  };
  dailyMessageCount: {
    date: string;
    count: number;
  }[];
  topContacts: {
    userId: string;
    messageCount: number;
  }[];
}
