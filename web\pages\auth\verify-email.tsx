import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Stack,
  Link
} from '@mui/material';
import { CheckCircle, Email, Refresh } from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { verifyEmailWithData } from '../../src/services/authService';

const VerifyEmailPage: React.FC = () => {
  const router = useRouter();
  const { token, email } = router.query;
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading');
  const [message, setMessage] = useState('');
  const [resending, setResending] = useState(false);

  useEffect(() => {
    if (token) {
      verifyEmail();
    } else if (!token && !email) {
      setStatus('error');
      setMessage('Неверная ссылка для подтверждения');
    } else {
      setStatus('loading');
      setMessage('Ожидание подтверждения email');
    }
  }, [token, email]);

  const verifyEmail = async () => {
    try {
      setStatus('loading');
      const result = await verifyEmailWithData({ token: token as string });

      if (result.success) {
        setStatus('success');
        setMessage(result.message);

        // Автоматический переход через 3 секунды
        setTimeout(() => {
          router.push('/auth/login?verified=true');
        }, 3000);
      } else {
        setStatus('error');
        setMessage(result.message);
      }
    } catch (error: any) {
      setStatus('error');
      setMessage('Произошла ошибка при подтверждении email');
    }
  };

  const resendVerification = async () => {
    if (!email) return;
    
    try {
      setResending(true);
      await axios.post('/api/auth/resend-verification', { email });
      setMessage('Новая ссылка для подтверждения отправлена на ваш email');
    } catch (error: any) {
      setMessage(error.response?.data?.message || 'Ошибка отправки письма');
    } finally {
      setResending(false);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <Stack spacing={3} alignItems="center">
            <CircularProgress size={60} />
            <Typography variant="h6">
              {token ? 'Подтверждение email...' : 'Проверьте вашу почту'}
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              {token 
                ? 'Пожалуйста, подождите...' 
                : `Мы отправили письмо с подтверждением на ${email || 'ваш email'}`
              }
            </Typography>
            {!token && email && (
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={resendVerification}
                disabled={resending}
              >
                {resending ? 'Отправка...' : 'Отправить повторно'}
              </Button>
            )}
          </Stack>
        );

      case 'success':
        return (
          <Stack spacing={3} alignItems="center">
            <CheckCircle color="success" sx={{ fontSize: 80 }} />
            <Typography variant="h5" color="success.main">
              Email подтвержден!
            </Typography>
            <Typography variant="body1" textAlign="center">
              Ваш email успешно подтвержден. Вы будете перенаправлены в профиль через несколько секунд.
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/profile')}
            >
              Перейти в профиль
            </Button>
          </Stack>
        );

      case 'expired':
        return (
          <Stack spacing={3} alignItems="center">
            <Email color="warning" sx={{ fontSize: 80 }} />
            <Typography variant="h6" color="warning.main">
              Ссылка истекла
            </Typography>
            <Typography variant="body2" textAlign="center">
              Ссылка для подтверждения email истекла. Запросите новую ссылку.
            </Typography>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={resendVerification}
              disabled={resending || !email}
            >
              {resending ? 'Отправка...' : 'Получить новую ссылку'}
            </Button>
          </Stack>
        );

      case 'error':
        return (
          <Stack spacing={3} alignItems="center">
            <Alert severity="error" sx={{ width: '100%' }}>
              {message}
            </Alert>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" gutterBottom>
                Возникли проблемы с подтверждением?
              </Typography>
              <Link href="/help/contact" variant="body2">
                Связаться с поддержкой
              </Link>
            </Box>
            <Button
              variant="outlined"
              onClick={() => router.push('/auth/login')}
            >
              Вернуться к входу
            </Button>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Подтверждение Email - Likes & Love</title>
        <meta
          name="description"
          content="Подтверждение адреса электронной почты для активации аккаунта в приложении знакомств Likes & Love"
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/auth/verify-email" />
      </Head>

      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Typography variant="h4" align="center" gutterBottom>
                Подтверждение Email
              </Typography>

              {renderContent()}

              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Не получили письмо? Проверьте папку "Спам" или обратитесь в поддержку
                </Typography>
              </Box>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default VerifyEmailPage;
