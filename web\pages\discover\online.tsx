import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Badge,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Refresh as RefreshIcon,
  Favorite as LikeIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  Circle as OnlineIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getOnlineUsers,
  swipeUser
} from '../../src/services/discoverService';
import { 
  OnlineUser,
  SwipeAction 
} from '../../src/types/discover.types';

const OnlineUsersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadOnlineUsers();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadOnlineUsers, 30000);
    return () => clearInterval(interval);
  }, [user, router]);

  const loadOnlineUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const users = await getOnlineUsers();
      setOnlineUsers(users);
    } catch (err: any) {
      setError('Ошибка загрузки пользователей онлайн');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (userId: string) => {
    try {
      setActionLoading(userId);
      setError(null);

      const swipeAction: SwipeAction = {
        userId,
        action: 'like',
        timestamp: new Date().toISOString()
      };

      const result = await swipeUser(swipeAction);
      
      if (result.success) {
        if (result.isMatch) {
          setSuccess('Это совпадение! 🎉');
        } else {
          setSuccess('Лайк отправлен');
        }
        
        // Remove user from list
        setOnlineUsers(prev => prev.filter(u => u.user.id !== userId));
      } else {
        setError(result.error || 'Ошибка отправки лайка');
      }
    } catch (err: any) {
      setError('Ошибка отправки лайка');
    } finally {
      setActionLoading(null);
    }
  };

  const formatOnlineTime = (dateString: string) => {
    const now = new Date();
    const onlineSince = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - onlineSince.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Только что';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} мин. онлайн`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return `${diffInHours} ч. онлайн`;
    }
  };

  const getActivityColor = (activity: OnlineUser['activity']) => {
    switch (activity) {
      case 'browsing':
        return 'success';
      case 'chatting':
        return 'primary';
      case 'updating_profile':
        return 'warning';
      case 'idle':
        return 'default';
      default:
        return 'default';
    }
  };

  const getActivityLabel = (activity: OnlineUser['activity']) => {
    switch (activity) {
      case 'browsing':
        return 'Просматривает анкеты';
      case 'chatting':
        return 'В чате';
      case 'updating_profile':
        return 'Обновляет профиль';
      case 'idle':
        return 'Неактивен';
      default:
        return 'Онлайн';
    }
  };

  const getResponseTimeColor = (responseTime: OnlineUser['responseTime']) => {
    switch (responseTime) {
      case 'very_fast':
        return 'success';
      case 'fast':
        return 'primary';
      case 'moderate':
        return 'warning';
      case 'slow':
        return 'error';
      default:
        return 'default';
    }
  };

  const getResponseTimeLabel = (responseTime: OnlineUser['responseTime']) => {
    switch (responseTime) {
      case 'very_fast':
        return 'Отвечает мгновенно';
      case 'fast':
        return 'Отвечает быстро';
      case 'moderate':
        return 'Отвечает умеренно';
      case 'slow':
        return 'Отвечает медленно';
      default:
        return '';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Пользователи онлайн - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите людей, которые сейчас онлайн в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/discover/online" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <Badge
                  badgeContent={onlineUsers.length}
                  color="success"
                  sx={{ mr: 2 }}
                >
                  <OnlineIcon color="success" />
                </Badge>
                Пользователи онлайн
              </Typography>
              <IconButton onClick={loadOnlineUsers} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка пользователей онлайн...
                </Typography>
              </Box>
            ) : onlineUsers.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <OnlineIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Сейчас никого нет онлайн
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Зайдите позже или попробуйте обновить страницу
                </Typography>
                <Button
                  variant="contained"
                  onClick={loadOnlineUsers}
                >
                  Обновить
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Сейчас онлайн: {onlineUsers.length} {onlineUsers.length === 1 ? 'человек' : 'людей'}
                  </Typography>
                  
                  <Grid container spacing={3}>
                    {onlineUsers.map((onlineUser) => (
                      <Grid item xs={12} sm={6} md={4} key={onlineUser.user.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ position: 'relative' }}>
                            {/* Online Badge */}
                            <Box sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<OnlineIcon />}
                                label="Онлайн"
                                size="small"
                                color="success"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>

                            {/* Verification Badge */}
                            {onlineUser.user.verificationStatus.phone && (
                              <Box sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                zIndex: 1,
                                backgroundColor: 'primary.main',
                                borderRadius: '50%',
                                p: 0.5
                              }}>
                                <VerifiedIcon sx={{ color: 'white', fontSize: 16 }} />
                              </Box>
                            )}

                            <CardMedia
                              component="img"
                              height="250"
                              image={onlineUser.user.photos[0]?.url || '/default-avatar.png'}
                              alt={onlineUser.user.firstName}
                              sx={{ objectFit: 'cover' }}
                            />
                          </Box>

                          <CardContent sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" gutterBottom>
                              {onlineUser.user.firstName}, {onlineUser.user.age}
                            </Typography>

                            {onlineUser.user.location && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {onlineUser.user.location.city}
                                  {onlineUser.user.location.distance && ` • ${onlineUser.user.location.distance} км`}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary">
                                {formatOnlineTime(onlineUser.onlineSince)}
                              </Typography>
                            </Box>

                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                              <Chip
                                label={getActivityLabel(onlineUser.activity)}
                                size="small"
                                color={getActivityColor(onlineUser.activity) as any}
                                variant="outlined"
                              />
                              
                              <Chip
                                label={getResponseTimeLabel(onlineUser.responseTime)}
                                size="small"
                                color={getResponseTimeColor(onlineUser.responseTime) as any}
                                variant="outlined"
                              />
                            </Box>

                            {onlineUser.user.occupation && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {onlineUser.user.occupation}
                                </Typography>
                              </Box>
                            )}

                            {onlineUser.user.education && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {onlineUser.user.education}
                                </Typography>
                              </Box>
                            )}

                            {onlineUser.user.bio && (
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                {onlineUser.user.bio.length > 100 
                                  ? `${onlineUser.user.bio.substring(0, 100)}...`
                                  : onlineUser.user.bio
                                }
                              </Typography>
                            )}

                            {onlineUser.user.interests.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {onlineUser.user.interests.slice(0, 3).map((interest, index) => (
                                  <Chip
                                    key={index}
                                    label={interest}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                                {onlineUser.user.interests.length > 3 && (
                                  <Chip
                                    label={`+${onlineUser.user.interests.length - 3}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            )}
                          </CardContent>

                          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                            <Button
                              variant="outlined"
                              onClick={() => router.push(`/users/${onlineUser.user.id}`)}
                              size="small"
                            >
                              Профиль
                            </Button>
                            
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                color="primary"
                                onClick={() => handleLike(onlineUser.user.id)}
                                disabled={actionLoading === onlineUser.user.id}
                                sx={{
                                  backgroundColor: 'error.light',
                                  color: 'error.dark',
                                  '&:hover': { backgroundColor: 'error.main', color: 'white' }
                                }}
                              >
                                {actionLoading === onlineUser.user.id ? (
                                  <CircularProgress size={20} />
                                ) : (
                                  <LikeIcon />
                                )}
                              </IconButton>
                              
                              <IconButton
                                color="primary"
                                onClick={() => router.push(`/chat/new?userId=${onlineUser.user.id}`)}
                              >
                                <MessageIcon />
                              </IconButton>
                            </Box>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default OnlineUsersPage;
