import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  LinearProgress
} from '@mui/material';
import {
  ArrowBack,
  ArrowFor<PERSON>,
  Person as PersonIcon,
  Cake as CakeIcon,
  Wc as GenderIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для формы базовой информации
interface BasicInfoForm {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  bio: string;
}

// Схема валидации
const basicInfoSchema = yup.object({
  firstName: yup.string()
    .required('Имя обязательно')
    .min(2, 'Минимум 2 символа')
    .max(50, 'Максимум 50 символов')
    .matches(/^[а-яёА-ЯЁa-zA-Z\s-]+$/, 'Только буквы, пробелы и дефисы'),
  lastName: yup.string()
    .required('Фамилия обязательна')
    .min(2, 'Минимум 2 символа')
    .max(50, 'Максимум 50 символов')
    .matches(/^[а-яёА-ЯЁa-zA-Z\s-]+$/, 'Только буквы, пробелы и дефисы'),
  dateOfBirth: yup.string()
    .required('Дата рождения обязательна')
    .test('age', 'Вам должно быть от 18 до 100 лет', function(value) {
      if (!value) return false;
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      return age >= 18 && age <= 100;
    }),
  gender: yup.string()
    .required('Выберите пол')
    .oneOf(['male', 'female', 'other'], 'Некорректное значение'),
  bio: yup.string()
    .max(500, 'Максимум 500 символов')
    .optional()
});

const OnboardingStep1Page: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch
  } = useForm<BasicInfoForm>({
    resolver: yupResolver(basicInfoSchema),
    mode: 'onChange',
    defaultValues: {
      firstName: user?.profile?.firstName || '',
      lastName: user?.profile?.lastName || '',
      dateOfBirth: user?.profile?.dateOfBirth || '',
      gender: user?.profile?.gender || 'male',
      bio: user?.profile?.bio || ''
    }
  });

  const watchedFields = watch();
  const progress = Object.values(watchedFields).filter(value => value && value.toString().trim() !== '').length;
  const totalFields = 5;
  const progressPercentage = (progress / totalFields) * 100;

  // Шаги онбординга
  const steps = [
    'Основная информация',
    'Интересы и хобби',
    'Фотографии',
    'Предпочтения'
  ];

  const handleFormSubmit = async (data: BasicInfoForm) => {
    try {
      setLoading(true);
      setError(null);

      // Обновляем профиль пользователя
      await updateProfile({
        profile: {
          ...user?.profile,
          firstName: data.firstName,
          lastName: data.lastName,
          dateOfBirth: data.dateOfBirth,
          gender: data.gender,
          bio: data.bio,
          displayName: `${data.firstName} ${data.lastName}`
        }
      });

      // Переходим к следующему шагу
      router.push('/onboarding/step-2-interests');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении данных');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/onboarding/welcome');
  };

  const handleSkip = () => {
    router.push('/onboarding/step-2-interests');
  };

  // Вычисляем возраст для отображения
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const age = calculateAge(watchedFields.dateOfBirth);

  return (
    <>
      <Head>
        <title>Основная информация - Likes & Love</title>
        <meta name="description" content="Расскажите о себе - добавьте основную информацию в ваш профиль знакомств" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Онбординг">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Прогресс */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" color="text.secondary">
                  Шаг 1 из 4
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {Math.round(progressPercentage)}% заполнено
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={progressPercentage} 
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>

            {/* Stepper */}
            <Stepper activeStep={0} sx={{ mb: 4 }} orientation={isMobile ? 'vertical' : 'horizontal'}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Основная форма */}
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <PersonIcon color="primary" sx={{ fontSize: 48, mb: 2 }} />
                  <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                    Расскажите о себе
                  </Typography>
                  <Typography variant="h6" color="text.secondary">
                    Эта информация поможет другим пользователям лучше вас узнать
                  </Typography>
                </Box>

                {error && (
                  <Fade in={!!error}>
                    <Alert severity="error" sx={{ mb: 3 }}>
                      {error}
                    </Alert>
                  </Fade>
                )}

                <form onSubmit={handleSubmit(handleFormSubmit)}>
                  <Grid container spacing={3}>
                    {/* Имя */}
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="firstName"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Имя"
                            fullWidth
                            error={!!errors.firstName}
                            helperText={errors.firstName?.message}
                            InputProps={{
                              startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                          />
                        )}
                      />
                    </Grid>

                    {/* Фамилия */}
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="lastName"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Фамилия"
                            fullWidth
                            error={!!errors.lastName}
                            helperText={errors.lastName?.message}
                          />
                        )}
                      />
                    </Grid>

                    {/* Дата рождения */}
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="dateOfBirth"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Дата рождения"
                            type="date"
                            fullWidth
                            error={!!errors.dateOfBirth}
                            helperText={errors.dateOfBirth?.message || (age ? `Возраст: ${age} лет` : '')}
                            InputLabelProps={{ shrink: true }}
                            InputProps={{
                              startAdornment: <CakeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                            inputProps={{
                              max: new Date(new Date().setFullYear(new Date().getFullYear() - 18)).toISOString().split('T')[0],
                              min: new Date(new Date().setFullYear(new Date().getFullYear() - 100)).toISOString().split('T')[0]
                            }}
                          />
                        )}
                      />
                    </Grid>

                    {/* Пол */}
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="gender"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth error={!!errors.gender}>
                            <InputLabel>Пол</InputLabel>
                            <Select
                              {...field}
                              label="Пол"
                              startAdornment={<GenderIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                            >
                              <MenuItem value="male">Мужской</MenuItem>
                              <MenuItem value="female">Женский</MenuItem>
                              <MenuItem value="other">Другой</MenuItem>
                            </Select>
                            {errors.gender && (
                              <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                                {errors.gender.message}
                              </Typography>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>

                    {/* О себе */}
                    <Grid item xs={12}>
                      <Controller
                        name="bio"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="О себе (необязательно)"
                            multiline
                            rows={4}
                            fullWidth
                            error={!!errors.bio}
                            helperText={errors.bio?.message || `${field.value?.length || 0}/500 символов`}
                            placeholder="Расскажите немного о себе, своих интересах и том, что вас вдохновляет..."
                          />
                        )}
                      />
                    </Grid>

                    {/* Кнопки навигации */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                        <Button
                          onClick={handleBack}
                          startIcon={<ArrowBack />}
                          disabled={loading}
                        >
                          Назад
                        </Button>

                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Button
                            onClick={handleSkip}
                            disabled={loading}
                          >
                            Пропустить
                          </Button>
                          <Button
                            type="submit"
                            variant="contained"
                            endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
                            disabled={loading || !isValid}
                          >
                            {loading ? 'Сохранение...' : 'Продолжить'}
                          </Button>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </form>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default OnboardingStep1Page;
