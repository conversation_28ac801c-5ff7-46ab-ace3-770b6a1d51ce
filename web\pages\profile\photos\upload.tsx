import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardActions,
  IconButton,
  Alert,
  CircularProgress,
  LinearProgress,
  FormControlLabel,
  Switch,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  CloudUpload,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  PhotoCamera,
  CheckCircle
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';
import { 
  getProfilePhotos, 
  uploadProfilePhoto 
} from '../../../src/services/profileService';
import { ProfilePhoto, PhotoUploadData } from '../../../src/types/profile.types';

interface UploadingPhoto {
  id: string;
  file: File;
  preview: string;
  isMain: boolean;
  progress: number;
  uploaded: boolean;
  error?: string;
}

const PhotoUploadPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [existingPhotos, setExistingPhotos] = useState<ProfilePhoto[]>([]);
  const [uploadingPhotos, setUploadingPhotos] = useState<UploadingPhoto[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const maxPhotos = 9;
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadExistingPhotos();
  }, [user, router]);

  const loadExistingPhotos = async () => {
    try {
      setLoading(true);
      const photos = await getProfilePhotos();
      setExistingPhotos(photos);
    } catch (err: any) {
      setError('Ошибка загрузки существующих фотографий');
    } finally {
      setLoading(false);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setError(null);
    setSuccess(null);

    // Проверяем лимиты
    const totalPhotos = existingPhotos.length + uploadingPhotos.length + acceptedFiles.length;
    if (totalPhotos > maxPhotos) {
      setError(`Максимум ${maxPhotos} фотографий. У вас уже ${existingPhotos.length + uploadingPhotos.length} фото.`);
      return;
    }

    // Обрабатываем отклоненные файлы
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => {
        const errorMessages = errors.map((e: any) => {
          switch (e.code) {
            case 'file-too-large':
              return `${file.name}: файл слишком большой (максимум 10MB)`;
            case 'file-invalid-type':
              return `${file.name}: неподдерживаемый формат`;
            default:
              return `${file.name}: ${e.message}`;
          }
        });
        return errorMessages.join(', ');
      });
      setError(errors.join('; '));
    }

    // Добавляем принятые файлы
    const newUploadingPhotos: UploadingPhoto[] = acceptedFiles.map((file, index) => ({
      id: `uploading-${Date.now()}-${index}`,
      file,
      preview: URL.createObjectURL(file),
      isMain: existingPhotos.length === 0 && uploadingPhotos.length === 0 && index === 0,
      progress: 0,
      uploaded: false
    }));

    setUploadingPhotos(prev => [...prev, ...newUploadingPhotos]);
  }, [existingPhotos.length, uploadingPhotos.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': acceptedFormats.map(format => format.replace('image/', '.'))
    },
    multiple: true,
    maxSize: maxFileSize,
    disabled: uploading || (existingPhotos.length + uploadingPhotos.length) >= maxPhotos
  });

  const removeUploadingPhoto = (photoId: string) => {
    setUploadingPhotos(prev => {
      const updated = prev.filter(p => p.id !== photoId);
      // Если удаляем главную фото, делаем первую оставшуюся главной
      const removedPhoto = prev.find(p => p.id === photoId);
      if (removedPhoto?.isMain && updated.length > 0) {
        updated[0].isMain = true;
      }
      return updated;
    });
  };

  const toggleMainPhoto = (photoId: string) => {
    setUploadingPhotos(prev => prev.map(photo => ({
      ...photo,
      isMain: photo.id === photoId
    })));
  };

  const uploadAllPhotos = async () => {
    if (uploadingPhotos.length === 0) return;

    setUploading(true);
    setError(null);

    for (const photo of uploadingPhotos) {
      if (photo.uploaded) continue;

      try {
        // Обновляем прогресс
        setUploadingPhotos(prev => prev.map(p => 
          p.id === photo.id ? { ...p, progress: 50 } : p
        ));

        const uploadData: PhotoUploadData = {
          file: photo.file,
          isMain: photo.isMain,
          order: existingPhotos.length + uploadingPhotos.findIndex(p => p.id === photo.id)
        };

        const result = await uploadProfilePhoto(uploadData);

        if (result.success) {
          setUploadingPhotos(prev => prev.map(p => 
            p.id === photo.id ? { ...p, progress: 100, uploaded: true } : p
          ));
        } else {
          setUploadingPhotos(prev => prev.map(p => 
            p.id === photo.id ? { ...p, error: result.message } : p
          ));
        }
      } catch (err: any) {
        setUploadingPhotos(prev => prev.map(p => 
          p.id === photo.id ? { ...p, error: 'Ошибка загрузки' } : p
        ));
      }
    }

    const uploadedCount = uploadingPhotos.filter(p => p.uploaded || !p.error).length;
    if (uploadedCount > 0) {
      setSuccess(`Успешно загружено ${uploadedCount} фотографий`);
      setTimeout(() => {
        router.push('/profile/photos');
      }, 2000);
    }

    setUploading(false);
  };

  const canUpload = uploadingPhotos.length > 0 && !uploading;
  const hasErrors = uploadingPhotos.some(p => p.error);

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Загрузить фотографии - Likes & Love</title>
        <meta 
          name="description" 
          content="Загрузите новые фотографии в ваш профиль приложения знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                <IconButton 
                  onClick={() => router.back()}
                  sx={{ mr: 2 }}
                >
                  <ArrowBack />
                </IconButton>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Загрузить фотографии
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Добавьте до {maxPhotos - existingPhotos.length} новых фотографий
                  </Typography>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Upload area */}
                    {(existingPhotos.length + uploadingPhotos.length) < maxPhotos && (
                      <Box
                        {...getRootProps()}
                        sx={{
                          border: `2px dashed ${isDragActive ? theme.palette.primary.main : theme.palette.grey[300]}`,
                          borderRadius: 2,
                          p: 4,
                          textAlign: 'center',
                          cursor: 'pointer',
                          backgroundColor: isDragActive ? theme.palette.primary.light + '10' : 'transparent',
                          transition: 'all 0.2s ease',
                          mb: 4,
                          '&:hover': {
                            borderColor: theme.palette.primary.main,
                            backgroundColor: theme.palette.primary.light + '05'
                          }
                        }}
                      >
                        <input {...getInputProps()} />
                        <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          {isDragActive ? 'Отпустите файлы здесь' : 'Перетащите фото или нажмите для выбора'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Поддерживаются форматы: JPEG, PNG, WebP (до 10MB)
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Можно выбрать несколько файлов одновременно
                        </Typography>
                      </Box>
                    )}

                    {/* Uploading photos preview */}
                    {uploadingPhotos.length > 0 && (
                      <Box sx={{ mb: 4 }}>
                        <Typography variant="h6" gutterBottom>
                          Фотографии для загрузки ({uploadingPhotos.length})
                        </Typography>
                        
                        <Grid container spacing={2}>
                          {uploadingPhotos.map((photo) => (
                            <Grid item xs={6} sm={4} md={3} key={photo.id}>
                              <Card sx={{ position: 'relative' }}>
                                <CardMedia
                                  component="img"
                                  height="200"
                                  image={photo.preview}
                                  alt="Предпросмотр"
                                  sx={{
                                    objectFit: 'cover',
                                    filter: photo.error ? 'grayscale(100%)' : 'none'
                                  }}
                                />

                                {/* Main photo indicator */}
                                <IconButton
                                  sx={{
                                    position: 'absolute',
                                    top: 8,
                                    left: 8,
                                    backgroundColor: 'rgba(0,0,0,0.5)',
                                    color: photo.isMain ? 'gold' : 'white',
                                    '&:hover': {
                                      backgroundColor: 'rgba(0,0,0,0.7)'
                                    }
                                  }}
                                  onClick={() => toggleMainPhoto(photo.id)}
                                  disabled={uploading}
                                >
                                  {photo.isMain ? <StarIcon /> : <StarBorderIcon />}
                                </IconButton>

                                {/* Upload status */}
                                {photo.uploaded && (
                                  <Box sx={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    backgroundColor: 'rgba(76, 175, 80, 0.9)',
                                    borderRadius: '50%',
                                    p: 0.5
                                  }}>
                                    <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
                                  </Box>
                                )}

                                {/* Progress bar */}
                                {uploading && !photo.uploaded && !photo.error && (
                                  <Box sx={{
                                    position: 'absolute',
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    backgroundColor: 'rgba(0,0,0,0.7)',
                                    p: 1
                                  }}>
                                    <LinearProgress 
                                      variant="determinate" 
                                      value={photo.progress}
                                      sx={{ mb: 1 }}
                                    />
                                    <Typography variant="caption" sx={{ color: 'white' }}>
                                      Загрузка... {photo.progress}%
                                    </Typography>
                                  </Box>
                                )}

                                {/* Error indicator */}
                                {photo.error && (
                                  <Box sx={{
                                    position: 'absolute',
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    backgroundColor: 'rgba(244, 67, 54, 0.9)',
                                    p: 1
                                  }}>
                                    <Typography variant="caption" sx={{ color: 'white' }}>
                                      {photo.error}
                                    </Typography>
                                  </Box>
                                )}

                                <CardActions sx={{ justifyContent: 'space-between', p: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    {photo.isMain ? 'Главная' : 'Фото'}
                                  </Typography>
                                  <IconButton
                                    size="small"
                                    onClick={() => removeUploadingPhoto(photo.id)}
                                    disabled={uploading}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </CardActions>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    )}

                    {/* Tips */}
                    <Alert severity="info" sx={{ mb: 4 }}>
                      <Typography variant="body2">
                        💡 <strong>Советы для лучших фотографий:</strong>
                      </Typography>
                      <Box component="ul" sx={{ mt: 1, mb: 0, pl: 2 }}>
                        <li>Используйте качественные фото с хорошим освещением</li>
                        <li>Покажите себя в полный рост и крупным планом</li>
                        <li>Добавьте фото с вашими увлечениями</li>
                        <li>Избегайте групповых фото как главную</li>
                        <li>Улыбайтесь и будьте естественными</li>
                      </Box>
                    </Alert>

                    {/* Action buttons */}
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                      <Button
                        variant="outlined"
                        onClick={() => router.back()}
                        disabled={uploading}
                      >
                        Отмена
                      </Button>
                      
                      <Button
                        variant="contained"
                        onClick={uploadAllPhotos}
                        disabled={!canUpload}
                        startIcon={uploading ? <CircularProgress size={20} /> : <CloudUpload />}
                        sx={{
                          background: canUpload 
                            ? `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                            : undefined
                        }}
                      >
                        {uploading ? 'Загрузка...' : `Загрузить ${uploadingPhotos.length} фото`}
                      </Button>
                    </Box>
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PhotoUploadPage;
