import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Breadcrumbs,
  Link,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  ContactSupport as ContactSupportIcon,
  Chat as ChatIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Schedule as ScheduleIcon,
  Send as SendIcon,
  Home as HomeIcon,
  Help as HelpIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Типы для формы обращения
interface ContactForm {
  name: string;
  email: string;
  category: string;
  subject: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
}

// Схема валидации
const contactSchema = yup.object({
  name: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  email: yup.string().email('Неверный формат email').required('Email обязателен'),
  category: yup.string().required('Выберите категорию'),
  subject: yup.string().required('Тема обязательна').min(5, 'Минимум 5 символов'),
  message: yup.string().required('Сообщение обязательно').min(20, 'Минимум 20 символов'),
  priority: yup.string().oneOf(['low', 'medium', 'high']).required()
});

const ContactPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ContactForm>({
    resolver: yupResolver(contactSchema),
    defaultValues: {
      name: '',
      email: '',
      category: '',
      subject: '',
      message: '',
      priority: 'medium'
    }
  });

  // Категории обращений
  const categories = [
    { value: 'account', label: 'Проблемы с аккаунтом' },
    { value: 'profile', label: 'Профиль и фото' },
    { value: 'matching', label: 'Знакомства и совпадения' },
    { value: 'messaging', label: 'Сообщения и чаты' },
    { value: 'payment', label: 'Платежи и подписка' },
    { value: 'safety', label: 'Безопасность и жалобы' },
    { value: 'technical', label: 'Технические проблемы' },
    { value: 'other', label: 'Другое' }
  ];

  // Способы связи
  const contactMethods = [
    {
      id: 'chat',
      title: 'Онлайн чат',
      description: 'Быстрый ответ в течение 5 минут',
      icon: <ChatIcon />,
      available: true,
      action: () => {
        // Здесь будет открытие чата поддержки
        console.log('Opening support chat');
      }
    },
    {
      id: 'email',
      title: 'Email поддержка',
      description: 'Ответ в течение 24 часов',
      icon: <EmailIcon />,
      available: true,
      action: () => {
        window.location.href = 'mailto:<EMAIL>';
      }
    },
    {
      id: 'phone',
      title: 'Телефон',
      description: 'Пн-Пт 9:00-18:00 МСК',
      icon: <PhoneIcon />,
      available: true,
      action: () => {
        window.location.href = 'tel:+74951234567';
      }
    }
  ];

  const handleFormSubmit = async (data: ContactForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отправки обращения
      // await submitContactForm(data);

      // Симуляция отправки
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSuccess(true);
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке сообщения');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Layout title="Обращение отправлено">
        <Container maxWidth="md">
          <Box sx={{ py: 6, textAlign: 'center' }}>
            <CheckCircleIcon color="success" sx={{ fontSize: 80, mb: 3 }} />
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 2 }}>
              Обращение отправлено!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Мы получили ваше сообщение и ответим в ближайшее время
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button variant="contained" onClick={() => router.push('/help')}>
                Вернуться в центр помощи
              </Button>
              <Button variant="outlined" onClick={() => setSuccess(false)}>
                Отправить еще одно обращение
              </Button>
            </Box>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Связаться с поддержкой - Likes & Love</title>
        <meta name="description" content="Свяжитесь со службой поддержки Likes & Love. Онлайн чат, email и телефон для решения любых вопросов." />
        <meta name="keywords" content="поддержка, контакты, помощь, чат, email" />
      </Head>

      <Layout title="Связаться с поддержкой">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/help" 
                onClick={(e) => { e.preventDefault(); router.push('/help'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HelpIcon fontSize="small" />
                Помощь
              </Link>
              <Typography color="text.primary">Контакты</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <ContactSupportIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Связаться с поддержкой
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Выберите удобный способ связи или отправьте нам сообщение
              </Typography>
            </Box>

            <Grid container spacing={4}>
              {/* Способы связи */}
              <Grid item xs={12} md={4}>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                  Способы связи
                </Typography>

                {contactMethods.map((method) => (
                  <Card key={method.id} sx={{ mb: 2 }}>
                    <CardActionArea onClick={method.action} disabled={!method.available}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          {method.icon}
                          <Typography variant="h6" fontWeight="bold">
                            {method.title}
                          </Typography>
                          {method.available && (
                            <Chip label="Доступно" size="small" color="success" />
                          )}
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {method.description}
                        </Typography>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                ))}

                {/* Время работы */}
                <Card sx={{ mt: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <ScheduleIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Время работы
                      </Typography>
                    </Box>
                    <List dense>
                      <ListItem>
                        <ListItemText 
                          primary="Онлайн чат" 
                          secondary="24/7" 
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText 
                          primary="Email поддержка" 
                          secondary="24/7" 
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText 
                          primary="Телефон" 
                          secondary="Пн-Пт 9:00-18:00 МСК" 
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Форма обращения */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                      Отправить сообщение
                    </Typography>

                    {error && (
                      <Alert severity="error" sx={{ mb: 3 }}>
                        {error}
                      </Alert>
                    )}

                    <form onSubmit={handleSubmit(handleFormSubmit)}>
                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Ваше имя"
                                fullWidth
                                error={!!errors.name}
                                helperText={errors.name?.message}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Email для ответа"
                                type="email"
                                fullWidth
                                error={!!errors.email}
                                helperText={errors.email?.message}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="category"
                            control={control}
                            render={({ field }) => (
                              <FormControl fullWidth error={!!errors.category}>
                                <InputLabel>Категория обращения</InputLabel>
                                <Select {...field} label="Категория обращения">
                                  {categories.map((category) => (
                                    <MenuItem key={category.value} value={category.value}>
                                      {category.label}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {errors.category && (
                                  <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                                    {errors.category.message}
                                  </Typography>
                                )}
                              </FormControl>
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="priority"
                            control={control}
                            render={({ field }) => (
                              <FormControl fullWidth>
                                <InputLabel>Приоритет</InputLabel>
                                <Select {...field} label="Приоритет">
                                  <MenuItem value="low">Низкий</MenuItem>
                                  <MenuItem value="medium">Средний</MenuItem>
                                  <MenuItem value="high">Высокий</MenuItem>
                                </Select>
                              </FormControl>
                            )}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <Controller
                            name="subject"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Тема сообщения"
                                fullWidth
                                error={!!errors.subject}
                                helperText={errors.subject?.message}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <Controller
                            name="message"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Подробное описание проблемы"
                                multiline
                                rows={6}
                                fullWidth
                                error={!!errors.message}
                                helperText={errors.message?.message}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <Alert severity="info" sx={{ mb: 2 }}>
                            <Typography variant="body2">
                              Для более быстрого решения проблемы укажите максимально подробную информацию
                            </Typography>
                          </Alert>

                          <Button
                            type="submit"
                            variant="contained"
                            size="large"
                            fullWidth={isMobile}
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                          >
                            {loading ? 'Отправляем...' : 'Отправить сообщение'}
                          </Button>
                        </Grid>
                      </Grid>
                    </form>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ContactPage;
