import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Alert,
  CircularProgress,
  Stack,
  TextField,
  Link as MuiLink,
  Divider,
  InputAdornment,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  Security, 
  VpnKey, 
  Backup,
  Help as HelpIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Link from 'next/link';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { verifyTwoFactorCode } from '../../src/services/authService';
import { TwoFactorVerificationData } from '../../src/types/auth.types';

const TwoFactorPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { email, returnUrl, required } = router.query;
  
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [backupCode, setBackupCode] = useState('');
  const [useBackupCode, setUseBackupCode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [attempts, setAttempts] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const maxAttempts = 5;
  const isRequired = required === 'true';

  useEffect(() => {
    // Если пользователь не авторизован и 2FA не обязательна, перенаправляем на логин
    if (!user && !isRequired) {
      router.push('/auth/login');
    }
  }, [user, isRequired, router]);

  useEffect(() => {
    // Фокус на первое поле при загрузке
    if (inputRefs.current[0] && !useBackupCode) {
      inputRefs.current[0].focus();
    }
  }, [useBackupCode]);

  const handleCodeChange = (index: number, value: string) => {
    // Разрешаем только цифры
    if (!/^\d*$/.test(value)) return;

    const newCode = [...code];
    newCode[index] = value.slice(-1);
    setCode(newCode);

    // Автоматический переход к следующему полю
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Автоматическая отправка при заполнении всех полей
    if (newCode.every(digit => digit) && newCode.join('').length === 6) {
      handleVerify(newCode.join(''));
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '');
    
    if (pastedData.length === 6) {
      const newCode = pastedData.split('').slice(0, 6);
      setCode(newCode);
      handleVerify(pastedData);
    }
  };

  const handleVerify = async (verificationCode?: string) => {
    const codeToVerify = verificationCode || (useBackupCode ? backupCode : code.join(''));
    
    if (!codeToVerify || (useBackupCode ? codeToVerify.length < 8 : codeToVerify.length !== 6)) {
      setError(useBackupCode ? 'Введите резервный код' : 'Введите 6-значный код');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const verificationData: TwoFactorVerificationData = useBackupCode 
        ? { code: '', backupCode: codeToVerify }
        : { code: codeToVerify };

      const result = await verifyTwoFactorCode(verificationData);
      
      if (result.success) {
        // Успешная верификация
        const redirectUrl = (returnUrl as string) || '/discover';
        router.push(redirectUrl);
      } else {
        throw new Error(result.message);
      }
    } catch (err: any) {
      setAttempts(prev => prev + 1);
      setError(err.message || 'Неверный код. Попробуйте еще раз.');
      
      // Очищаем поля
      if (useBackupCode) {
        setBackupCode('');
      } else {
        setCode(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      }

      // Блокируем после максимального количества попыток
      if (attempts >= maxAttempts - 1) {
        setError('Превышено максимальное количество попыток. Попробуйте позже.');
        setTimeout(() => {
          router.push('/auth/login');
        }, 3000);
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleBackupCode = () => {
    setUseBackupCode(!useBackupCode);
    setError('');
    setCode(['', '', '', '', '', '']);
    setBackupCode('');
  };

  const renderCodeInput = () => {
    if (useBackupCode) {
      return (
        <TextField
          fullWidth
          label="Резервный код"
          value={backupCode}
          onChange={(e) => setBackupCode(e.target.value.replace(/\s/g, ''))}
          placeholder="Введите 8-значный резервный код"
          disabled={loading}
          inputProps={{
            maxLength: 8,
            style: { textAlign: 'center', fontSize: '1.2rem', letterSpacing: '0.1em' }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Backup />
              </InputAdornment>
            )
          }}
        />
      );
    }

    return (
      <Stack direction="row" spacing={1} justifyContent="center">
        {code.map((digit, index) => (
          <TextField
            key={index}
            inputRef={(el) => (inputRefs.current[index] = el)}
            value={digit}
            onChange={(e) => handleCodeChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={index === 0 ? handlePaste : undefined}
            disabled={loading}
            inputProps={{
              maxLength: 1,
              style: { 
                textAlign: 'center', 
                fontSize: '1.5rem',
                fontWeight: 'bold'
              }
            }}
            sx={{ 
              width: 56,
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused': {
                  '& > fieldset': {
                    borderColor: 'primary.main',
                    borderWidth: 2
                  }
                }
              }
            }}
          />
        ))}
      </Stack>
    );
  };

  return (
    <>
      <Head>
        <title>Двухфакторная аутентификация - Likes & Love</title>
        <meta 
          name="description" 
          content="Подтверждение входа с помощью двухфакторной аутентификации в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Security sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                <Typography variant="h4" gutterBottom>
                  Двухфакторная аутентификация
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {useBackupCode 
                    ? 'Введите один из ваших резервных кодов'
                    : 'Введите 6-значный код из приложения аутентификатора'
                  }
                </Typography>
                {email && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    для аккаунта: {email}
                  </Typography>
                )}
              </Box>

              {error && (
                <Alert 
                  severity="error" 
                  sx={{ mb: 3 }}
                  action={
                    attempts < maxAttempts - 1 && (
                      <Tooltip title="Попробовать снова">
                        <IconButton
                          color="inherit"
                          size="small"
                          onClick={() => {
                            setError('');
                            setCode(['', '', '', '', '', '']);
                            setBackupCode('');
                            inputRefs.current[0]?.focus();
                          }}
                        >
                          <RefreshIcon />
                        </IconButton>
                      </Tooltip>
                    )
                  }
                >
                  {error}
                  {attempts > 0 && attempts < maxAttempts && (
                    <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                      Осталось попыток: {maxAttempts - attempts}
                    </Typography>
                  )}
                </Alert>
              )}

              <Box sx={{ mb: 4 }}>
                {renderCodeInput()}
              </Box>

              {!useBackupCode && (
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => handleVerify()}
                  disabled={loading || code.some(digit => !digit)}
                  size="large"
                  sx={{ mb: 3 }}
                  startIcon={loading ? <CircularProgress size={20} /> : <VpnKey />}
                >
                  {loading ? 'Проверка...' : 'Подтвердить'}
                </Button>
              )}

              {useBackupCode && (
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => handleVerify()}
                  disabled={loading || !backupCode || backupCode.length < 8}
                  size="large"
                  sx={{ mb: 3 }}
                  startIcon={loading ? <CircularProgress size={20} /> : <Backup />}
                >
                  {loading ? 'Проверка...' : 'Использовать резервный код'}
                </Button>
              )}

              <Divider sx={{ my: 3 }} />

              <Box sx={{ textAlign: 'center' }}>
                <Button
                  variant="text"
                  onClick={toggleBackupCode}
                  disabled={loading}
                  startIcon={useBackupCode ? <VpnKey /> : <Backup />}
                >
                  {useBackupCode 
                    ? 'Использовать код из приложения' 
                    : 'Использовать резервный код'
                  }
                </Button>
              </Box>

              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Проблемы с входом?{' '}
                  <Link href="/help/2fa" passHref>
                    <MuiLink>
                      Получить помощь
                    </MuiLink>
                  </Link>
                </Typography>
              </Box>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default TwoFactorPage;
