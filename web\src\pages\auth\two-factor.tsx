import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  Typography, 
  Box, 
  CircularProgress, 
  <PERSON>ert, 
  Button,
  TextField,
  Paper,
  Grid
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { verify2FA } from '@/services/authService';
import Layout from '@/components/Layout/Layout';

interface TwoFactorFormData {
  code: string;
}

const schema = yup.object({
  code: yup.string()
    .required('Код подтверждения обязателен')
    .matches(/^\d{6}$/, 'Код должен состоять из 6 цифр')
});

export default function TwoFactor() {
  const router = useRouter();
  const { email } = router.query;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { control, handleSubmit, formState: { errors } } = useForm<TwoFactorFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      code: ''
    }
  });

  const onSubmit = async (data: TwoFactorFormData) => {
    setLoading(true);
    setError('');
    
    try {
      await verify2FA(data.code);
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Ошибка при проверке кода. Пожалуйста, попробуйте снова.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setLoading(true);
    setError('');
    
    try {
      await resend2FACode(email as string);
      // Показать сообщение об успешной отправке
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке кода. Пожалуйста, попробуйте снова.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <Head>
        <title>Двухфакторная аутентификация | Likes & Love</title>
        <meta name="description" content="Двухфакторная аутентификация в приложении Likes & Love" />
      </Head>
      
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: '60vh',
          padding: 3
        }}
      >
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            width: '100%', 
            maxWidth: 500,
            borderRadius: 2
          }}
        >
          <Typography variant="h5" component="h1" gutterBottom align="center">
            Двухфакторная аутентификация
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 3, textAlign: 'center' }}>
            Для продолжения введите код подтверждения, отправленный на ваше устройство
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          
          <form onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="code"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Код подтверждения"
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  error={!!errors.code}
                  helperText={errors.code?.message}
                  inputProps={{ 
                    maxLength: 6,
                    inputMode: 'numeric',
                    pattern: '[0-9]*'
                  }}
                />
              )}
            />
            
            <Button 
              type="submit"
              variant="contained" 
              color="primary"
              fullWidth
              size="large"
              sx={{ mt: 2 }}
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Подтвердить'
              )}
            </Button>
            
            <Grid container justifyContent="center" sx={{ mt: 2 }}>
              <Button 
                variant="text" 
                color="primary"
                onClick={handleResendCode}
                disabled={loading}
              >
                Отправить код повторно
              </Button>
            </Grid>
          </form>
        </Paper>
      </Box>
    </Layout>
  );
}

// Эти функции должны быть добавлены в authService.ts
async function verify2FA(code: string): Promise<any> {
  // Реализация проверки 2FA кода
  const { data } = await axios.post(`${API_URL}/auth/2fa/verify`, { code });
  return data;
}

async function resend2FACode(email: string): Promise<any> {
  // Реализация повторной отправки 2FA кода
  const { data } = await axios.post(`${API_URL}/auth/2fa/resend`, { email });
  return data;
}