export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  avatar?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  role: 'user' | 'admin' | 'moderator';
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  matchPreferences: MatchPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  matches: boolean;
  messages: boolean;
  events: boolean;
}

export interface PrivacyPreferences {
  profileVisibility: 'public' | 'matches' | 'private';
  showOnlineStatus: boolean;
  showLastActive: boolean;
  allowLocationSharing: boolean;
}

export interface MatchPreferences {
  ageRange: [number, number];
  distance: number;
  gender: string[];
  interests: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  birthDate: string;
  gender: string;
  acceptTerms: boolean;
}

export interface VerificationResult {
  loading: boolean;
  success: boolean;
  message: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  socialLogin: (provider: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
  checkAuth: () => Promise<void>;
}