import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  TextField
} from '@mui/material';
import {
  ArrowBack,
  Email as EmailIcon,
  Notifications as NotificationsIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  People as PeopleIcon,
  Event as EventIcon,
  Security as SecurityIcon,
  Campaign as CampaignIcon,
  Save as SaveIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon,
  Unsubscribe as UnsubscribeIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';

// Типы для настроек Email-уведомлений
interface EmailNotificationSettings {
  enabled: boolean;
  matches: boolean;
  messages: boolean;
  likes: boolean;
  weeklyDigest: boolean;
  monthlyReport: boolean;
  events: boolean;
  promotions: boolean;
  security: boolean;
  tips: boolean;
  frequency: 'instant' | 'daily' | 'weekly';
  digestDay: string;
  unsubscribeAll: boolean;
}

// Схема валидации
const emailNotificationSchema = yup.object({
  enabled: yup.boolean(),
  matches: yup.boolean(),
  messages: yup.boolean(),
  likes: yup.boolean(),
  weeklyDigest: yup.boolean(),
  monthlyReport: yup.boolean(),
  events: yup.boolean(),
  promotions: yup.boolean(),
  security: yup.boolean(),
  tips: yup.boolean(),
  frequency: yup.string().oneOf(['instant', 'daily', 'weekly']),
  digestDay: yup.string(),
  unsubscribeAll: yup.boolean()
});

const EmailNotificationsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
    setValue
  } = useForm<EmailNotificationSettings>({
    resolver: yupResolver(emailNotificationSchema),
    defaultValues: {
      enabled: user?.notifications?.email?.enabled ?? true,
      matches: user?.notifications?.email?.matches ?? true,
      messages: user?.notifications?.email?.messages ?? true,
      likes: user?.notifications?.email?.likes ?? false,
      weeklyDigest: user?.notifications?.email?.weeklyDigest ?? true,
      monthlyReport: user?.notifications?.email?.monthlyReport ?? true,
      events: user?.notifications?.email?.events ?? true,
      promotions: user?.notifications?.email?.promotions ?? false,
      security: user?.notifications?.email?.security ?? true,
      tips: user?.notifications?.email?.tips ?? false,
      frequency: user?.notifications?.email?.frequency ?? 'daily',
      digestDay: user?.notifications?.email?.digestDay ?? 'monday',
      unsubscribeAll: false
    }
  });

  const watchedEnabled = watch('enabled');
  const watchedUnsubscribeAll = watch('unsubscribeAll');

  // Категории Email-уведомлений
  const emailCategories = [
    {
      key: 'matches' as keyof EmailNotificationSettings,
      label: 'Новые совпадения',
      description: 'Уведомления о новых взаимных лайках',
      icon: <PeopleIcon />,
      priority: 'high',
      frequency: true
    },
    {
      key: 'messages' as keyof EmailNotificationSettings,
      label: 'Сообщения',
      description: 'Новые сообщения в чатах',
      icon: <MessageIcon />,
      priority: 'high',
      frequency: true
    },
    {
      key: 'likes' as keyof EmailNotificationSettings,
      label: 'Лайки',
      description: 'Когда кто-то лайкает ваш профиль',
      icon: <FavoriteIcon />,
      priority: 'medium',
      frequency: true
    },
    {
      key: 'events' as keyof EmailNotificationSettings,
      label: 'События',
      description: 'Приглашения на события и встречи',
      icon: <EventIcon />,
      priority: 'medium',
      frequency: false
    },
    {
      key: 'security' as keyof EmailNotificationSettings,
      label: 'Безопасность',
      description: 'Важные уведомления о безопасности аккаунта',
      icon: <SecurityIcon />,
      priority: 'critical',
      frequency: false
    },
    {
      key: 'tips' as keyof EmailNotificationSettings,
      label: 'Советы и рекомендации',
      description: 'Полезные советы для знакомств',
      icon: <InfoIcon />,
      priority: 'low',
      frequency: false
    },
    {
      key: 'promotions' as keyof EmailNotificationSettings,
      label: 'Акции и предложения',
      description: 'Специальные предложения и скидки',
      icon: <CampaignIcon />,
      priority: 'low',
      frequency: false
    }
  ];

  // Дайджесты и отчеты
  const digestCategories = [
    {
      key: 'weeklyDigest' as keyof EmailNotificationSettings,
      label: 'Еженедельный дайджест',
      description: 'Сводка активности за неделю',
      icon: <ScheduleIcon />
    },
    {
      key: 'monthlyReport' as keyof EmailNotificationSettings,
      label: 'Месячный отчет',
      description: 'Статистика и достижения за месяц',
      icon: <TrendingUpIcon />
    }
  ];

  // Опции частоты
  const frequencyOptions = [
    { value: 'instant', label: 'Мгновенно', description: 'Получать email сразу' },
    { value: 'daily', label: 'Ежедневно', description: 'Сводка один раз в день' },
    { value: 'weekly', label: 'Еженедельно', description: 'Сводка один раз в неделю' }
  ];

  // Дни недели для дайджеста
  const weekDays = [
    { value: 'monday', label: 'Понедельник' },
    { value: 'tuesday', label: 'Вторник' },
    { value: 'wednesday', label: 'Среда' },
    { value: 'thursday', label: 'Четверг' },
    { value: 'friday', label: 'Пятница' },
    { value: 'saturday', label: 'Суббота' },
    { value: 'sunday', label: 'Воскресенье' }
  ];

  const handleSaveSettings = async (data: EmailNotificationSettings) => {
    try {
      setLoading(true);
      setError(null);

      // Если выбрана полная отписка, отключаем все уведомления
      if (data.unsubscribeAll) {
        data = {
          ...data,
          enabled: false,
          matches: false,
          messages: false,
          likes: false,
          weeklyDigest: false,
          monthlyReport: false,
          events: false,
          promotions: false,
          security: true, // Безопасность всегда остается включенной
          tips: false
        };
      }

      // Здесь будет вызов API для сохранения настроек Email-уведомлений
      // await updateEmailNotificationSettings(data);

      // Обновляем профиль пользователя
      await updateProfile({
        notifications: {
          ...user?.notifications,
          email: data
        }
      });

      if (data.unsubscribeAll) {
        setSuccess('Вы отписались от всех Email-уведомлений (кроме критически важных)');
      } else {
        setSuccess('Настройки Email-уведомлений сохранены');
      }
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleUnsubscribeAll = () => {
    setValue('unsubscribeAll', true);
    // Отключаем все уведомления кроме безопасности
    emailCategories.forEach(category => {
      if (category.key !== 'security') {
        setValue(category.key, false);
      }
    });
    digestCategories.forEach(category => {
      setValue(category.key, false);
    });
    setValue('enabled', false);
  };

  const handleBack = () => {
    router.push('/settings/notifications');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  if (!user) {
    return (
      <Layout title="Email-уведомления">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Email-уведомления - Likes & Love</title>
        <meta name="description" content="Настройки Email-уведомлений в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Email-уведомления">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/notifications" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/notifications'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <NotificationsIcon fontSize="small" />
                Уведомления
              </Link>
              <Typography color="text.primary">Email</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <EmailIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Email-уведомления
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Настройте уведомления на email: {user.email}
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleSaveSettings)}>
              <Grid container spacing={3}>
                {/* Основные настройки */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Основные настройки
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <EmailIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Email-уведомления"
                            secondary="Включить или отключить все Email-уведомления"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="enabled"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled={watchedUnsubscribeAll}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>

                      {watchedEnabled && (
                        <Grid container spacing={2} sx={{ mt: 2 }}>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="frequency"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth>
                                  <InputLabel>Частота уведомлений</InputLabel>
                                  <Select {...field} label="Частота уведомлений">
                                    {frequencyOptions.map((option) => (
                                      <MenuItem key={option.value} value={option.value}>
                                        <Box>
                                          <Typography variant="body1">{option.label}</Typography>
                                          <Typography variant="caption" color="text.secondary">
                                            {option.description}
                                          </Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Категории уведомлений */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Типы уведомлений
                      </Typography>

                      <List>
                        {emailCategories.map((category, index) => (
                          <React.Fragment key={category.key}>
                            <ListItem>
                              <ListItemIcon>
                                {category.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {category.label}
                                    <Chip 
                                      label={category.priority} 
                                      size="small" 
                                      color={getPriorityColor(category.priority) as any}
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={category.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={category.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={!watchedEnabled || (category.key === 'security')}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < emailCategories.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Дайджесты и отчеты */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Дайджесты и отчеты
                      </Typography>

                      <List>
                        {digestCategories.map((category, index) => (
                          <React.Fragment key={category.key}>
                            <ListItem>
                              <ListItemIcon>
                                {category.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={category.label}
                                secondary={category.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={category.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={!watchedEnabled}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < digestCategories.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>

                      {watch('weeklyDigest') && (
                        <Grid container spacing={2} sx={{ mt: 2 }}>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="digestDay"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth>
                                  <InputLabel>День недели для дайджеста</InputLabel>
                                  <Select {...field} label="День недели для дайджеста">
                                    {weekDays.map((day) => (
                                      <MenuItem key={day.value} value={day.value}>
                                        {day.label}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Отписка от всех уведомлений */}
                <Grid item xs={12}>
                  <Card sx={{ border: '1px solid', borderColor: 'warning.main' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                        <UnsubscribeIcon color="warning" />
                        <Typography variant="h6" fontWeight="bold" color="warning.main">
                          Отписаться от всех уведомлений
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Отключить все Email-уведомления одним нажатием. Уведомления о безопасности 
                        останутся включенными для защиты вашего аккаунта.
                      </Typography>

                      <Button
                        variant="outlined"
                        color="warning"
                        startIcon={<UnsubscribeIcon />}
                        onClick={handleUnsubscribeAll}
                        disabled={loading}
                      >
                        Отписаться от всех
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Кнопки сохранения */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button onClick={handleBack} disabled={loading}>
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading || !isDirty}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Сохранение...' : 'Сохранить'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default EmailNotificationsPage;
