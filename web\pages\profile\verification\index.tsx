import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Verified as VerifiedIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  PhotoCamera as PhotoIcon,
  Description as DocumentIcon,
  Share as SocialIcon,
  AccountBalance as GosuslugiIcon,
  Add as AddIcon,
  CheckCircle,
  Cancel
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';
import { 
  getProfileVerifications, 
  requestProfileVerification,
  cancelVerificationRequest 
} from '../../../src/services/profileService';
import { ProfileVerification, VerificationRequest } from '../../../src/types/profile.types';

const VerificationPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [verifications, setVerifications] = useState<ProfileVerification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [selectedVerificationType, setSelectedVerificationType] = useState<string>('');
  const [requesting, setRequesting] = useState(false);

  // Form data for different verification types
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [documentType, setDocumentType] = useState('passport');
  const [documentPhoto, setDocumentPhoto] = useState<File | null>(null);
  const [socialProvider, setSocialProvider] = useState('vk');

  const verificationTypes = [
    {
      type: 'phone',
      title: 'Телефон',
      description: 'Подтвердите номер телефона через SMS',
      icon: <PhoneIcon color="primary" />,
      benefits: ['Повышает доверие', 'Защита от спама'],
      required: true
    },
    {
      type: 'email',
      title: 'Email',
      description: 'Подтвердите адрес электронной почты',
      icon: <EmailIcon color="primary" />,
      benefits: ['Восстановление доступа', 'Уведомления'],
      required: true
    },
    {
      type: 'photo',
      title: 'Фото-верификация',
      description: 'Подтвердите, что фото принадлежат вам',
      icon: <PhotoIcon color="primary" />,
      benefits: ['Защита от фейков', 'Больше доверия'],
      required: false
    },
    {
      type: 'document',
      title: 'Документ',
      description: 'Загрузите фото документа для верификации',
      icon: <DocumentIcon color="primary" />,
      benefits: ['Максимальное доверие', 'VIP статус'],
      required: false
    },
    {
      type: 'social',
      title: 'Социальные сети',
      description: 'Привяжите аккаунт социальной сети',
      icon: <SocialIcon color="primary" />,
      benefits: ['Быстрая верификация', 'Импорт данных'],
      required: false
    },
    {
      type: 'gosuslugi',
      title: 'Госуслуги',
      description: 'Верификация через портал Госуслуги',
      icon: <GosuslugiIcon color="primary" />,
      benefits: ['Государственная верификация', 'Максимальная надежность'],
      required: false
    }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadVerifications();
  }, [user, router]);

  const loadVerifications = async () => {
    try {
      setLoading(true);
      setError(null);
      const verificationsData = await getProfileVerifications();
      setVerifications(verificationsData);
    } catch (err: any) {
      setError('Ошибка загрузки данных верификации');
    } finally {
      setLoading(false);
    }
  };

  const getVerificationStatus = (type: string) => {
    return verifications.find(v => v.type === type);
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle color="success" />;
      case 'pending':
        return <PendingIcon color="warning" />;
      case 'rejected':
      case 'expired':
        return <ErrorIcon color="error" />;
      default:
        return <AddIcon color="action" />;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'verified':
        return 'Подтверждено';
      case 'pending':
        return 'На проверке';
      case 'rejected':
        return 'Отклонено';
      case 'expired':
        return 'Истекло';
      default:
        return 'Не подтверждено';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
      case 'expired':
        return 'error';
      default:
        return 'default';
    }
  };

  const handleRequestVerification = (type: string) => {
    setSelectedVerificationType(type);
    setRequestDialogOpen(true);
    
    // Предзаполняем данные если они есть
    if (type === 'phone' && user.phoneNumber) {
      setPhoneNumber(user.phoneNumber);
    }
    if (type === 'email' && user.email) {
      setEmail(user.email);
    }
  };

  const submitVerificationRequest = async () => {
    if (!selectedVerificationType) return;

    try {
      setRequesting(true);
      setError(null);

      const request: VerificationRequest = {
        type: selectedVerificationType as any
      };

      // Добавляем данные в зависимости от типа верификации
      switch (selectedVerificationType) {
        case 'phone':
          if (!phoneNumber.trim()) {
            setError('Введите номер телефона');
            return;
          }
          request.data = { phoneNumber: phoneNumber.trim() };
          break;
        case 'email':
          if (!email.trim()) {
            setError('Введите адрес электронной почты');
            return;
          }
          request.data = { email: email.trim() };
          break;
        case 'document':
          if (!documentPhoto) {
            setError('Выберите фото документа');
            return;
          }
          request.data = { documentType, documentPhoto };
          break;
        case 'social':
          request.data = { socialProvider };
          break;
      }

      const result = await requestProfileVerification(request);
      
      if (result.success) {
        setSuccess(result.message);
        setRequestDialogOpen(false);
        loadVerifications(); // Перезагружаем данные
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка отправки запроса на верификацию');
    } finally {
      setRequesting(false);
    }
  };

  const handleCancelVerification = async (verificationId: string) => {
    try {
      await cancelVerificationRequest(verificationId);
      setSuccess('Запрос на верификацию отменен');
      loadVerifications();
    } catch (err: any) {
      setError('Ошибка отмены запроса');
    }
  };

  const renderVerificationForm = () => {
    switch (selectedVerificationType) {
      case 'phone':
        return (
          <TextField
            fullWidth
            label="Номер телефона"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            placeholder="+7 (999) 123-45-67"
            sx={{ mt: 2 }}
          />
        );
      case 'email':
        return (
          <TextField
            fullWidth
            label="Email адрес"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            sx={{ mt: 2 }}
          />
        );
      case 'document':
        return (
          <Box sx={{ mt: 2 }}>
            <FormControl component="fieldset" sx={{ mb: 2 }}>
              <FormLabel component="legend">Тип документа</FormLabel>
              <RadioGroup
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
              >
                <FormControlLabel value="passport" control={<Radio />} label="Паспорт РФ" />
                <FormControlLabel value="driver_license" control={<Radio />} label="Водительские права" />
                <FormControlLabel value="id_card" control={<Radio />} label="Удостоверение личности" />
              </RadioGroup>
            </FormControl>
            <Button
              variant="outlined"
              component="label"
              fullWidth
            >
              {documentPhoto ? documentPhoto.name : 'Выбрать фото документа'}
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={(e) => setDocumentPhoto(e.target.files?.[0] || null)}
              />
            </Button>
          </Box>
        );
      case 'social':
        return (
          <FormControl component="fieldset" sx={{ mt: 2 }}>
            <FormLabel component="legend">Социальная сеть</FormLabel>
            <RadioGroup
              value={socialProvider}
              onChange={(e) => setSocialProvider(e.target.value)}
            >
              <FormControlLabel value="vk" control={<Radio />} label="ВКонтакте" />
              <FormControlLabel value="google" control={<Radio />} label="Google" />
              <FormControlLabel value="yandex" control={<Radio />} label="Яндекс" />
              <FormControlLabel value="sber" control={<Radio />} label="Сбер ID" />
            </RadioGroup>
          </FormControl>
        );
      default:
        return null;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Верификация профиля - Likes & Love</title>
        <meta 
          name="description" 
          content="Подтвердите ваш профиль для повышения доверия и безопасности в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/profile/verification" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <VerifiedIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                  Верификация профиля
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Подтвердите ваши данные для повышения доверия и безопасности
                </Typography>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка данных верификации...
                  </Typography>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Verification types grid */}
                    <Grid container spacing={3}>
                      {verificationTypes.map((verificationType) => {
                        const verification = getVerificationStatus(verificationType.type);
                        const isVerified = verification?.status === 'verified';
                        const isPending = verification?.status === 'pending';
                        const isRejected = verification?.status === 'rejected' || verification?.status === 'expired';

                        return (
                          <Grid item xs={12} md={6} key={verificationType.type}>
                            <Card 
                              sx={{ 
                                height: '100%',
                                border: isVerified ? `2px solid ${theme.palette.success.main}` : 
                                       isPending ? `2px solid ${theme.palette.warning.main}` :
                                       isRejected ? `2px solid ${theme.palette.error.main}` : 'none',
                                position: 'relative'
                              }}
                            >
                              <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                  {verificationType.icon}
                                  <Typography variant="h6" sx={{ ml: 2, flexGrow: 1 }}>
                                    {verificationType.title}
                                  </Typography>
                                  <Chip
                                    icon={getStatusIcon(verification?.status)}
                                    label={getStatusText(verification?.status)}
                                    color={getStatusColor(verification?.status) as any}
                                    size="small"
                                  />
                                  {verificationType.required && (
                                    <Chip
                                      label="Обязательно"
                                      color="primary"
                                      size="small"
                                      sx={{ ml: 1 }}
                                    />
                                  )}
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  {verificationType.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Преимущества:
                                  </Typography>
                                  <Box component="ul" sx={{ mt: 0.5, mb: 0, pl: 2 }}>
                                    {verificationType.benefits.map((benefit, index) => (
                                      <li key={index}>
                                        <Typography variant="caption" color="text.secondary">
                                          {benefit}
                                        </Typography>
                                      </li>
                                    ))}
                                  </Box>
                                </Box>

                                {verification?.rejectionReason && (
                                  <Alert severity="error" sx={{ mt: 2 }}>
                                    <Typography variant="caption">
                                      Причина отклонения: {verification.rejectionReason}
                                    </Typography>
                                  </Alert>
                                )}

                                {verification?.expiresAt && verification.status === 'verified' && (
                                  <Typography variant="caption" color="text.secondary">
                                    Действительно до: {new Date(verification.expiresAt).toLocaleDateString()}
                                  </Typography>
                                )}
                              </CardContent>

                              <CardActions>
                                {!verification || isRejected ? (
                                  <Button
                                    variant="contained"
                                    onClick={() => handleRequestVerification(verificationType.type)}
                                    size="small"
                                    startIcon={<AddIcon />}
                                  >
                                    Подтвердить
                                  </Button>
                                ) : isPending ? (
                                  <Button
                                    variant="outlined"
                                    onClick={() => handleCancelVerification(verification.id)}
                                    size="small"
                                    startIcon={<Cancel />}
                                    color="warning"
                                  >
                                    Отменить
                                  </Button>
                                ) : (
                                  <Button
                                    variant="outlined"
                                    onClick={() => router.push('/profile/verification/status')}
                                    size="small"
                                  >
                                    Подробнее
                                  </Button>
                                )}
                              </CardActions>
                            </Card>
                          </Grid>
                        );
                      })}
                    </Grid>

                    {/* Benefits section */}
                    <Box sx={{ mt: 6 }}>
                      <Typography variant="h6" gutterBottom>
                        Зачем нужна верификация?
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ textAlign: 'center', p: 2 }}>
                            <CheckCircle sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                            <Typography variant="subtitle2" gutterBottom>
                              Больше доверия
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Подтвержденные профили получают больше лайков и сообщений
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ textAlign: 'center', p: 2 }}>
                            <VerifiedIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                            <Typography variant="subtitle2" gutterBottom>
                              Защита от фейков
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Верификация помогает отличить настоящие профили от поддельных
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ textAlign: 'center', p: 2 }}>
                            <PhotoIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                            <Typography variant="subtitle2" gutterBottom>
                              Приоритет в поиске
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Подтвержденные профили показываются в поиске в первую очередь
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Verification request dialog */}
        <Dialog 
          open={requestDialogOpen} 
          onClose={() => setRequestDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Запрос на верификацию: {verificationTypes.find(v => v.type === selectedVerificationType)?.title}
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {verificationTypes.find(v => v.type === selectedVerificationType)?.description}
            </Typography>
            
            {renderVerificationForm()}

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => setRequestDialogOpen(false)}
              disabled={requesting}
            >
              Отмена
            </Button>
            <Button 
              onClick={submitVerificationRequest}
              variant="contained"
              disabled={requesting}
              startIcon={requesting ? <CircularProgress size={20} /> : undefined}
            >
              {requesting ? 'Отправка...' : 'Отправить запрос'}
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default VerificationPage;
