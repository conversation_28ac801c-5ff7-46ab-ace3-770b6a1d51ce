import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Stack,
  Card,
  CardContent,
  Grid,
  useTheme,
  useMediaQuery,
  Fade,
  Slide
} from '@mui/material';
import {
  Favorite,
  Security,
  LocationOn,
  Verified,
  Groups,
  VideoCall,
  ArrowForward,
  Star
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import OnboardingProgress from '../../components/Onboarding/OnboardingProgress';
import { useAuth } from '../../src/contexts/AuthContext';
import { useOnboarding } from '../../src/contexts/OnboardingContext';

const WelcomePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { steps, currentStep, nextStep } = useOnboarding();

  useEffect(() => {
    // Если пользователь не авторизован, перенаправляем на логин
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  const features = [
    {
      icon: <Security color="primary" sx={{ fontSize: 40 }} />,
      title: 'Безопасность',
      description: 'Верификация через Госуслуги и проверка профилей'
    },
    {
      icon: <Verified color="primary" sx={{ fontSize: 40 }} />,
      title: 'Проверенные профили',
      description: 'Только реальные люди с подтвержденными данными'
    },
    {
      icon: <LocationOn color="primary" sx={{ fontSize: 40 }} />,
      title: 'Умный поиск',
      description: 'Находите людей рядом с вами и по интересам'
    },
    {
      icon: <VideoCall color="primary" sx={{ fontSize: 40 }} />,
      title: 'Видеозвонки',
      description: 'Общайтесь лицом к лицу прямо в приложении'
    },
    {
      icon: <Groups color="primary" sx={{ fontSize: 40 }} />,
      title: 'События',
      description: 'Участвуйте в мероприятиях и встречах'
    },
    {
      icon: <Star color="primary" sx={{ fontSize: 40 }} />,
      title: 'Качественные знакомства',
      description: 'Алгоритм подбора для серьезных отношений'
    }
  ];

  const handleGetStarted = () => {
    nextStep();
  };

  const handleSkipOnboarding = () => {
    router.push('/discover');
  };

  if (!user) {
    return null; // Показываем пустую страницу пока идет редирект
  }

  return (
    <>
      <Head>
        <title>Добро пожаловать в Likes & Love</title>
        <meta 
          name="description" 
          content="Начните знакомства в безопасном приложении с проверенными профилями. Настройте свой профиль за несколько простых шагов." 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Progress indicator */}
            <OnboardingProgress
              steps={steps}
              currentStep={currentStep}
              variant={isMobile ? 'minimal' : 'horizontal'}
              showLabels={!isMobile}
            />

            <Fade in timeout={800}>
              <Paper elevation={3} sx={{ p: { xs: 3, md: 6 }, textAlign: 'center' }}>
                {/* Welcome header */}
                <Box sx={{ mb: 4 }}>
                  <Typography 
                    variant={isMobile ? "h4" : "h3"} 
                    gutterBottom 
                    sx={{ 
                      fontWeight: 700,
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}
                  >
                    Добро пожаловать в Likes & Love!
                  </Typography>
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                    Привет, {user.firstName || user.email}! 👋
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
                    Мы рады видеть вас в нашем сообществе! Давайте настроим ваш профиль, 
                    чтобы вы могли найти именно тех людей, которые вам подходят.
                  </Typography>
                </Box>

                {/* Features grid */}
                <Box sx={{ mb: 6 }}>
                  <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
                    Что делает нас особенными
                  </Typography>
                  <Grid container spacing={3}>
                    {features.map((feature, index) => (
                      <Grid item xs={12} sm={6} md={4} key={index}>
                        <Slide 
                          in 
                          timeout={1000 + index * 200} 
                          direction="up"
                        >
                          <Card 
                            sx={{ 
                              height: '100%',
                              transition: 'transform 0.2s ease-in-out',
                              '&:hover': {
                                transform: 'translateY(-4px)',
                                boxShadow: theme.shadows[8]
                              }
                            }}
                          >
                            <CardContent sx={{ textAlign: 'center', p: 3 }}>
                              <Box sx={{ mb: 2 }}>
                                {feature.icon}
                              </Box>
                              <Typography variant="h6" gutterBottom>
                                {feature.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {feature.description}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Slide>
                      </Grid>
                    ))}
                  </Grid>
                </Box>

                {/* Call to action */}
                <Box sx={{ mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Готовы начать?
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Настройка профиля займет всего 5 минут
                  </Typography>
                  
                  <Stack 
                    direction={isMobile ? "column" : "row"} 
                    spacing={2} 
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Button
                      variant="contained"
                      size="large"
                      onClick={handleGetStarted}
                      endIcon={<ArrowForward />}
                      sx={{
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        '&:hover': {
                          background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`
                        }
                      }}
                    >
                      Настроить профиль
                    </Button>
                    
                    <Button
                      variant="text"
                      size="large"
                      onClick={handleSkipOnboarding}
                      sx={{ px: 3 }}
                    >
                      Пропустить настройку
                    </Button>
                  </Stack>
                </Box>

                {/* Additional info */}
                <Box sx={{ 
                  mt: 4, 
                  pt: 3, 
                  borderTop: `1px solid ${theme.palette.divider}`,
                  textAlign: 'center'
                }}>
                  <Typography variant="body2" color="text.secondary">
                    <Favorite sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
                    Присоединяйтесь к тысячам пользователей, которые уже нашли свою любовь
                  </Typography>
                </Box>
              </Paper>
            </Fade>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default WelcomePage;
