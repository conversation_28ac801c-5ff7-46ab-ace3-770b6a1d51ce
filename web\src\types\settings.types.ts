export interface UserSettings {
  id: string;
  userId: string;
  privacy: PrivacySettings;
  notifications: NotificationSettings;
  preferences: UserPreferences;
  security: SecuritySettings;
  account: AccountSettings;
  createdAt: string;
  updatedAt: string;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showOnlineStatus: boolean;
  showLastSeen: boolean;
  showAge: boolean;
  showLocation: boolean;
  showDistance: boolean;
  allowMessagesFrom: 'everyone' | 'matches' | 'premium';
  allowProfileViews: 'everyone' | 'matches' | 'premium';
  hideFromSearch: boolean;
  incognitoMode: boolean;
  blockScreenshots: boolean;
  dataProcessingConsent: boolean;
  marketingConsent: boolean;
  analyticsConsent: boolean;
}

export interface NotificationSettings {
  email: EmailNotifications;
  push: PushNotifications;
  sms: SmsNotifications;
  inApp: InAppNotifications;
  frequency: 'immediate' | 'daily' | 'weekly' | 'never';
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm format
    endTime: string; // HH:mm format
    timezone: string;
  };
}

export interface EmailNotifications {
  enabled: boolean;
  newMatches: boolean;
  newMessages: boolean;
  profileViews: boolean;
  likes: boolean;
  superLikes: boolean;
  promotions: boolean;
  newsletter: boolean;
  securityAlerts: boolean;
  accountUpdates: boolean;
}

export interface PushNotifications {
  enabled: boolean;
  newMatches: boolean;
  newMessages: boolean;
  profileViews: boolean;
  likes: boolean;
  superLikes: boolean;
  meetingReminders: boolean;
  eventReminders: boolean;
  securityAlerts: boolean;
  promotions: boolean;
}

export interface SmsNotifications {
  enabled: boolean;
  securityAlerts: boolean;
  loginAlerts: boolean;
  passwordChanges: boolean;
  accountChanges: boolean;
}

export interface InAppNotifications {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  badge: boolean;
  preview: boolean;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  currency: string;
  units: 'metric' | 'imperial';
  theme: 'light' | 'dark' | 'auto';
  autoplay: boolean;
  reducedMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
  searchRadius: number; // in kilometers
  ageRange: {
    min: number;
    max: number;
  };
  interestedIn: 'men' | 'women' | 'everyone';
  showMeIn: 'discovery' | 'hidden';
}

export interface SecuritySettings {
  twoFactorAuth: {
    enabled: boolean;
    method: 'sms' | 'email' | 'app';
    backupCodes: string[];
    lastUsed?: string;
  };
  loginAlerts: boolean;
  deviceTracking: boolean;
  sessionTimeout: number; // in minutes
  passwordRequirements: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
  };
  trustedDevices: TrustedDevice[];
  loginHistory: LoginSession[];
  blockedUsers: string[];
  reportedUsers: string[];
}

export interface TrustedDevice {
  id: string;
  name: string;
  deviceType: 'mobile' | 'desktop' | 'tablet';
  browser: string;
  os: string;
  location: string;
  ipAddress: string;
  lastUsed: string;
  isCurrentDevice: boolean;
  createdAt: string;
}

export interface LoginSession {
  id: string;
  deviceInfo: {
    name: string;
    type: 'mobile' | 'desktop' | 'tablet';
    browser: string;
    os: string;
  };
  location: {
    country: string;
    city: string;
    region?: string;
  };
  ipAddress: string;
  loginTime: string;
  logoutTime?: string;
  isActive: boolean;
  isSuspicious: boolean;
}

export interface AccountSettings {
  email: string;
  phone: string;
  username: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  accountStatus: 'active' | 'suspended' | 'deactivated' | 'deleted';
  deactivationReason?: string;
  deactivatedAt?: string;
  deletionScheduledAt?: string;
  dataRetentionPeriod: number; // in days
  exportRequests: DataExportRequest[];
  deleteRequests: AccountDeleteRequest[];
}

export interface DataExportRequest {
  id: string;
  requestedAt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  expiresAt?: string;
  fileSize?: number;
  format: 'json' | 'csv' | 'pdf';
}

export interface AccountDeleteRequest {
  id: string;
  requestedAt: string;
  scheduledAt: string;
  reason: string;
  feedback?: string;
  status: 'pending' | 'scheduled' | 'cancelled' | 'completed';
  cancellationDeadline: string;
}

export interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'password_change' | 'email_change' | 'phone_change' | 
        'two_factor_enabled' | 'two_factor_disabled' | 'suspicious_activity' | 
        'account_locked' | 'account_unlocked';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  deviceInfo?: {
    name: string;
    type: string;
    browser: string;
    os: string;
  };
  location?: {
    country: string;
    city: string;
    ipAddress: string;
  };
  metadata?: Record<string, any>;
  timestamp: string;
  acknowledged: boolean;
}

export interface PrivacyReport {
  dataCollected: {
    personalInfo: string[];
    behavioralData: string[];
    deviceData: string[];
    locationData: string[];
  };
  dataSharing: {
    thirdParties: string[];
    purposes: string[];
    retentionPeriod: string;
  };
  userRights: {
    canExport: boolean;
    canDelete: boolean;
    canCorrect: boolean;
    canRestrict: boolean;
  };
  lastUpdated: string;
}

// Request/Response types
export interface UpdatePrivacySettingsRequest {
  privacy: Partial<PrivacySettings>;
}

export interface UpdateNotificationSettingsRequest {
  notifications: Partial<NotificationSettings>;
}

export interface UpdatePreferencesRequest {
  preferences: Partial<UserPreferences>;
}

export interface UpdateSecuritySettingsRequest {
  security: Partial<SecuritySettings>;
}

export interface Enable2FARequest {
  method: 'sms' | 'email' | 'app';
  phoneNumber?: string;
  email?: string;
}

export interface Verify2FARequest {
  code: string;
  backupCode?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangeEmailRequest {
  newEmail: string;
  password: string;
}

export interface ChangePhoneRequest {
  newPhone: string;
  password: string;
}

export interface DeactivateAccountRequest {
  reason: string;
  feedback?: string;
  password: string;
}

export interface DeleteAccountRequest {
  reason: string;
  feedback?: string;
  password: string;
  confirmDeletion: boolean;
}

export interface BlockUserRequest {
  userId: string;
  reason: string;
}

export interface ReportUserRequest {
  userId: string;
  reason: string;
  description: string;
  evidence?: string[];
}

export interface ExportDataRequest {
  format: 'json' | 'csv' | 'pdf';
  includeMessages: boolean;
  includePhotos: boolean;
  includeMatches: boolean;
  includeProfile: boolean;
}

// Context types
export interface SettingsContextType {
  // State
  settings: UserSettings | null;
  securityEvents: SecurityEvent[];
  privacyReport: PrivacyReport | null;
  loading: boolean;
  error: string | null;

  // Actions
  loadSettings: () => Promise<void>;
  loadSecurityEvents: () => Promise<void>;
  loadPrivacyReport: () => Promise<void>;
  
  updatePrivacySettings: (request: UpdatePrivacySettingsRequest) => Promise<void>;
  updateNotificationSettings: (request: UpdateNotificationSettingsRequest) => Promise<void>;
  updatePreferences: (request: UpdatePreferencesRequest) => Promise<void>;
  updateSecuritySettings: (request: UpdateSecuritySettingsRequest) => Promise<void>;
  
  enable2FA: (request: Enable2FARequest) => Promise<{ qrCode?: string; backupCodes: string[] }>;
  verify2FA: (request: Verify2FARequest) => Promise<void>;
  disable2FA: (password: string) => Promise<void>;
  
  changePassword: (request: ChangePasswordRequest) => Promise<void>;
  changeEmail: (request: ChangeEmailRequest) => Promise<void>;
  changePhone: (request: ChangePhoneRequest) => Promise<void>;
  
  addTrustedDevice: (deviceName: string) => Promise<void>;
  removeTrustedDevice: (deviceId: string) => Promise<void>;
  terminateSession: (sessionId: string) => Promise<void>;
  terminateAllSessions: () => Promise<void>;
  
  blockUser: (request: BlockUserRequest) => Promise<void>;
  unblockUser: (userId: string) => Promise<void>;
  reportUser: (request: ReportUserRequest) => Promise<void>;
  
  exportData: (request: ExportDataRequest) => Promise<DataExportRequest>;
  deactivateAccount: (request: DeactivateAccountRequest) => Promise<void>;
  reactivateAccount: (password: string) => Promise<void>;
  deleteAccount: (request: DeleteAccountRequest) => Promise<void>;
  cancelAccountDeletion: () => Promise<void>;
  
  acknowledgeSecurityEvent: (eventId: string) => Promise<void>;
  clearSecurityEvents: () => Promise<void>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
