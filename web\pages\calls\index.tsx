import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Call as CallIcon,
  VideoCall as VideoCallIcon,
  CallMade as OutgoingIcon,
  CallReceived as IncomingIcon,
  CallEnd as MissedIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  Message as MessageIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getCallHistory,
  initiateCall
} from '../../src/services/chatService';
import { 
  CallSession,
  CallRequest 
} from '../../src/types/chat.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`calls-tabpanel-${index}`}
      aria-labelledby={`calls-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const CallsHistoryPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [callHistory, setCallHistory] = useState<CallSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedCall, setSelectedCall] = useState<CallSession | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const tabs = [
    { label: 'Все', filter: 'all' },
    { label: 'Пропущенные', filter: 'missed' },
    { label: 'Исходящие', filter: 'outgoing' },
    { label: 'Входящие', filter: 'incoming' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadCallHistory();
  }, [user, router]);

  const loadCallHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const history = await getCallHistory();
      setCallHistory(history);
    } catch (err: any) {
      setError('Ошибка загрузки истории звонков');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, call: CallSession) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedCall(call);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCall(null);
  };

  const handleCallBack = async (call: CallSession) => {
    if (!call) return;
    
    try {
      setActionLoading(call.id);
      setError(null);

      const request: CallRequest = {
        conversationId: call.conversationId,
        type: call.type,
        participantIds: call.participantIds.filter(id => id !== user?.id)
      };

      const result = await initiateCall(request);
      
      if (result.success && result.session) {
        setSuccess('Звонок инициирован');
        router.push(`/calls/${result.session.id}`);
      } else {
        setError(result.error || 'Ошибка инициации звонка');
      }
    } catch (err: any) {
      setError('Ошибка инициации звонка');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const formatCallDuration = (duration?: number) => {
    if (!duration) return '0:00';
    
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatCallTime = (dateString: string) => {
    const callTime = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - callTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) return 'Вчера';
      if (diffInDays < 7) return `${diffInDays} дн. назад`;
      return callTime.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' });
    }
  };

  const getCallIcon = (call: CallSession) => {
    const isOutgoing = call.initiatorId === user?.id;
    
    if (call.status === 'missed') {
      return <MissedIcon color="error" />;
    } else if (isOutgoing) {
      return <OutgoingIcon color="success" />;
    } else {
      return <IncomingIcon color="primary" />;
    }
  };

  const getCallStatusColor = (status: CallSession['status']) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'default';
      case 'declined':
        return 'warning';
      case 'missed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getCallStatusLabel = (status: CallSession['status']) => {
    switch (status) {
      case 'active':
        return 'Активный';
      case 'ended':
        return 'Завершен';
      case 'declined':
        return 'Отклонен';
      case 'missed':
        return 'Пропущен';
      case 'initiating':
        return 'Инициация';
      case 'ringing':
        return 'Звонок';
      default:
        return status;
    }
  };

  const filteredCalls = callHistory.filter(call => {
    const currentFilter = tabs[activeTab].filter;
    
    switch (currentFilter) {
      case 'missed':
        return call.status === 'missed';
      case 'outgoing':
        return call.initiatorId === user?.id;
      case 'incoming':
        return call.initiatorId !== user?.id;
      default:
        return true;
    }
  });

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>История звонков - Likes & Love</title>
        <meta 
          name="description" 
          content="История ваших звонков в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/calls" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
              {/* Header */}
              <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant={isMobile ? "h5" : "h4"}>
                    <CallIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                    История звонков
                  </Typography>
                  <IconButton onClick={loadCallHistory} disabled={loading}>
                    <RefreshIcon />
                  </IconButton>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ m: 2 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ m: 2 }}>
                  {success}
                </Alert>
              )}

              {/* Tabs */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={activeTab}
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  variant={isMobile ? "scrollable" : "fullWidth"}
                  scrollButtons="auto"
                >
                  {tabs.map((tab, index) => (
                    <Tab key={index} label={tab.label} />
                  ))}
                </Tabs>
              </Box>

              {/* Call List */}
              <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <CircularProgress size={60} />
                  </Box>
                ) : filteredCalls.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 8 }}>
                    <CallIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      История звонков пуста
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Здесь будут отображаться ваши звонки
                    </Typography>
                  </Box>
                ) : (
                  <Fade in timeout={600}>
                    <List sx={{ height: '100%', overflow: 'auto', py: 0 }}>
                      {filteredCalls.map((call) => (
                        <ListItem
                          key={call.id}
                          button
                          onClick={() => router.push(`/calls/${call.id}`)}
                          sx={{
                            borderBottom: 1,
                            borderColor: 'divider',
                            '&:hover': {
                              backgroundColor: 'action.hover'
                            }
                          }}
                        >
                          <ListItemAvatar>
                            <Avatar sx={{ backgroundColor: 'primary.light' }}>
                              {call.type === 'video' ? <VideoCallIcon /> : <CallIcon />}
                            </Avatar>
                          </ListItemAvatar>

                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {getCallIcon(call)}
                                <Typography variant="subtitle1">
                                  {call.type === 'video' ? 'Видеозвонок' : 'Аудиозвонок'}
                                </Typography>
                                <Chip
                                  label={getCallStatusLabel(call.status)}
                                  size="small"
                                  color={getCallStatusColor(call.status) as any}
                                  variant="outlined"
                                />
                              </Box>
                            }
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                                <Typography variant="body2" color="text.secondary">
                                  {formatCallTime(call.startedAt || call.endedAt || '')}
                                </Typography>
                                {call.duration && (
                                  <Typography variant="body2" color="text.secondary">
                                    Длительность: {formatCallDuration(call.duration)}
                                  </Typography>
                                )}
                                {call.quality && (
                                  <Typography variant="body2" color="text.secondary">
                                    Качество: {call.quality.audioQuality}/10
                                  </Typography>
                                )}
                              </Box>
                            }
                          />

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, call)}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>
                        </ListItem>
                      ))}
                    </List>
                  </Fade>
                )}
              </Box>
            </Paper>
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem 
            onClick={() => selectedCall && handleCallBack(selectedCall)}
            disabled={actionLoading === selectedCall?.id}
          >
            <CallIcon sx={{ mr: 1 }} />
            Перезвонить
          </MenuItem>
          <MenuItem onClick={() => {
            if (selectedCall) {
              router.push(`/chat/${selectedCall.conversationId}`);
            }
            handleMenuClose();
          }}>
            <MessageIcon sx={{ mr: 1 }} />
            Написать сообщение
          </MenuItem>
          <MenuItem onClick={handleMenuClose}>
            <DeleteIcon sx={{ mr: 1 }} />
            Удалить из истории
          </MenuItem>
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            <BlockIcon sx={{ mr: 1 }} />
            Заблокировать пользователя
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default CallsHistoryPage;
