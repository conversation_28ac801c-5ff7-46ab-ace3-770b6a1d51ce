import axios from 'axios';
import { config } from '../config';
import {
  Place,
  PlaceCategory,
  PlaceReview,
  PlaceCheckIn,
  PlaceSuggestion,
  PlaceStatistics,
  PlaceSearchResult,
  CreatePlaceRequest,
  UpdatePlaceRequest,
  CreateReviewRequest,
  PlaceFilters,
  PaginatedResponse
} from '../types/places.types';

const placesApi = axios.create({
  baseURL: `${config.api.baseUrl}/places`,
  withCredentials: true,
});

// Places CRUD
export const getPlaces = async (
  filters?: PlaceFilters,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Place>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await placesApi.get(`/?${params.toString()}`);
  return data;
};

export const getPlace = async (id: string): Promise<Place> => {
  const { data } = await placesApi.get(`/${id}`);
  return data.place;
};

export const createPlace = async (request: CreatePlaceRequest): Promise<Place> => {
  const formData = new FormData();
  
  // Add basic fields
  Object.entries(request).forEach(([key, value]) => {
    if (key === 'photos' && value) {
      (value as File[]).forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });
    } else if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    }
  });

  const { data } = await placesApi.post('/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.place;
};

export const updatePlace = async (id: string, request: UpdatePlaceRequest): Promise<Place> => {
  const { data } = await placesApi.put(`/${id}`, request);
  return data.place;
};

export const deletePlace = async (id: string): Promise<void> => {
  await placesApi.delete(`/${id}`);
};

export const claimPlace = async (id: string, verificationData: any): Promise<void> => {
  await placesApi.post(`/${id}/claim`, verificationData);
};

// Search and discovery
export const searchPlaces = async (
  query: string,
  filters?: PlaceFilters,
  page: number = 1,
  limit: number = 20
): Promise<PlaceSearchResult> => {
  const params = new URLSearchParams({
    query,
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await placesApi.get(`/search?${params.toString()}`);
  return data;
};

export const getNearbyPlaces = async (
  latitude: number,
  longitude: number,
  radius: number = 5,
  categoryId?: string,
  limit: number = 20
): Promise<Place[]> => {
  const params = new URLSearchParams({
    lat: latitude.toString(),
    lng: longitude.toString(),
    radius: radius.toString(),
    limit: limit.toString()
  });

  if (categoryId) {
    params.append('category', categoryId);
  }

  const { data } = await placesApi.get(`/nearby?${params.toString()}`);
  return data.places;
};

export const getPopularPlaces = async (
  categoryId?: string,
  timeframe: 'day' | 'week' | 'month' = 'week',
  limit: number = 20
): Promise<Place[]> => {
  const params = new URLSearchParams({
    timeframe,
    limit: limit.toString()
  });

  if (categoryId) {
    params.append('category', categoryId);
  }

  const { data } = await placesApi.get(`/popular?${params.toString()}`);
  return data.places;
};

export const getTrendingPlaces = async (
  timeframe: 'day' | 'week' | 'month' = 'week',
  limit: number = 20
): Promise<Place[]> => {
  const { data } = await placesApi.get(`/trending?timeframe=${timeframe}&limit=${limit}`);
  return data.places;
};

export const getPlaceSuggestions = async (
  type?: PlaceSuggestion['type'],
  limit: number = 10
): Promise<PlaceSuggestion[]> => {
  const params = new URLSearchParams({
    limit: limit.toString()
  });

  if (type) {
    params.append('type', type);
  }

  const { data } = await placesApi.get(`/suggestions?${params.toString()}`);
  return data.suggestions;
};

// Categories
export const getPlaceCategories = async (): Promise<PlaceCategory[]> => {
  const { data } = await placesApi.get('/categories');
  return data.categories;
};

export const getPlacesByCategory = async (
  categoryId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Place>> => {
  const { data } = await placesApi.get(`/categories/${categoryId}/places?page=${page}&limit=${limit}`);
  return data;
};

// Favorites
export const getFavoritePlaces = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Place>> => {
  const { data } = await placesApi.get(`/favorites?page=${page}&limit=${limit}`);
  return data;
};

export const addToFavorites = async (placeId: string): Promise<void> => {
  await placesApi.post(`/${placeId}/favorite`);
};

export const removeFromFavorites = async (placeId: string): Promise<void> => {
  await placesApi.delete(`/${placeId}/favorite`);
};

export const isFavorite = async (placeId: string): Promise<boolean> => {
  const { data } = await placesApi.get(`/${placeId}/favorite/status`);
  return data.isFavorite;
};

// Check-ins
export const checkInToPlace = async (
  placeId: string,
  note?: string,
  photos?: File[],
  location?: { latitude: number; longitude: number },
  isPrivate: boolean = false
): Promise<PlaceCheckIn> => {
  const formData = new FormData();
  
  formData.append('placeId', placeId);
  formData.append('isPrivate', isPrivate.toString());
  
  if (note) {
    formData.append('note', note);
  }
  
  if (location) {
    formData.append('location', JSON.stringify(location));
  }
  
  if (photos) {
    photos.forEach((photo, index) => {
      formData.append(`photo_${index}`, photo);
    });
  }

  const { data } = await placesApi.post('/checkins', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.checkin;
};

export const getPlaceCheckins = async (
  placeId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<PlaceCheckIn>> => {
  const { data } = await placesApi.get(`/${placeId}/checkins?page=${page}&limit=${limit}`);
  return data;
};

export const getUserCheckins = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<PlaceCheckIn>> => {
  const { data } = await placesApi.get(`/checkins/my?page=${page}&limit=${limit}`);
  return data;
};

// Reviews
export const createReview = async (request: CreateReviewRequest): Promise<PlaceReview> => {
  const formData = new FormData();
  
  Object.entries(request).forEach(([key, value]) => {
    if (key === 'photos' && value) {
      (value as File[]).forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });
    } else if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    }
  });

  const { data } = await placesApi.post('/reviews', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.review;
};

export const updateReview = async (
  reviewId: string,
  request: Partial<CreateReviewRequest>
): Promise<PlaceReview> => {
  const { data } = await placesApi.put(`/reviews/${reviewId}`, request);
  return data.review;
};

export const deleteReview = async (reviewId: string): Promise<void> => {
  await placesApi.delete(`/reviews/${reviewId}`);
};

export const getPlaceReviews = async (
  placeId: string,
  page: number = 1,
  limit: number = 10,
  sortBy: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful' = 'newest'
): Promise<PaginatedResponse<PlaceReview>> => {
  const { data } = await placesApi.get(`/${placeId}/reviews?page=${page}&limit=${limit}&sort=${sortBy}`);
  return data;
};

export const getUserReviews = async (
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponse<PlaceReview>> => {
  const { data } = await placesApi.get(`/reviews/my?page=${page}&limit=${limit}`);
  return data;
};

export const markReviewHelpful = async (reviewId: string): Promise<void> => {
  await placesApi.post(`/reviews/${reviewId}/helpful`);
};

export const unmarkReviewHelpful = async (reviewId: string): Promise<void> => {
  await placesApi.delete(`/reviews/${reviewId}/helpful`);
};

// Photos
export const uploadPlacePhotos = async (placeId: string, photos: File[]): Promise<string[]> => {
  const formData = new FormData();
  
  photos.forEach((photo, index) => {
    formData.append(`photo_${index}`, photo);
  });

  const { data } = await placesApi.post(`/${placeId}/photos`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.photoUrls;
};

export const deletePlacePhoto = async (placeId: string, photoId: string): Promise<void> => {
  await placesApi.delete(`/${placeId}/photos/${photoId}`);
};

// Reporting
export const reportPlace = async (
  placeId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await placesApi.post(`/${placeId}/report`, { reason, details });
};

export const reportReview = async (
  reviewId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await placesApi.post(`/reviews/${reviewId}/report`, { reason, details });
};

// Statistics
export const getPlaceStatistics = async (): Promise<PlaceStatistics> => {
  const { data } = await placesApi.get('/statistics');
  return data.statistics;
};

export const getPlaceAnalytics = async (placeId: string): Promise<any> => {
  const { data } = await placesApi.get(`/${placeId}/analytics`);
  return data.analytics;
};

// Geolocation
export const getUserLocation = async (): Promise<{ latitude: number; longitude: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      (error) => {
        reject(new Error(`Geolocation error: ${error.message}`));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  });
};

export const reverseGeocode = async (
  latitude: number,
  longitude: number
): Promise<string> => {
  const { data } = await placesApi.get(`/geocode/reverse?lat=${latitude}&lng=${longitude}`);
  return data.address;
};

export const geocodeAddress = async (address: string): Promise<{
  latitude: number;
  longitude: number;
  formattedAddress: string;
}> => {
  const { data } = await placesApi.get(`/geocode?address=${encodeURIComponent(address)}`);
  return data.location;
};

// Amenities and features
export const getAvailableAmenities = async (): Promise<any[]> => {
  const { data } = await placesApi.get('/amenities');
  return data.amenities;
};

export const getPopularTags = async (limit: number = 50): Promise<{ tag: string; count: number }[]> => {
  const { data } = await placesApi.get(`/tags/popular?limit=${limit}`);
  return data.tags;
};

// Bulk operations
export const bulkUpdatePlaces = async (
  placeIds: string[],
  updates: Partial<UpdatePlaceRequest>
): Promise<void> => {
  await placesApi.put('/bulk-update', { placeIds, updates });
};

export const bulkDeletePlaces = async (placeIds: string[]): Promise<void> => {
  await placesApi.delete('/bulk-delete', { data: { placeIds } });
};

// Integration with meetings/events
export const getPlacesForMeeting = async (
  filters?: PlaceFilters & {
    capacity?: number;
    reservationSupported?: boolean;
  }
): Promise<Place[]> => {
  const { data } = await placesApi.get('/for-meetings', { params: filters });
  return data.places;
};

export const getPlacesForEvent = async (
  filters?: PlaceFilters & {
    capacity?: number;
    eventSupported?: boolean;
  }
): Promise<Place[]> => {
  const { data } = await placesApi.get('/for-events', { params: filters });
  return data.places;
};

// Working hours utilities
export const isPlaceOpenNow = async (placeId: string): Promise<boolean> => {
  const { data } = await placesApi.get(`/${placeId}/open-status`);
  return data.isOpen;
};

export const getPlaceOpeningHours = async (
  placeId: string,
  date?: string
): Promise<{ open: string; close: string; isClosed: boolean }> => {
  const params = date ? `?date=${date}` : '';
  const { data } = await placesApi.get(`/${placeId}/hours${params}`);
  return data.hours;
};

// Sharing
export const sharePlace = async (
  placeId: string,
  platform: 'facebook' | 'twitter' | 'whatsapp' | 'telegram' | 'copy'
): Promise<string> => {
  const { data } = await placesApi.post(`/${placeId}/share`, { platform });
  return data.shareUrl;
};

export const getPlaceShareStats = async (placeId: string): Promise<any> => {
  const { data } = await placesApi.get(`/${placeId}/share/stats`);
  return data.stats;
};
