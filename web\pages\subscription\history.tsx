import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  TextField,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  History as HistoryIcon,
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSubscription } from '../../src/contexts/SubscriptionContext';
import { formatCurrency } from '../../src/services/subscriptionService';
import { PaymentTransaction } from '../../src/types/subscription.types';

const PaymentHistoryPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { transactionHistory, loading, error, loadTransactionHistory } = useSubscription();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedTransaction, setSelectedTransaction] = useState<PaymentTransaction | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showFiltersDialog, setShowFiltersDialog] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuTransactionId, setMenuTransactionId] = useState<string | null>(null);

  // Filter states
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: ''
  });

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadTransactionHistory();
  }, [user, router]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, transactionId: string) => {
    setAnchorEl(event.currentTarget);
    setMenuTransactionId(transactionId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuTransactionId(null);
  };

  const handleViewDetails = (transaction: PaymentTransaction) => {
    setSelectedTransaction(transaction);
    setShowDetailsDialog(true);
    handleMenuClose();
  };

  const handleDownloadReceipt = (transactionId: string) => {
    // In real implementation, this would download the receipt
    window.open(`/api/receipts/${transactionId}`, '_blank');
    handleMenuClose();
  };

  const getStatusColor = (status: PaymentTransaction['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      case 'refunded':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: PaymentTransaction['status']) => {
    switch (status) {
      case 'completed':
        return 'Завершен';
      case 'pending':
        return 'В обработке';
      case 'failed':
        return 'Ошибка';
      case 'cancelled':
        return 'Отменен';
      case 'refunded':
        return 'Возврат';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: PaymentTransaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckIcon fontSize="small" />;
      case 'pending':
        return <ScheduleIcon fontSize="small" />;
      case 'failed':
        return <ErrorIcon fontSize="small" />;
      case 'cancelled':
        return <CancelIcon fontSize="small" />;
      case 'refunded':
        return <RefreshIcon fontSize="small" />;
      default:
        return null;
    }
  };

  const getTypeText = (type: PaymentTransaction['type']) => {
    switch (type) {
      case 'subscription':
        return 'Подписка';
      case 'one_time':
        return 'Разовый платеж';
      case 'refund':
        return 'Возврат';
      case 'chargeback':
        return 'Чарджбэк';
      default:
        return type;
    }
  };

  const filteredTransactions = transactionHistory.filter(transaction => {
    if (filters.status && transaction.status !== filters.status) return false;
    if (filters.type && transaction.type !== filters.type) return false;
    if (filters.dateFrom && new Date(transaction.createdAt) < new Date(filters.dateFrom)) return false;
    if (filters.dateTo && new Date(transaction.createdAt) > new Date(filters.dateTo)) return false;
    if (filters.amountMin && transaction.amount < Number(filters.amountMin)) return false;
    if (filters.amountMax && transaction.amount > Number(filters.amountMax)) return false;
    return true;
  });

  const paginatedTransactions = filteredTransactions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>История платежей - Likes & Love</title>
        <meta 
          name="description" 
          content="История платежей и транзакций в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <HistoryIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                История платежей
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<FilterIcon />}
                  onClick={() => setShowFiltersDialog(true)}
                >
                  Фильтры
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadTransactionHistory}
                  disabled={loading}
                >
                  Обновить
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка истории платежей...
                </Typography>
              </Box>
            ) : filteredTransactions.length === 0 ? (
              <Fade in timeout={600}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <HistoryIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    История платежей пуста
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Здесь будут отображаться все ваши транзакции
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/subscription/plans')}
                  >
                    Оформить подписку
                  </Button>
                </Box>
              </Fade>
            ) : (
              <Fade in timeout={600}>
                <Paper elevation={3}>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Дата</TableCell>
                          <TableCell>Описание</TableCell>
                          <TableCell>Тип</TableCell>
                          <TableCell>Сумма</TableCell>
                          <TableCell>Статус</TableCell>
                          <TableCell align="right">Действия</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {paginatedTransactions.map((transaction) => (
                          <TableRow key={transaction.id} hover>
                            <TableCell>
                              <Typography variant="body2">
                                {new Date(transaction.createdAt).toLocaleDateString('ru-RU')}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {new Date(transaction.createdAt).toLocaleTimeString('ru-RU')}
                              </Typography>
                            </TableCell>
                            
                            <TableCell>
                              <Typography variant="body2" fontWeight="medium">
                                {transaction.description}
                              </Typography>
                              {transaction.metadata.planId && (
                                <Typography variant="caption" color="text.secondary">
                                  План: {transaction.metadata.planId}
                                </Typography>
                              )}
                            </TableCell>
                            
                            <TableCell>
                              <Typography variant="body2">
                                {getTypeText(transaction.type)}
                              </Typography>
                            </TableCell>
                            
                            <TableCell>
                              <Typography variant="body2" fontWeight="medium">
                                {formatCurrency(transaction.amount, transaction.currency)}
                              </Typography>
                              {transaction.refund && (
                                <Typography variant="caption" color="error.main">
                                  Возврат: {formatCurrency(transaction.refund.amount, transaction.currency)}
                                </Typography>
                              )}
                            </TableCell>
                            
                            <TableCell>
                              <Chip
                                label={getStatusText(transaction.status)}
                                color={getStatusColor(transaction.status) as any}
                                size="small"
                                icon={getStatusIcon(transaction.status)}
                                variant="filled"
                              />
                            </TableCell>
                            
                            <TableCell align="right">
                              <IconButton
                                onClick={(e) => handleMenuOpen(e, transaction.id)}
                                size="small"
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  
                  <TablePagination
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    component="div"
                    count={filteredTransactions.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage="Строк на странице:"
                    labelDisplayedRows={({ from, to, count }) => 
                      `${from}-${to} из ${count !== -1 ? count : `более ${to}`}`
                    }
                  />
                </Paper>
              </Fade>
            )}

            {/* Transaction Menu */}
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={() => {
                const transaction = transactionHistory.find(t => t.id === menuTransactionId);
                if (transaction) handleViewDetails(transaction);
              }}>
                <ReceiptIcon sx={{ mr: 1 }} fontSize="small" />
                Подробности
              </MenuItem>
              
              {menuTransactionId && (
                <MenuItem onClick={() => handleDownloadReceipt(menuTransactionId)}>
                  <DownloadIcon sx={{ mr: 1 }} fontSize="small" />
                  Скачать чек
                </MenuItem>
              )}
            </Menu>

            {/* Transaction Details Dialog */}
            <Dialog
              open={showDetailsDialog}
              onClose={() => setShowDetailsDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Детали транзакции
              </DialogTitle>
              <DialogContent>
                {selectedTransaction && (
                  <Box sx={{ pt: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      ID транзакции
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {selectedTransaction.id}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                      Описание
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {selectedTransaction.description}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                      Сумма
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {formatCurrency(selectedTransaction.amount, selectedTransaction.currency)}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                      Способ оплаты
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {selectedTransaction.paymentMethod.type === 'card' 
                        ? `**** ${selectedTransaction.paymentMethod.details.last4}`
                        : selectedTransaction.paymentMethod.type
                      }
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                      Дата создания
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {new Date(selectedTransaction.createdAt).toLocaleString('ru-RU')}
                    </Typography>

                    {selectedTransaction.processedAt && (
                      <>
                        <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                          Дата обработки
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {new Date(selectedTransaction.processedAt).toLocaleString('ru-RU')}
                        </Typography>
                      </>
                    )}

                    {selectedTransaction.failureReason && (
                      <>
                        <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                          Причина ошибки
                        </Typography>
                        <Typography variant="body1" color="error.main" gutterBottom>
                          {selectedTransaction.failureReason}
                        </Typography>
                      </>
                    )}
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowDetailsDialog(false)}>
                  Закрыть
                </Button>
                {selectedTransaction?.receipt && (
                  <Button
                    onClick={() => window.open(selectedTransaction.receipt!.url, '_blank')}
                    startIcon={<DownloadIcon />}
                  >
                    Скачать чек
                  </Button>
                )}
              </DialogActions>
            </Dialog>

            {/* Filters Dialog */}
            <Dialog
              open={showFiltersDialog}
              onClose={() => setShowFiltersDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Фильтры
              </DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControl fullWidth>
                    <InputLabel>Статус</InputLabel>
                    <Select
                      value={filters.status}
                      onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                      label="Статус"
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="completed">Завершен</MenuItem>
                      <MenuItem value="pending">В обработке</MenuItem>
                      <MenuItem value="failed">Ошибка</MenuItem>
                      <MenuItem value="cancelled">Отменен</MenuItem>
                      <MenuItem value="refunded">Возврат</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Тип</InputLabel>
                    <Select
                      value={filters.type}
                      onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      label="Тип"
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="subscription">Подписка</MenuItem>
                      <MenuItem value="one_time">Разовый платеж</MenuItem>
                      <MenuItem value="refund">Возврат</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label="Дата от"
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
                    InputLabelProps={{ shrink: true }}
                  />

                  <TextField
                    fullWidth
                    label="Дата до"
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
                    InputLabelProps={{ shrink: true }}
                  />

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <TextField
                      label="Сумма от"
                      type="number"
                      value={filters.amountMin}
                      onChange={(e) => setFilters({ ...filters, amountMin: e.target.value })}
                      fullWidth
                    />
                    <TextField
                      label="Сумма до"
                      type="number"
                      value={filters.amountMax}
                      onChange={(e) => setFilters({ ...filters, amountMax: e.target.value })}
                      fullWidth
                    />
                  </Box>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => {
                  setFilters({
                    status: '',
                    type: '',
                    dateFrom: '',
                    dateTo: '',
                    amountMin: '',
                    amountMax: ''
                  });
                }}>
                  Сбросить
                </Button>
                <Button onClick={() => setShowFiltersDialog(false)} variant="contained">
                  Применить
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PaymentHistoryPage;
