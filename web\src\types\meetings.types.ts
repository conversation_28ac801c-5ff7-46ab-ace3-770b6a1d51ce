export interface MeetingParticipant {
  id: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  isOnline: boolean;
  verificationStatus: {
    phone: boolean;
    email: boolean;
    photo: boolean;
    document: boolean;
  };
  role: 'organizer' | 'participant' | 'invited';
  status: 'confirmed' | 'pending' | 'declined' | 'maybe';
  joinedAt?: string;
  respondedAt?: string;
}

export interface MeetingLocation {
  type: 'physical' | 'online' | 'hybrid';
  name: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  venue?: {
    id: string;
    name: string;
    type: 'cafe' | 'restaurant' | 'park' | 'cinema' | 'museum' | 'other';
    rating: number;
    priceRange: '$' | '$$' | '$$$' | '$$$$';
    photos: string[];
    amenities: string[];
  };
  onlineDetails?: {
    platform: 'zoom' | 'meet' | 'teams' | 'discord' | 'other';
    link: string;
    meetingId?: string;
    password?: string;
  };
}

export interface Meeting {
  id: string;
  title: string;
  description?: string;
  type: 'date' | 'group_meeting' | 'activity' | 'casual' | 'business';
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'cancelled';
  privacy: 'private' | 'friends' | 'public';
  
  organizer: MeetingParticipant;
  participants: MeetingParticipant[];
  maxParticipants?: number;
  
  scheduledAt: string;
  duration: number; // in minutes
  endTime?: string;
  
  location: MeetingLocation;
  
  tags: string[];
  category: string;
  
  requirements?: {
    ageRange?: { min: number; max: number };
    gender?: 'male' | 'female' | 'any';
    verifiedOnly: boolean;
    premiumOnly: boolean;
    interests?: string[];
  };
  
  cost?: {
    type: 'free' | 'paid' | 'split' | 'host_pays';
    amount?: number;
    currency: string;
    description?: string;
  };
  
  photos: string[];
  
  createdAt: string;
  updatedAt: string;
  
  statistics: {
    views: number;
    interested: number;
    confirmed: number;
    declined: number;
  };
  
  reminders: {
    enabled: boolean;
    times: number[]; // minutes before meeting
  };
  
  chat?: {
    id: string;
    enabled: boolean;
    messagesCount: number;
  };
  
  feedback?: {
    rating: number;
    reviews: MeetingReview[];
  };
}

export interface MeetingReview {
  id: string;
  authorId: string;
  authorName: string;
  rating: number;
  comment?: string;
  createdAt: string;
  isVerified: boolean;
}

export interface MeetingRequest {
  id: string;
  meetingId: string;
  meeting: Meeting;
  requesterId: string;
  requester: MeetingParticipant;
  message?: string;
  status: 'pending' | 'approved' | 'declined' | 'cancelled';
  createdAt: string;
  respondedAt?: string;
  responseMessage?: string;
}

export interface CreateMeetingRequest {
  title: string;
  description?: string;
  type: Meeting['type'];
  privacy: Meeting['privacy'];
  scheduledAt: string;
  duration: number;
  location: MeetingLocation;
  tags: string[];
  category: string;
  maxParticipants?: number;
  requirements?: Meeting['requirements'];
  cost?: Meeting['cost'];
  photos?: File[];
  reminders?: Meeting['reminders'];
}

export interface UpdateMeetingRequest {
  title?: string;
  description?: string;
  scheduledAt?: string;
  duration?: number;
  location?: MeetingLocation;
  tags?: string[];
  maxParticipants?: number;
  requirements?: Meeting['requirements'];
  cost?: Meeting['cost'];
  status?: Meeting['status'];
}

export interface MeetingFilters {
  type?: Meeting['type'][];
  category?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
  location?: {
    type?: MeetingLocation['type'];
    radius?: number;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  cost?: {
    type?: Meeting['cost']['type'][];
    maxAmount?: number;
  };
  requirements?: {
    ageRange?: { min: number; max: number };
    verifiedOnly?: boolean;
    premiumOnly?: boolean;
  };
  tags?: string[];
  sortBy?: 'date' | 'popularity' | 'distance' | 'created' | 'price';
  sortOrder?: 'asc' | 'desc';
}

export interface MeetingSearchResult {
  meetings: Meeting[];
  totalCount: number;
  filters: MeetingFilters;
  suggestions: string[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface MeetingInvitation {
  id: string;
  meetingId: string;
  meeting: Meeting;
  inviterId: string;
  inviter: MeetingParticipant;
  inviteeId: string;
  invitee: MeetingParticipant;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: string;
  expiresAt: string;
  respondedAt?: string;
}

export interface MeetingNotification {
  id: string;
  type: 'invitation' | 'reminder' | 'update' | 'cancellation' | 'new_participant' | 'message';
  meetingId: string;
  meeting: Meeting;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

export interface MeetingStatistics {
  totalMeetings: number;
  upcomingMeetings: number;
  completedMeetings: number;
  cancelledMeetings: number;
  averageRating: number;
  totalParticipants: number;
  popularCategories: {
    category: string;
    count: number;
  }[];
  popularLocations: {
    location: string;
    count: number;
  }[];
  monthlyStats: {
    month: string;
    meetings: number;
    participants: number;
  }[];
}

export interface MeetingContextType {
  // State
  meetings: Meeting[];
  myMeetings: Meeting[];
  meetingRequests: MeetingRequest[];
  invitations: MeetingInvitation[];
  notifications: MeetingNotification[];
  loading: boolean;
  error: string | null;
  filters: MeetingFilters;
  
  // Actions
  loadMeetings: (filters?: MeetingFilters) => Promise<void>;
  loadMyMeetings: () => Promise<void>;
  loadMeetingRequests: () => Promise<void>;
  loadInvitations: () => Promise<void>;
  getMeeting: (id: string) => Promise<Meeting>;
  createMeeting: (request: CreateMeetingRequest) => Promise<Meeting>;
  updateMeeting: (id: string, request: UpdateMeetingRequest) => Promise<Meeting>;
  deleteMeeting: (id: string) => Promise<void>;
  joinMeeting: (id: string, message?: string) => Promise<void>;
  leaveMeeting: (id: string) => Promise<void>;
  inviteToMeeting: (meetingId: string, userIds: string[], message?: string) => Promise<void>;
  respondToInvitation: (invitationId: string, status: 'accepted' | 'declined', message?: string) => Promise<void>;
  respondToRequest: (requestId: string, status: 'approved' | 'declined', message?: string) => Promise<void>;
  cancelMeeting: (id: string, reason?: string) => Promise<void>;
  rateMeeting: (id: string, rating: number, comment?: string) => Promise<void>;
  reportMeeting: (id: string, reason: string, details?: string) => Promise<void>;
  searchMeetings: (query: string, filters?: MeetingFilters) => Promise<MeetingSearchResult>;
  updateFilters: (filters: Partial<MeetingFilters>) => void;
  markNotificationAsRead: (id: string) => Promise<void>;
  getStatistics: () => Promise<MeetingStatistics>;
}

export interface MeetingTemplate {
  id: string;
  name: string;
  description: string;
  type: Meeting['type'];
  category: string;
  defaultDuration: number;
  defaultLocation: Partial<MeetingLocation>;
  suggestedTags: string[];
  isPopular: boolean;
  usageCount: number;
}

export interface MeetingSuggestion {
  id: string;
  type: 'similar_interests' | 'nearby_location' | 'popular_time' | 'friend_activity' | 'trending';
  meeting: Meeting;
  reason: string;
  score: number;
  expiresAt: string;
}

export interface MeetingChat {
  id: string;
  meetingId: string;
  participants: MeetingParticipant[];
  messages: MeetingChatMessage[];
  isActive: boolean;
  createdAt: string;
}

export interface MeetingChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  text: string;
  type: 'text' | 'system' | 'announcement';
  sentAt: string;
  isEdited: boolean;
  editedAt?: string;
}

export interface MeetingReminder {
  id: string;
  meetingId: string;
  userId: string;
  type: 'push' | 'email' | 'sms';
  scheduledAt: string;
  message: string;
  isSent: boolean;
  sentAt?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
