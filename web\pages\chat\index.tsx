import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  Badge,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Fab,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Message as MessageIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Archive as ArchiveIcon,
  VolumeOff as MuteIcon,
  PushPin as PinIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  Add as AddIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  CheckCircle as ReadIcon,
  Circle as UnreadIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getConversations,
  archiveConversation,
  muteConversation,
  pinConversation,
  deleteConversation
} from '../../src/services/chatService';
import { Conversation, ChatFilter } from '../../src/types/chat.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`chat-tabpanel-${index}`}
      aria-labelledby={`chat-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const ChatListPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const tabs = [
    { label: 'Все', filter: 'all' },
    { label: 'Непрочитанные', filter: 'unread' },
    { label: 'Закрепленные', filter: 'pinned' },
    { label: 'Архив', filter: 'archived' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadConversations();
  }, [user, router, activeTab]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      setError(null);

      const filter: ChatFilter = {
        type: tabs[activeTab].filter as any,
        searchQuery: searchQuery || undefined
      };

      const response = await getConversations(filter);
      setConversations(response.data);
    } catch (err: any) {
      setError('Ошибка загрузки чатов');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, conversation: Conversation) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedConversation(conversation);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedConversation(null);
  };

  const handleConversationClick = (conversation: Conversation) => {
    router.push(`/chat/${conversation.id}`);
  };

  const handleArchive = async () => {
    if (!selectedConversation) return;
    
    try {
      setActionLoading('archive');
      await archiveConversation(selectedConversation.id);
      setSuccess('Чат архивирован');
      setConversations(prev => prev.filter(c => c.id !== selectedConversation.id));
      handleMenuClose();
    } catch (err: any) {
      setError('Ошибка архивирования чата');
    } finally {
      setActionLoading(null);
    }
  };

  const handleMute = async () => {
    if (!selectedConversation) return;
    
    try {
      setActionLoading('mute');
      await muteConversation(selectedConversation.id, 24 * 60); // 24 hours
      setSuccess('Уведомления отключены на 24 часа');
      setConversations(prev => prev.map(c => 
        c.id === selectedConversation.id ? { ...c, isMuted: true } : c
      ));
      handleMenuClose();
    } catch (err: any) {
      setError('Ошибка отключения уведомлений');
    } finally {
      setActionLoading(null);
    }
  };

  const handlePin = async () => {
    if (!selectedConversation) return;
    
    try {
      setActionLoading('pin');
      await pinConversation(selectedConversation.id);
      setSuccess('Чат закреплен');
      setConversations(prev => prev.map(c => 
        c.id === selectedConversation.id ? { ...c, isPinned: true } : c
      ));
      handleMenuClose();
    } catch (err: any) {
      setError('Ошибка закрепления чата');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async () => {
    if (!selectedConversation) return;
    
    try {
      setActionLoading('delete');
      await deleteConversation(selectedConversation.id);
      setSuccess('Чат удален');
      setConversations(prev => prev.filter(c => c.id !== selectedConversation.id));
      handleMenuClose();
    } catch (err: any) {
      setError('Ошибка удаления чата');
    } finally {
      setActionLoading(null);
    }
  };

  const formatLastMessageTime = (dateString: string) => {
    const now = new Date();
    const messageTime = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч.`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) return 'Вчера';
      if (diffInDays < 7) return `${diffInDays} дн.`;
      return messageTime.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' });
    }
  };

  const getOtherParticipant = (conversation: Conversation) => {
    return conversation.participants.find(p => p.id !== user?.id);
  };

  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    
    const otherParticipant = getOtherParticipant(conversation);
    const searchLower = searchQuery.toLowerCase();
    
    return (
      otherParticipant?.firstName.toLowerCase().includes(searchLower) ||
      otherParticipant?.lastName.toLowerCase().includes(searchLower) ||
      conversation.lastMessage?.text?.toLowerCase().includes(searchLower)
    );
  });

  const unreadCount = conversations.filter(c => c.unreadCount > 0).length;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Чаты - Likes & Love</title>
        <meta 
          name="description" 
          content="Ваши чаты и сообщения в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/chat" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
              {/* Header */}
              <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant={isMobile ? "h5" : "h4"}>
                    <Badge badgeContent={unreadCount} color="primary">
                      <MessageIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                    </Badge>
                    Чаты
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => router.push('/chat/new')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Новый чат
                  </Button>
                </Box>

                {/* Search */}
                <TextField
                  fullWidth
                  placeholder="Поиск чатов..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Box>

              {error && (
                <Alert severity="error" sx={{ m: 2 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ m: 2 }}>
                  {success}
                </Alert>
              )}

              {/* Tabs */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={activeTab}
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  variant={isMobile ? "scrollable" : "fullWidth"}
                  scrollButtons="auto"
                >
                  {tabs.map((tab, index) => (
                    <Tab 
                      key={index}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {tab.label}
                          {tab.filter === 'unread' && unreadCount > 0 && (
                            <Chip size="small" label={unreadCount} color="primary" />
                          )}
                        </Box>
                      }
                    />
                  ))}
                </Tabs>
              </Box>

              {/* Chat List */}
              <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <CircularProgress size={60} />
                  </Box>
                ) : filteredConversations.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 8 }}>
                    <MessageIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      {searchQuery ? 'Ничего не найдено' : 'У вас пока нет чатов'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      {searchQuery 
                        ? 'Попробуйте изменить поисковый запрос'
                        : 'Начните общение с людьми, которые вам понравились'
                      }
                    </Typography>
                    {!searchQuery && (
                      <Button
                        variant="contained"
                        onClick={() => router.push('/matches')}
                      >
                        Посмотреть совпадения
                      </Button>
                    )}
                  </Box>
                ) : (
                  <Fade in timeout={600}>
                    <List sx={{ height: '100%', overflow: 'auto', py: 0 }}>
                      {filteredConversations.map((conversation) => {
                        const otherParticipant = getOtherParticipant(conversation);
                        if (!otherParticipant) return null;

                        return (
                          <ListItem
                            key={conversation.id}
                            button
                            onClick={() => handleConversationClick(conversation)}
                            sx={{
                              borderBottom: 1,
                              borderColor: 'divider',
                              '&:hover': {
                                backgroundColor: 'action.hover'
                              },
                              backgroundColor: conversation.unreadCount > 0 ? 'action.selected' : 'transparent'
                            }}
                          >
                            <ListItemAvatar>
                              <Badge
                                overlap="circular"
                                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                badgeContent={
                                  otherParticipant.isOnline ? (
                                    <Box
                                      sx={{
                                        width: 12,
                                        height: 12,
                                        borderRadius: '50%',
                                        backgroundColor: 'success.main',
                                        border: '2px solid white'
                                      }}
                                    />
                                  ) : null
                                }
                              >
                                <Avatar
                                  src={otherParticipant.avatarUrl}
                                  sx={{ width: 56, height: 56 }}
                                >
                                  {otherParticipant.firstName[0]}
                                </Avatar>
                              </Badge>
                            </ListItemAvatar>

                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography 
                                    variant="subtitle1" 
                                    sx={{ 
                                      fontWeight: conversation.unreadCount > 0 ? 'bold' : 'normal',
                                      flexGrow: 1
                                    }}
                                  >
                                    {otherParticipant.firstName} {otherParticipant.lastName}
                                  </Typography>
                                  {otherParticipant.verificationStatus.phone && (
                                    <VerifiedIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                                  )}
                                  {conversation.isPinned && (
                                    <PinIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                                  )}
                                  {conversation.isMuted && (
                                    <MuteIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography 
                                    variant="body2" 
                                    color="text.secondary"
                                    sx={{ 
                                      flexGrow: 1,
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap'
                                    }}
                                  >
                                    {conversation.lastMessage?.text || 'Нет сообщений'}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {conversation.lastMessageAt && formatLastMessageTime(conversation.lastMessageAt)}
                                  </Typography>
                                </Box>
                              }
                            />

                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              {conversation.unreadCount > 0 && (
                                <Badge badgeContent={conversation.unreadCount} color="primary" />
                              )}
                              
                              <IconButton
                                size="small"
                                onClick={(e) => handleMenuOpen(e, conversation)}
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </Box>
                          </ListItem>
                        );
                      })}
                    </List>
                  </Fade>
                )}
              </Box>
            </Paper>
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handlePin} disabled={actionLoading === 'pin'}>
            <PinIcon sx={{ mr: 1 }} />
            {selectedConversation?.isPinned ? 'Открепить' : 'Закрепить'}
          </MenuItem>
          <MenuItem onClick={handleMute} disabled={actionLoading === 'mute'}>
            <MuteIcon sx={{ mr: 1 }} />
            {selectedConversation?.isMuted ? 'Включить уведомления' : 'Отключить уведомления'}
          </MenuItem>
          <MenuItem onClick={handleArchive} disabled={actionLoading === 'archive'}>
            <ArchiveIcon sx={{ mr: 1 }} />
            Архивировать
          </MenuItem>
          <MenuItem onClick={handleDelete} disabled={actionLoading === 'delete'} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1 }} />
            Удалить чат
          </MenuItem>
        </Menu>

        {/* FAB for mobile */}
        {isMobile && (
          <Fab
            color="primary"
            sx={{
              position: 'fixed',
              bottom: 80,
              right: 16
            }}
            onClick={() => router.push('/chat/new')}
          >
            <AddIcon />
          </Fab>
        )}
      </Layout>
    </>
  );
};

export default ChatListPage;
