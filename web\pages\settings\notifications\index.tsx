import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormLabel,
  TextField,
  Chip
} from '@mui/material';
import {
  ArrowBack,
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  PhoneAndroid as PhoneIcon,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  Event as EventIcon,
  Schedule as ScheduleIcon,
  VolumeOff as VolumeOffIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Типы для настроек уведомлений
interface NotificationSettings {
  email: {
    matches: boolean;
    messages: boolean;
    likes: boolean;
    events: boolean;
    marketing: boolean;
    security: boolean;
  };
  push: {
    matches: boolean;
    messages: boolean;
    likes: boolean;
    events: boolean;
    reminders: boolean;
  };
  sms: {
    security: boolean;
    important: boolean;
  };
  inApp: {
    matches: boolean;
    messages: boolean;
    likes: boolean;
    events: boolean;
    system: boolean;
  };
  frequency: 'immediate' | 'daily' | 'weekly' | 'never';
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
}

// Схема валидации
const notificationSchema = yup.object({
  frequency: yup.string().oneOf(['immediate', 'daily', 'weekly', 'never']).required(),
  quietHours: yup.object({
    startTime: yup.string().required(),
    endTime: yup.string().required(),
    timezone: yup.string().required()
  })
});

const NotificationSettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch
  } = useForm<NotificationSettings>({
    resolver: yupResolver(notificationSchema),
    defaultValues: {
      email: {
        matches: true,
        messages: true,
        likes: true,
        events: true,
        marketing: false,
        security: true
      },
      push: {
        matches: true,
        messages: true,
        likes: true,
        events: true,
        reminders: true
      },
      sms: {
        security: true,
        important: false
      },
      inApp: {
        matches: true,
        messages: true,
        likes: true,
        events: true,
        system: true
      },
      frequency: 'immediate',
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
        timezone: 'Europe/Moscow'
      }
    }
  });

  useEffect(() => {
    if (user?.settings?.notifications) {
      reset(user.settings.notifications);
    }
  }, [user, reset]);

  const handleSave = async (data: NotificationSettings) => {
    try {
      setLoading(true);
      setError(null);
      
      await updateProfile({
        settings: {
          notifications: data
        }
      });

      setSuccess('Настройки уведомлений успешно обновлены');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset();
    setError(null);
  };

  if (!user) {
    return (
      <Layout title="Настройки уведомлений">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Настройки уведомлений - Likes & Love</title>
        <meta name="description" content="Управление настройками уведомлений в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Настройки уведомлений">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton 
                onClick={() => router.back()} 
                sx={{ display: { xs: 'flex', md: 'none' } }}
              >
                <ArrowBack />
              </IconButton>
              <NotificationsIcon color="primary" sx={{ fontSize: 32 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                Настройки уведомлений
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleSave)}>
              <Grid container spacing={3}>
                {/* Общие настройки */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Общие настройки
                      </Typography>

                      <Box sx={{ mb: 3 }}>
                        <Controller
                          name="frequency"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel>Частота уведомлений</InputLabel>
                              <Select {...field} label="Частота уведомлений">
                                <MenuItem value="immediate">Мгновенно</MenuItem>
                                <MenuItem value="daily">Ежедневно</MenuItem>
                                <MenuItem value="weekly">Еженедельно</MenuItem>
                                <MenuItem value="never">Никогда</MenuItem>
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Box>

                      <Alert severity="info" sx={{ mb: 3 }}>
                        <Typography variant="body2">
                          Важные уведомления безопасности будут приходить независимо от выбранной частоты
                        </Typography>
                      </Alert>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Push уведомления */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                        <PhoneIcon color="primary" />
                        <Typography variant="h6" fontWeight="bold">
                          Push уведомления
                        </Typography>
                        <Chip label="Рекомендуется" size="small" color="success" />
                      </Box>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <FavoriteIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Новые совпадения"
                            secondary="Когда кто-то лайкнул вас взаимно"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="push.matches"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <MessageIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Новые сообщения"
                            secondary="Когда вам пишут в чате"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="push.messages"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <FavoriteIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Лайки"
                            secondary="Когда кто-то лайкнул ваш профиль"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="push.likes"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Email уведомления */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                        <EmailIcon color="primary" />
                        <Typography variant="h6" fontWeight="bold">
                          Email уведомления
                        </Typography>
                      </Box>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <FavoriteIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Совпадения и лайки"
                            secondary="Еженедельная сводка активности"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="email.matches"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <MessageIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Важные сообщения"
                            secondary="Только важные уведомления"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="email.messages"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <EventIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="События и мероприятия"
                            secondary="Уведомления о новых событиях в вашем городе"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="email.events"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* SMS уведомления */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                        <SmsIcon color="primary" />
                        <Typography variant="h6" fontWeight="bold">
                          SMS уведомления
                        </Typography>
                        <Chip label="Только важные" size="small" color="warning" />
                      </Box>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <InfoIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Безопасность аккаунта"
                            secondary="Коды подтверждения и уведомления о входе"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="sms.security"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <NotificationsIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Важные уведомления"
                            secondary="Критически важная информация"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="sms.important"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Тихие часы */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                        <VolumeOffIcon color="primary" />
                        <Typography variant="h6" fontWeight="bold">
                          Тихие часы
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Controller
                          name="quietHours.enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              }
                              label="Включить тихие часы"
                            />
                          )}
                        />
                      </Box>

                      {watch('quietHours.enabled') && (
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Controller
                              name="quietHours.startTime"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Начало"
                                  type="time"
                                  fullWidth
                                  InputLabelProps={{ shrink: true }}
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={6}>
                            <Controller
                              name="quietHours.endTime"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Конец"
                                  type="time"
                                  fullWidth
                                  InputLabelProps={{ shrink: true }}
                                />
                              )}
                            />
                          </Grid>
                        </Grid>
                      )}

                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          В тихие часы вы не будете получать push-уведомления, кроме критически важных
                        </Typography>
                      </Alert>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Кнопки сохранения */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      onClick={handleCancel}
                      startIcon={<CancelIcon />}
                      disabled={loading}
                    >
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                      disabled={loading || !isDirty}
                    >
                      Сохранить
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NotificationSettingsPage;
