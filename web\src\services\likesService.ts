import axios from 'axios';
import { config } from '../config';
import {
  Like,
  Match,
  LikeActionRequest,
  LikeActionResponse,
  LikesStats,
  FilterOptions,
  PaginatedResponse,
  MatchDetailsResponse,
  SendMessageRequest,
  SendMessageResponse,
  CompatibilityAnalysis,
  MeetingSuggestion,
  IceBreaker
} from '../types/likes.types';

const likesApi = axios.create({
  baseURL: `${config.api.baseUrl}/likes`,
  withCredentials: true,
});

const matchesApi = axios.create({
  baseURL: `${config.api.baseUrl}/matches`,
  withCredentials: true,
});

// Likes API
export const getReceivedLikes = async (filters?: FilterOptions): Promise<PaginatedResponse<Like>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await likesApi.get(`/received?${params.toString()}`);
  return data;
};

export const getSentLikes = async (filters?: FilterOptions): Promise<PaginatedResponse<Like>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await likesApi.get(`/sent?${params.toString()}`);
  return data;
};

export const getMutualLikes = async (filters?: FilterOptions): Promise<PaginatedResponse<Like>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await likesApi.get(`/mutual?${params.toString()}`);
  return data;
};

export const sendLike = async (request: LikeActionRequest): Promise<LikeActionResponse> => {
  try {
    const { data } = await likesApi.post('/send', request);
    return {
      success: true,
      message: data.message || 'Лайк отправлен',
      isMatch: data.isMatch || false,
      match: data.match
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка отправки лайка',
      isMatch: false,
      error: error.response?.data?.error
    };
  }
};

export const markLikesAsRead = async (likeIds: string[]): Promise<void> => {
  await likesApi.post('/mark-read', { likeIds });
};

export const getLikesStats = async (): Promise<LikesStats> => {
  const { data } = await likesApi.get('/stats');
  return data.stats;
};

// Matches API
export const getMatches = async (filters?: FilterOptions): Promise<PaginatedResponse<Match>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await matchesApi.get(`/?${params.toString()}`);
  return data;
};

export const getMatchDetails = async (matchId: string): Promise<MatchDetailsResponse> => {
  const { data } = await matchesApi.get(`/${matchId}`);
  return data;
};

export const markMatchesAsRead = async (matchIds: string[]): Promise<void> => {
  await matchesApi.post('/mark-read', { matchIds });
};

export const archiveMatch = async (matchId: string): Promise<void> => {
  await matchesApi.post(`/${matchId}/archive`);
};

export const unarchiveMatch = async (matchId: string): Promise<void> => {
  await matchesApi.post(`/${matchId}/unarchive`);
};

export const blockMatch = async (matchId: string, reason?: string): Promise<void> => {
  await matchesApi.post(`/${matchId}/block`, { reason });
};

export const reportMatch = async (matchId: string, reason: string, details?: string): Promise<void> => {
  await matchesApi.post(`/${matchId}/report`, { reason, details });
};

// Messages API
export const sendMessage = async (request: SendMessageRequest): Promise<SendMessageResponse> => {
  try {
    let requestData: any = {
      conversationId: request.conversationId,
      text: request.text,
      type: request.type,
      replyToMessageId: request.replyToMessageId
    };

    if (request.attachments && request.attachments.length > 0) {
      const formData = new FormData();
      Object.entries(requestData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      request.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });

      const { data } = await matchesApi.post('/messages', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        message: data.message
      };
    } else {
      const { data } = await matchesApi.post('/messages', requestData);
      return {
        success: true,
        message: data.message
      };
    }
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Ошибка отправки сообщения'
    };
  }
};

export const getMessages = async (conversationId: string, page: number = 1, limit: number = 50): Promise<PaginatedResponse<any>> => {
  const { data } = await matchesApi.get(`/conversations/${conversationId}/messages?page=${page}&limit=${limit}`);
  return data;
};

export const markMessagesAsRead = async (conversationId: string, messageIds: string[]): Promise<void> => {
  await matchesApi.post(`/conversations/${conversationId}/mark-read`, { messageIds });
};

export const deleteMessage = async (messageId: string): Promise<void> => {
  await matchesApi.delete(`/messages/${messageId}`);
};

export const editMessage = async (messageId: string, newText: string): Promise<void> => {
  await matchesApi.put(`/messages/${messageId}`, { text: newText });
};

export const addMessageReaction = async (messageId: string, emoji: string): Promise<void> => {
  await matchesApi.post(`/messages/${messageId}/reactions`, { emoji });
};

export const removeMessageReaction = async (messageId: string, reactionId: string): Promise<void> => {
  await matchesApi.delete(`/messages/${messageId}/reactions/${reactionId}`);
};

// Compatibility API
export const getCompatibilityAnalysis = async (userId1: string, userId2: string): Promise<CompatibilityAnalysis> => {
  const { data } = await matchesApi.get(`/compatibility/${userId1}/${userId2}`);
  return data.analysis;
};

// Meeting suggestions API
export const getMeetingSuggestions = async (matchId: string, type?: string): Promise<MeetingSuggestion[]> => {
  const params = type ? `?type=${type}` : '';
  const { data } = await matchesApi.get(`/${matchId}/meeting-suggestions${params}`);
  return data.suggestions;
};

export const bookMeeting = async (matchId: string, suggestionId: string, dateTime: string): Promise<{ success: boolean; bookingId?: string; error?: string }> => {
  try {
    const { data } = await matchesApi.post(`/${matchId}/book-meeting`, {
      suggestionId,
      dateTime
    });
    return {
      success: true,
      bookingId: data.bookingId
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Ошибка бронирования встречи'
    };
  }
};

// Ice breakers API
export const getIceBreakers = async (targetUserId: string): Promise<IceBreaker[]> => {
  const { data } = await likesApi.get(`/ice-breakers/${targetUserId}`);
  return data.iceBreakers;
};

// Boost API
export const boostProfile = async (duration: number): Promise<{ success: boolean; message: string; expiresAt?: string }> => {
  const { data } = await likesApi.post('/boost', { duration });
  return data;
};

export const getBoostStatus = async (): Promise<{ isActive: boolean; expiresAt?: string; remainingBoosts: number }> => {
  const { data } = await likesApi.get('/boost/status');
  return data;
};

// Super likes API
export const getSuperLikesCount = async (): Promise<{ available: number; total: number; nextRefresh: string }> => {
  const { data } = await likesApi.get('/super-likes/count');
  return data;
};

export const purchaseSuperLikes = async (count: number): Promise<{ success: boolean; message: string; newCount?: number }> => {
  try {
    const { data } = await likesApi.post('/super-likes/purchase', { count });
    return {
      success: true,
      message: data.message,
      newCount: data.newCount
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка покупки супер-лайков'
    };
  }
};

// Undo API
export const undoLastAction = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const { data } = await likesApi.post('/undo');
    return {
      success: true,
      message: data.message || 'Последнее действие отменено'
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка отмены действия'
    };
  }
};
