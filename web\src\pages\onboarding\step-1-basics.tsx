import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  Typo<PERSON>, 
  Box, 
  Button, 
  Stepper, 
  Step, 
  StepLabel,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { ru } from 'date-fns/locale';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuthContext } from '@/contexts/AuthContext';
import { updateUserProfile } from '@/services/userService';
import OnboardingLayout from '@/components/Onboarding/OnboardingLayout';
import { OnboardingSteps } from '@/constants/onboarding';

interface BasicInfoFormData {
  firstName: string;
  lastName: string;
  birthDate: Date | null;
  gender: string;
  location: string;
  occupation: string;
}

const schema = yup.object({
  firstName: yup.string().required('Имя обязательно'),
  lastName: yup.string().required('Фамилия обязательна'),
  birthDate: yup.date()
    .required('Дата рождения обязательна')
    .max(new Date(new Date().setFullYear(new Date().getFullYear() - 18)), 'Вам должно быть не менее 18 лет'),
  gender: yup.string().required('Пол обязателен'),
  location: yup.string().required('Местоположение обязательно'),
  occupation: yup.string().required('Род занятий обязателен')
});

export default function OnboardingBasics() {
  const router = useRouter();
  const { user, updateUser } = useAuthContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { control, handleSubmit, formState: { errors } } = useForm<BasicInfoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      birthDate: user?.birthDate ? new Date(user.birthDate) : null,
      gender: user?.gender || '',
      location: user?.location || '',
      occupation: user?.occupation || ''
    }
  });

  const onSubmit = async (data: BasicInfoFormData) => {
    setLoading(true);
    setError('');
    
    try {
      const updatedUser = await updateUserProfile({
        firstName: data.firstName,
        lastName: data.lastName,
        birthDate: data.birthDate?.toISOString(),
        gender: data.gender,
        location: data.location,
        occupation: data.occupation
      });
      
      updateUser(updatedUser);
      router.push('/onboarding/step-2-interests');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении данных. Пожалуйста, попробуйте снова.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <OnboardingLayout activeStep={0}>
      <Head>
        <title>Основная информация | Likes & Love</title>
        <meta name="description" content="Заполните основную информацию о себе для создания профиля в Likes & Love" />
      </Head>
      
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          width: '100%', 
          maxWidth: 800,
          borderRadius: 2,
          mx: 'auto'
        }}
      >
        <Typography variant="h5" component="h1" gutterBottom align="center">
          Расскажите о себе
        </Typography>
        
        <Typography variant="body1" sx={{ mb: 4, textAlign: 'center' }}>
          Эта информация поможет нам создать ваш профиль и найти подходящие пары
        </Typography>
        
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Имя"
                    variant="outlined"
                    fullWidth
                    error={!!errors.firstName}
                    helperText={errors.firstName?.message}
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Фамилия"
                    variant="outlined"
                    fullWidth
                    error={!!errors.lastName}
                    helperText={errors.lastName?.message}
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
                <Controller
                  name="birthDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Дата рождения"
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          error: !!errors.birthDate,
                          helperText: errors.birthDate?.message
                        }
                      }}
                    />
                  )}
                />
              </LocalizationProvider>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Controller
                name="gender"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.gender}>
                    <InputLabel id="gender-label">Пол</InputLabel>
                    <Select
                      {...field}
                      labelId="gender-label"
                      label="Пол"
                    >
                      <MenuItem value="male">Мужской</MenuItem>
                      <MenuItem value="female">Женский</MenuItem>
                      <MenuItem value="other">Другой</MenuItem>
                      <MenuItem value="prefer_not_to_say">Предпочитаю не указывать</MenuItem>
                    </Select>
                    {errors.gender && (
                      <FormHelperText>{errors.gender.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Controller
                name="location"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Местоположение"
                    variant="outlined"
                    fullWidth
                    error={!!errors.location}
                    helperText={errors.location?.message}
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Controller
                name="occupation"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Род занятий"
                    variant="outlined"
                    fullWidth
                    error={!!errors.occupation}
                    helperText={errors.occupation?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button 
              variant="outlined"
              onClick={() => router.push('/')}
            >
              Пропустить
            </Button>
            
            <Button 
              type="submit"
              variant="contained" 
              color="primary"
              size="large"
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Продолжить'
              )}
            </Button>
          </Box>
        </form>
      </Paper>
    </OnboardingLayout>
  );
}