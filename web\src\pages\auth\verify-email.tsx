import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { Typography, Box, CircularProgress, <PERSON><PERSON>, Button } from '@mui/material';
import { useAuthContext } from '@/contexts/AuthContext';
import { verifyEmail } from '@/services/authService';
import Layout from '@/components/Layout/Layout';
import { VerificationResult } from '@/types/auth.types';

export default function VerifyEmail() {
  const router = useRouter();
  const { token } = router.query;
  const { user, updateUser } = useAuthContext();
  const [verificationStatus, setVerificationStatus] = useState<VerificationResult>({
    loading: true,
    success: false,
    message: '',
  });

  useEffect(() => {
    if (!token || typeof token !== 'string') return;

    const verify = async () => {
      try {
        const result = await verifyEmail(token);
        setVerificationStatus({
          loading: false,
          success: true,
          message: 'Ваш email успешно подтвержден!'
        });
        
        if (user) {
          updateUser({
            ...user,
            emailVerified: true
          });
        }
      } catch (error: any) {
        setVerificationStatus({
          loading: false,
          success: false,
          message: error.message || 'Ошибка при подтверждении email'
        });
      }
    };

    verify();
  }, [token, user, updateUser]);

  const handleContinue = () => {
    if (user) {
      router.push('/profile');
    } else {
      router.push('/auth/login');
    }
  };

  return (
    <Layout>
      <Head>
        <title>Подтверждение Email | Likes & Love</title>
        <meta name="description" content="Подтверждение email адреса в приложении Likes & Love" />
      </Head>
      
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: '60vh',
          padding: 3
        }}
      >
        {verificationStatus.loading ? (
          <CircularProgress size={60} />
        ) : (
          <>
            <Alert 
              severity={verificationStatus.success ? "success" : "error"}
              sx={{ mb: 3, width: '100%', maxWidth: 500 }}
            >
              {verificationStatus.message}
            </Alert>
            
            <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
              {verificationStatus.success 
                ? 'Теперь вы можете использовать все возможности приложения Likes & Love.' 
                : 'Пожалуйста, проверьте ссылку или запросите новое письмо с подтверждением.'}
            </Typography>
            
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleContinue}
            >
              {verificationStatus.success 
                ? 'Продолжить' 
                : 'Вернуться на главную'}
            </Button>
          </>
        )}
      </Box>
    </Layout>
  );
}