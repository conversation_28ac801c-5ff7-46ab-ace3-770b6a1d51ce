import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Refresh as RefreshIcon,
  Favorite as LikeIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  NewReleases as NewIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Gift as GiftIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getNewUsers,
  swipeUser
} from '../../src/services/discoverService';
import { 
  NewUser,
  SwipeAction 
} from '../../src/types/discover.types';

const NewUsersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [newUsers, setNewUsers] = useState<NewUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [daysFilter, setDaysFilter] = useState(7);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadNewUsers();
  }, [user, router, daysFilter]);

  const loadNewUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const users = await getNewUsers(daysFilter);
      setNewUsers(users);
    } catch (err: any) {
      setError('Ошибка загрузки новых пользователей');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (userId: string) => {
    try {
      setActionLoading(userId);
      setError(null);

      const swipeAction: SwipeAction = {
        userId,
        action: 'like',
        timestamp: new Date().toISOString()
      };

      const result = await swipeUser(swipeAction);
      
      if (result.success) {
        if (result.isMatch) {
          setSuccess('Это совпадение! 🎉');
        } else {
          setSuccess('Лайк отправлен');
        }
        
        // Remove user from list
        setNewUsers(prev => prev.filter(u => u.user.id !== userId));
      } else {
        setError(result.error || 'Ошибка отправки лайка');
      }
    } catch (err: any) {
      setError('Ошибка отправки лайка');
    } finally {
      setActionLoading(null);
    }
  };

  const formatJoinDate = (dateString: string) => {
    const joinDate = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else {
      return `${diffInDays} дн. назад`;
    }
  };

  const getBonusIcon = (type: string) => {
    switch (type) {
      case 'free_super_likes':
        return <StarIcon />;
      case 'free_boost':
        return <NewIcon />;
      case 'premium_trial':
        return <GiftIcon />;
      default:
        return <GiftIcon />;
    }
  };

  const getBonusLabel = (type: string, value: number) => {
    switch (type) {
      case 'free_super_likes':
        return `${value} бесплатных супер-лайков`;
      case 'free_boost':
        return `${value} бесплатных буста`;
      case 'premium_trial':
        return `${value} дней премиума`;
      default:
        return 'Бонус';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Новые пользователи - Likes & Love</title>
        <meta 
          name="description" 
          content="Познакомьтесь с новыми пользователями в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/discover/new" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <NewIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'primary.main' }} />
                Новые пользователи
              </Typography>
              <IconButton onClick={loadNewUsers} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Filter */}
            <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Период</InputLabel>
                <Select
                  value={daysFilter}
                  label="Период"
                  onChange={(e) => setDaysFilter(e.target.value as number)}
                >
                  <MenuItem value={1}>За последний день</MenuItem>
                  <MenuItem value={3}>За последние 3 дня</MenuItem>
                  <MenuItem value={7}>За последнюю неделю</MenuItem>
                  <MenuItem value={14}>За последние 2 недели</MenuItem>
                  <MenuItem value={30}>За последний месяц</MenuItem>
                </Select>
              </FormControl>
            </Paper>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка новых пользователей...
                </Typography>
              </Box>
            ) : newUsers.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <NewIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Новых пользователей не найдено
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Попробуйте изменить период поиска
                </Typography>
                <Button
                  variant="contained"
                  onClick={loadNewUsers}
                >
                  Обновить
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Найдено {newUsers.length} новых {newUsers.length === 1 ? 'пользователь' : 'пользователей'}
                  </Typography>
                  
                  <Grid container spacing={3}>
                    {newUsers.map((newUser) => (
                      <Grid item xs={12} sm={6} md={4} key={newUser.user.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ position: 'relative' }}>
                            {/* New Badge */}
                            <Box sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<NewIcon />}
                                label="Новичок"
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>

                            {/* Verification Badge */}
                            {newUser.user.verificationStatus.phone && (
                              <Box sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                zIndex: 1,
                                backgroundColor: 'primary.main',
                                borderRadius: '50%',
                                p: 0.5
                              }}>
                                <VerifiedIcon sx={{ color: 'white', fontSize: 16 }} />
                              </Box>
                            )}

                            {/* New to Area Badge */}
                            {newUser.isNewToArea && (
                              <Box sx={{
                                position: 'absolute',
                                bottom: 8,
                                left: 8,
                                zIndex: 1
                              }}>
                                <Chip
                                  label="Новый в городе"
                                  size="small"
                                  color="secondary"
                                  sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                                />
                              </Box>
                            )}

                            <CardMedia
                              component="img"
                              height="250"
                              image={newUser.user.photos[0]?.url || '/default-avatar.png'}
                              alt={newUser.user.firstName}
                              sx={{ objectFit: 'cover' }}
                            />
                          </Box>

                          <CardContent sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" gutterBottom>
                              {newUser.user.firstName}, {newUser.user.age}
                            </Typography>

                            {newUser.user.location && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {newUser.user.location.city}
                                  {newUser.user.location.distance && ` • ${newUser.user.location.distance} км`}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary">
                                Присоединился {formatJoinDate(newUser.joinedAt)} • {newUser.daysOnPlatform} дн. на сайте
                              </Typography>
                            </Box>

                            {/* Welcome Bonus */}
                            {newUser.welcomeBonus && (
                              <Box sx={{ mb: 2 }}>
                                <Chip
                                  icon={getBonusIcon(newUser.welcomeBonus.type)}
                                  label={getBonusLabel(newUser.welcomeBonus.type, newUser.welcomeBonus.value)}
                                  size="small"
                                  color="warning"
                                  variant="outlined"
                                />
                              </Box>
                            )}

                            {newUser.user.occupation && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {newUser.user.occupation}
                                </Typography>
                              </Box>
                            )}

                            {newUser.user.education && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {newUser.user.education}
                                </Typography>
                              </Box>
                            )}

                            {newUser.user.bio && (
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                {newUser.user.bio.length > 100 
                                  ? `${newUser.user.bio.substring(0, 100)}...`
                                  : newUser.user.bio
                                }
                              </Typography>
                            )}

                            {newUser.user.interests.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {newUser.user.interests.slice(0, 3).map((interest, index) => (
                                  <Chip
                                    key={index}
                                    label={interest}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                                {newUser.user.interests.length > 3 && (
                                  <Chip
                                    label={`+${newUser.user.interests.length - 3}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            )}
                          </CardContent>

                          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                            <Button
                              variant="outlined"
                              onClick={() => router.push(`/users/${newUser.user.id}`)}
                              size="small"
                            >
                              Профиль
                            </Button>
                            
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                color="primary"
                                onClick={() => handleLike(newUser.user.id)}
                                disabled={actionLoading === newUser.user.id}
                                sx={{
                                  backgroundColor: 'error.light',
                                  color: 'error.dark',
                                  '&:hover': { backgroundColor: 'error.main', color: 'white' }
                                }}
                              >
                                {actionLoading === newUser.user.id ? (
                                  <CircularProgress size={20} />
                                ) : (
                                  <LikeIcon />
                                )}
                              </IconButton>
                              
                              <IconButton
                                color="primary"
                                onClick={() => router.push(`/chat/new?userId=${newUser.user.id}`)}
                              >
                                <MessageIcon />
                              </IconButton>
                            </Box>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NewUsersPage;
