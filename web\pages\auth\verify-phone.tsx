import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Stack,
  TextField,
  InputAdornment
} from '@mui/material';
import { CheckCircle, Phone, Refresh, Sms } from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { verifyPhoneWithData } from '../../src/services/authService';

const VerifyPhonePage: React.FC = () => {
  const router = useRouter();
  const { phone } = router.query;
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const [resending, setResending] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (!phone) {
      setStatus('error');
      setMessage('Номер телефона не указан');
      return;
    }

    // Запускаем таймер обратного отсчета
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [phone]);

  const handleCodeChange = (index: number, value: string) => {
    // Разрешаем только цифры
    if (!/^\d*$/.test(value)) return;

    const newCode = [...code];
    newCode[index] = value.slice(-1); // Берем только последний символ
    setCode(newCode);

    // Автоматический переход к следующему полю
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Автоматическая отправка при заполнении всех полей
    if (newCode.every(digit => digit) && newCode.join('').length === 6) {
      verifyCode(newCode.join(''));
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const digits = pastedText.replace(/\D/g, '').slice(0, 6);
    
    if (digits.length === 6) {
      const newCode = digits.split('');
      setCode(newCode);
      verifyCode(digits);
    }
  };

  const verifyCode = async (verificationCode?: string) => {
    const codeToVerify = verificationCode || code.join('');
    
    if (codeToVerify.length !== 6) {
      setMessage('Введите полный 6-значный код');
      return;
    }

    try {
      setStatus('loading');
      const response = await axios.post('/api/auth/verify-phone', { 
        phone, 
        code: codeToVerify 
      });
      
      if (response.data.success) {
        setStatus('success');
        setMessage('Телефон успешно подтвержден!');
        
        // Автоматический переход через 2 секунды
        setTimeout(() => {
          router.push('/profile');
        }, 2000);
      }
    } catch (error: any) {
      setStatus('error');
      setMessage(error.response?.data?.message || 'Неверный код подтверждения');
      
      // Очищаем поля при ошибке
      setCode(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    }
  };

  const resendCode = async () => {
    if (!canResend || !phone) return;
    
    try {
      setResending(true);
      await axios.post('/api/auth/resend-phone-code', { phone });
      
      setMessage('Новый код отправлен на ваш телефон');
      setCountdown(60);
      setCanResend(false);
      
      // Запускаем новый таймер
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
    } catch (error: any) {
      setMessage(error.response?.data?.message || 'Ошибка отправки кода');
    } finally {
      setResending(false);
    }
  };

  const formatPhone = (phoneNumber: string) => {
    if (!phoneNumber) return '';
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.startsWith('7')) {
      return `+7 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7, 9)}-${cleaned.slice(9, 11)}`;
    }
    return phoneNumber;
  };

  if (status === 'success') {
    return (
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Stack spacing={3} alignItems="center">
                <CheckCircle color="success" sx={{ fontSize: 80 }} />
                <Typography variant="h5" color="success.main">
                  Телефон подтвержден!
                </Typography>
                <Typography variant="body1" textAlign="center">
                  Ваш номер телефона успешно подтвержден. Вы будете перенаправлены в профиль.
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => router.push('/profile')}
                >
                  Перейти в профиль
                </Button>
              </Stack>
            </Paper>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Подтверждение телефона - Likes & Love</title>
        <meta
          name="description"
          content="Подтверждение номера телефона для активации аккаунта в приложении знакомств Likes & Love"
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/auth/verify-phone" />
      </Head>

      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Typography variant="h4" align="center" gutterBottom>
                Подтверждение телефона
              </Typography>
            
            <Stack spacing={3} alignItems="center">
              <Sms color="primary" sx={{ fontSize: 60 }} />
              
              <Typography variant="body1" textAlign="center">
                Мы отправили SMS с кодом подтверждения на номер:
              </Typography>
              
              <Typography variant="h6" color="primary">
                {formatPhone(phone as string)}
              </Typography>

              {message && (
                <Alert 
                  severity={status === 'error' ? 'error' : 'info'} 
                  sx={{ width: '100%' }}
                >
                  {message}
                </Alert>
              )}

              <Box>
                <Typography variant="body2" textAlign="center" gutterBottom>
                  Введите 6-значный код:
                </Typography>
                
                <Stack direction="row" spacing={1} justifyContent="center">
                  {code.map((digit, index) => (
                    <TextField
                      key={index}
                      inputRef={(el) => (inputRefs.current[index] = el)}
                      value={digit}
                      onChange={(e) => handleCodeChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      onPaste={index === 0 ? handlePaste : undefined}
                      variant="outlined"
                      size="small"
                      inputProps={{
                        maxLength: 1,
                        style: { 
                          textAlign: 'center', 
                          fontSize: '1.5rem',
                          width: '40px',
                          height: '40px'
                        }
                      }}
                      disabled={status === 'loading'}
                    />
                  ))}
                </Stack>
              </Box>

              {status === 'loading' && (
                <CircularProgress size={24} />
              )}

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Не получили код?
                </Typography>
                
                {canResend ? (
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={resendCode}
                    disabled={resending}
                    size="small"
                  >
                    {resending ? 'Отправка...' : 'Отправить повторно'}
                  </Button>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Повторная отправка через {countdown} сек
                  </Typography>
                )}
              </Box>

              <Button
                variant="text"
                onClick={() => router.push('/auth/login')}
                size="small"
              >
                Вернуться к входу
              </Button>
            </Stack>
          </Paper>
        </Box>
      </Container>
    </Layout>
    </>
  );
};

export default VerifyPhonePage;
