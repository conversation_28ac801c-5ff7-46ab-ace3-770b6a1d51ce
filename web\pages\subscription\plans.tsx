import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import {
  ArrowBack,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  LocalOffer as OfferIcon,
  Payment as PaymentIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  Verified as VerifiedIcon,
  Diamond as DiamondIcon,
  Favorite as FavoriteIcon,
  Visibility as VisibilityIcon,
  Chat as ChatIcon,
  VideoCall as VideoCallIcon,
  Support as SupportIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSubscription } from '../../src/contexts/SubscriptionContext';
import { formatCurrency, calculateDiscount } from '../../src/services/subscriptionService';
import { SubscriptionPlan } from '../../src/types/subscription.types';

const SubscriptionPlansPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const {
    availablePlans,
    currentSubscription,
    loading,
    error,
    loadAvailablePlans,
    createSubscription,
    validatePromoCode
  } = useSubscription();

  const [isYearly, setIsYearly] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [promoCode, setPromoCode] = useState('');
  const [promoDiscount, setPromoDiscount] = useState<any>(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadAvailablePlans();
  }, [user, router]);

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowPaymentDialog(true);
  };

  const handleValidatePromoCode = async () => {
    if (!selectedPlan || !promoCode.trim()) return;

    try {
      const result = await validatePromoCode({
        code: promoCode.trim(),
        planId: selectedPlan.id,
        billingCycle: isYearly ? 'yearly' : 'monthly'
      });

      if (result.isValid && result.discount) {
        setPromoDiscount(result.discount);
      } else {
        setPromoDiscount(null);
        // Show error message
      }
    } catch (err: any) {
      setPromoDiscount(null);
    }
  };

  const handleCreateSubscription = async () => {
    if (!selectedPlan) return;

    try {
      setActionLoading(true);
      
      // For demo purposes, we'll assume user has a default payment method
      // In real implementation, this would redirect to payment method selection
      await createSubscription({
        planId: selectedPlan.id,
        billingCycle: isYearly ? 'yearly' : 'monthly',
        paymentMethodId: 'default', // This would be selected by user
        promoCode: promoCode.trim() || undefined,
        autoRenew: true
      });

      setSuccess('Подписка успешно оформлена!');
      setShowPaymentDialog(false);
      
      // Redirect to subscription page after short delay
      setTimeout(() => {
        router.push('/subscription');
      }, 2000);
      
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(false);
    }
  };

  const getPlanPrice = (plan: SubscriptionPlan) => {
    return isYearly ? plan.pricing.yearly : plan.pricing.monthly;
  };

  const getPlanDiscount = (plan: SubscriptionPlan) => {
    if (!isYearly || !plan.pricing.yearly.originalPrice) return null;
    return calculateDiscount(plan.pricing.yearly.originalPrice, plan.pricing.yearly.price);
  };

  const getFeatureIcon = (category: string) => {
    switch (category) {
      case 'communication':
        return <ChatIcon fontSize="small" />;
      case 'discovery':
        return <VisibilityIcon fontSize="small" />;
      case 'profile':
        return <VerifiedIcon fontSize="small" />;
      case 'premium':
        return <DiamondIcon fontSize="small" />;
      case 'analytics':
        return <TrendingUpIcon fontSize="small" />;
      case 'support':
        return <SupportIcon fontSize="small" />;
      default:
        return <CheckIcon fontSize="small" />;
    }
  };

  const isCurrentPlan = (plan: SubscriptionPlan) => {
    return currentSubscription?.plan.id === plan.id;
  };

  const canUpgrade = (plan: SubscriptionPlan) => {
    if (!currentSubscription) return true;
    return plan.order > currentSubscription.plan.order;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Тарифные планы - Likes & Love</title>
        <meta 
          name="description" 
          content="Выберите подходящий тарифный план в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/subscription/plans" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <StarIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Тарифные планы
              </Typography>
            </Box>

            {/* Billing Toggle */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isYearly}
                    onChange={(e) => setIsYearly(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body1">
                      Годовая подписка
                    </Typography>
                    <Chip
                      label="Скидка до 40%"
                      color="success"
                      size="small"
                      variant="filled"
                    />
                  </Box>
                }
                sx={{ mb: 2 }}
              />
              <Typography variant="body2" color="text.secondary">
                {isYearly ? 'Экономьте с годовой подпиской' : 'Гибкая месячная оплата'}
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка тарифных планов...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3} justifyContent="center">
                  {availablePlans
                    .filter(plan => plan.status === 'active')
                    .sort((a, b) => a.order - b.order)
                    .map((plan, index) => {
                      const pricing = getPlanPrice(plan);
                      const discount = getPlanDiscount(plan);
                      const isCurrent = isCurrentPlan(plan);
                      const canUpgradeToPlan = canUpgrade(plan);

                      return (
                        <Grid item xs={12} sm={6} md={4} key={plan.id}>
                          <Zoom in timeout={600 + index * 100}>
                            <Card
                              elevation={plan.isPopular ? 8 : 2}
                              sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                position: 'relative',
                                border: plan.isPopular ? `2px solid ${theme.palette.primary.main}` : 'none',
                                transform: plan.isPopular ? 'scale(1.05)' : 'none',
                                '&:hover': {
                                  transform: plan.isPopular ? 'scale(1.05)' : 'scale(1.02)',
                                  boxShadow: theme.shadows[8]
                                },
                                transition: 'all 0.3s ease-in-out'
                              }}
                            >
                              {/* Popular Badge */}
                              {plan.isPopular && (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: -10,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    zIndex: 1
                                  }}
                                >
                                  <Chip
                                    label="Популярный"
                                    color="primary"
                                    size="small"
                                    icon={<StarIcon />}
                                  />
                                </Box>
                              )}

                              {/* Recommended Badge */}
                              {plan.isRecommended && (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: 16,
                                    right: 16,
                                    zIndex: 1
                                  }}
                                >
                                  <Chip
                                    label="Рекомендуем"
                                    color="success"
                                    size="small"
                                    variant="outlined"
                                  />
                                </Box>
                              )}

                              <CardContent sx={{ flexGrow: 1, pt: plan.isPopular ? 4 : 2 }}>
                                {/* Plan Header */}
                                <Box sx={{ textAlign: 'center', mb: 3 }}>
                                  <Typography variant="h5" gutterBottom>
                                    {plan.displayName}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {plan.description}
                                  </Typography>

                                  {/* Pricing */}
                                  <Box sx={{ mb: 2 }}>
                                    {pricing.originalPrice && pricing.originalPrice > pricing.price && (
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          textDecoration: 'line-through',
                                          color: 'text.secondary'
                                        }}
                                      >
                                        {formatCurrency(pricing.originalPrice)}
                                      </Typography>
                                    )}
                                    <Typography variant="h4" color="primary" gutterBottom>
                                      {formatCurrency(pricing.price)}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      {isYearly ? 'в год' : 'в месяц'}
                                    </Typography>
                                    {isYearly && plan.pricing.yearly.monthlyEquivalent && (
                                      <Typography variant="caption" color="text.secondary">
                                        {formatCurrency(plan.pricing.yearly.monthlyEquivalent)} в месяц
                                      </Typography>
                                    )}
                                  </Box>

                                  {/* Discount Badge */}
                                  {discount && (
                                    <Chip
                                      label={`Скидка ${discount}%`}
                                      color="success"
                                      size="small"
                                      icon={<OfferIcon />}
                                    />
                                  )}
                                </Box>

                                {/* Features List */}
                                <List dense sx={{ mb: 2 }}>
                                  {plan.features
                                    .filter(feature => feature.isHighlight)
                                    .slice(0, 8)
                                    .map((feature, featureIndex) => (
                                      <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                                        <ListItemIcon sx={{ minWidth: 32 }}>
                                          {getFeatureIcon(feature.category)}
                                        </ListItemIcon>
                                        <ListItemText
                                          primary={feature.name}
                                          primaryTypographyProps={{
                                            variant: 'body2',
                                            fontSize: '0.875rem'
                                          }}
                                        />
                                      </ListItem>
                                    ))}
                                  
                                  {plan.features.filter(f => f.isHighlight).length > 8 && (
                                    <ListItem sx={{ px: 0, py: 0.5 }}>
                                      <ListItemText
                                        primary={`И еще ${plan.features.filter(f => f.isHighlight).length - 8} возможностей...`}
                                        primaryTypographyProps={{
                                          variant: 'body2',
                                          fontStyle: 'italic',
                                          color: 'text.secondary'
                                        }}
                                      />
                                    </ListItem>
                                  )}
                                </List>

                                {/* Plan Badge */}
                                {plan.badge && (
                                  <Box sx={{ textAlign: 'center', mb: 2 }}>
                                    <Chip
                                      label={plan.badge.text}
                                      color={plan.badge.color}
                                      size="small"
                                      variant="outlined"
                                    />
                                  </Box>
                                )}
                              </CardContent>

                              <CardActions sx={{ p: 2, pt: 0 }}>
                                {isCurrent ? (
                                  <Button
                                    fullWidth
                                    variant="outlined"
                                    disabled
                                    startIcon={<CheckIcon />}
                                  >
                                    Текущий план
                                  </Button>
                                ) : canUpgradeToPlan ? (
                                  <Button
                                    fullWidth
                                    variant={plan.isPopular ? "contained" : "outlined"}
                                    color="primary"
                                    size="large"
                                    onClick={() => handleSelectPlan(plan)}
                                    startIcon={<PaymentIcon />}
                                  >
                                    {currentSubscription ? 'Перейти' : 'Выбрать'}
                                  </Button>
                                ) : (
                                  <Button
                                    fullWidth
                                    variant="outlined"
                                    disabled
                                  >
                                    Недоступно
                                  </Button>
                                )}
                              </CardActions>
                            </Card>
                          </Zoom>
                        </Grid>
                      );
                    })}
                </Grid>
              </Fade>
            )}

            {/* Additional Information */}
            <Box sx={{ mt: 6, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Все планы включают
              </Typography>
              <Grid container spacing={2} justifyContent="center" sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <SecurityIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="body2" textAlign="center">
                      Безопасность данных
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <SupportIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="body2" textAlign="center">
                      Поддержка 24/7
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <VerifiedIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="body2" textAlign="center">
                      Проверенные профили
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <FavoriteIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="body2" textAlign="center">
                      Гарантия качества
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Payment Dialog */}
            <Dialog
              open={showPaymentDialog}
              onClose={() => setShowPaymentDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Оформление подписки
              </DialogTitle>
              <DialogContent>
                {selectedPlan && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      {selectedPlan.displayName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {selectedPlan.description}
                    </Typography>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1">
                        Стоимость ({isYearly ? 'год' : 'месяц'}):
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {formatCurrency(getPlanPrice(selectedPlan).price)}
                      </Typography>
                    </Box>

                    {/* Promo Code */}
                    <Box sx={{ mb: 2 }}>
                      <TextField
                        fullWidth
                        label="Промокод (необязательно)"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        onBlur={handleValidatePromoCode}
                        size="small"
                      />
                      {promoDiscount && (
                        <Alert severity="success" sx={{ mt: 1 }}>
                          Скидка {promoDiscount.type === 'percentage' ? `${promoDiscount.value}%` : formatCurrency(promoDiscount.value)}
                        </Alert>
                      )}
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6">
                        Итого:
                      </Typography>
                      <Typography variant="h6" color="primary">
                        {formatCurrency(
                          promoDiscount 
                            ? getPlanPrice(selectedPlan).price - promoDiscount.amount
                            : getPlanPrice(selectedPlan).price
                        )}
                      </Typography>
                    </Box>

                    <Typography variant="caption" color="text.secondary">
                      Нажимая "Оформить подписку", вы соглашаетесь с условиями использования и политикой конфиденциальности.
                    </Typography>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowPaymentDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleCreateSubscription}
                  variant="contained"
                  disabled={actionLoading}
                  startIcon={actionLoading ? <CircularProgress size={20} /> : <PaymentIcon />}
                >
                  Оформить подписку
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SubscriptionPlansPage;
