import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Fade,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Tooltip
} from '@mui/material';
import {
  ArrowBack,
  Block as BlockIcon,
  Person as PersonIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Unblock as UnblockIcon,
  Report as ReportIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSettings } from '../../src/contexts/SettingsContext';

interface BlockedUser {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  blockedAt: string;
  reason: string;
  mutualFriends?: number;
}

const BlockedUsersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    unblockUser,
    loadSettings 
  } = useSettings();

  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<BlockedUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'date'>('date');
  const [selectedUser, setSelectedUser] = useState<BlockedUser | null>(null);
  const [showUnblockDialog, setShowUnblockDialog] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuUserId, setMenuUserId] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    // Load blocked users from settings
    if (settings?.security.blockedUsers) {
      // In real app, this would fetch full user data
      const mockBlockedUsers: BlockedUser[] = settings.security.blockedUsers.map((userId, index) => ({
        id: userId,
        firstName: `Пользователь`,
        lastName: `${index + 1}`,
        avatar: undefined,
        blockedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        reason: ['Спам', 'Неподобающее поведение', 'Фейковый профиль'][Math.floor(Math.random() * 3)],
        mutualFriends: Math.floor(Math.random() * 10)
      }));
      setBlockedUsers(mockBlockedUsers);
    }
  }, [user, router, settings]);

  useEffect(() => {
    let filtered = [...blockedUsers];
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(user => 
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      if (sortBy === 'name') {
        return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
      } else {
        return new Date(b.blockedAt).getTime() - new Date(a.blockedAt).getTime();
      }
    });
    
    setFilteredUsers(filtered);
  }, [blockedUsers, searchQuery, sortBy]);

  const handleUnblock = async (userId: string) => {
    try {
      await unblockUser(userId);
      setSuccess('Пользователь разблокирован');
      setShowUnblockDialog(false);
      setSelectedUser(null);
      loadSettings();
    } catch (err: any) {
      // Error handled by context
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, userId: string) => {
    setAnchorEl(event.currentTarget);
    setMenuUserId(userId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuUserId(null);
  };

  const handleUnblockClick = (user: BlockedUser) => {
    setSelectedUser(user);
    setShowUnblockDialog(true);
    handleMenuClose();
  };

  const formatBlockedDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Сегодня';
    } else if (diffDays === 1) {
      return 'Вчера';
    } else if (diffDays < 7) {
      return `${diffDays} дней назад`;
    } else {
      return date.toLocaleDateString('ru-RU');
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Заблокированные пользователи - Likes & Love</title>
        <meta 
          name="description" 
          content="Управление заблокированными пользователями в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <BlockIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Заблокированные пользователи
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadSettings}
                disabled={loading}
              >
                Обновить
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка заблокированных пользователей...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* Search and Filters */}
                  <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
                    <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
                      <TextField
                        fullWidth
                        placeholder="Поиск по имени..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <TextField
                        select
                        label="Сортировка"
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as 'name' | 'date')}
                        sx={{ minWidth: 150 }}
                      >
                        <MenuItem value="date">По дате</MenuItem>
                        <MenuItem value="name">По имени</MenuItem>
                      </TextField>
                    </Box>
                    
                    <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Найдено: {filteredUsers.length} из {blockedUsers.length}
                      </Typography>
                      
                      {searchQuery && (
                        <Chip
                          label={`Поиск: "${searchQuery}"`}
                          onDelete={() => setSearchQuery('')}
                          size="small"
                        />
                      )}
                    </Box>
                  </Paper>

                  {filteredUsers.length === 0 ? (
                    <Card sx={{ textAlign: 'center', py: 8 }}>
                      <CardContent>
                        <BlockIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          {blockedUsers.length === 0 ? 'Нет заблокированных пользователей' : 'Пользователи не найдены'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                          {blockedUsers.length === 0 
                            ? 'Здесь будут отображаться пользователи, которых вы заблокировали'
                            : 'Попробуйте изменить параметры поиска'
                          }
                        </Typography>
                        {searchQuery && (
                          <Button
                            variant="outlined"
                            onClick={() => setSearchQuery('')}
                          >
                            Очистить поиск
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    <Paper elevation={2}>
                      <List>
                        {filteredUsers.map((blockedUser, index) => (
                          <React.Fragment key={blockedUser.id}>
                            <ListItem sx={{ py: 2 }}>
                              <ListItemIcon>
                                <Avatar src={blockedUser.avatar}>
                                  {blockedUser.firstName[0]}{blockedUser.lastName[0]}
                                </Avatar>
                              </ListItemIcon>
                              
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Typography variant="body1">
                                      {blockedUser.firstName} {blockedUser.lastName}
                                    </Typography>
                                    <Chip
                                      label={blockedUser.reason}
                                      size="small"
                                      color="error"
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Box>
                                    <Typography variant="body2" color="text.secondary">
                                      Заблокирован: {formatBlockedDate(blockedUser.blockedAt)}
                                    </Typography>
                                    {blockedUser.mutualFriends && blockedUser.mutualFriends > 0 && (
                                      <Typography variant="caption" color="text.secondary">
                                        {blockedUser.mutualFriends} общих знакомых
                                      </Typography>
                                    )}
                                  </Box>
                                }
                              />
                              
                              <ListItemSecondaryAction>
                                <IconButton
                                  onClick={(e) => handleMenuOpen(e, blockedUser.id)}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < filteredUsers.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </Paper>
                  )}

                  {/* Info Card */}
                  <Card sx={{ mt: 4, backgroundColor: 'background.default' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        <InfoIcon color="primary" />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            О блокировке пользователей
                          </Typography>

                          <Typography variant="body2" color="text.secondary" paragraph>
                            Когда вы блокируете пользователя:
                          </Typography>

                          <Box component="ul" sx={{ pl: 2, m: 0 }}>
                            <Typography component="li" variant="body2" color="text.secondary">
                              Он не сможет найти ваш профиль в поиске
                            </Typography>
                            <Typography component="li" variant="body2" color="text.secondary">
                              Не сможет отправлять вам сообщения
                            </Typography>
                            <Typography component="li" variant="body2" color="text.secondary">
                              Не увидит ваш профиль в рекомендациях
                            </Typography>
                            <Typography component="li" variant="body2" color="text.secondary">
                              Существующие совпадения будут удалены
                            </Typography>
                          </Box>

                          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                            Вы можете разблокировать пользователя в любое время.
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Fade>
            )}

            {/* User Menu */}
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={() => {
                const user = filteredUsers.find(u => u.id === menuUserId);
                if (user) handleUnblockClick(user);
              }}>
                <UnblockIcon sx={{ mr: 1 }} fontSize="small" />
                Разблокировать
              </MenuItem>

              <MenuItem onClick={() => {
                handleMenuClose();
                router.push(`/report/user/${menuUserId}`);
              }}>
                <ReportIcon sx={{ mr: 1 }} fontSize="small" />
                Пожаловаться
              </MenuItem>
            </Menu>

            {/* Unblock Confirmation Dialog */}
            <Dialog
              open={showUnblockDialog}
              onClose={() => setShowUnblockDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Разблокировать пользователя?
              </DialogTitle>
              <DialogContent>
                {selectedUser && (
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Avatar src={selectedUser.avatar}>
                        {selectedUser.firstName[0]}{selectedUser.lastName[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="body1">
                          {selectedUser.firstName} {selectedUser.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Заблокирован: {formatBlockedDate(selectedUser.blockedAt)}
                        </Typography>
                      </Box>
                    </Box>

                    <Alert severity="info">
                      После разблокировки этот пользователь снова сможет:
                      <ul>
                        <li>Найти ваш профиль в поиске</li>
                        <li>Отправлять вам сообщения</li>
                        <li>Видеть ваш профиль в рекомендациях</li>
                      </ul>
                    </Alert>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowUnblockDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={() => selectedUser && handleUnblock(selectedUser.id)}
                  variant="contained"
                  color="primary"
                >
                  Разблокировать
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default BlockedUsersPage;
