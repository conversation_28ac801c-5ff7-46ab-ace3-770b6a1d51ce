import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  Badge,
  IconButton,
  Menu,
  MenuItem,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Star as StarIcon,
  MoreVert as MoreVertIcon,
  Message as MessageIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  MarkEmailRead as MarkReadIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import {
  getReceivedLikes,
  sendLike,
  markLikesAsRead,
  getLikesStats
} from '../../src/services/likesService';
import { Like, FilterOptions, LikesStats } from '../../src/types/likes.types';

const ReceivedLikesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [likes, setLikes] = useState<Like[]>([]);
  const [stats, setStats] = useState<LikesStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedLike, setSelectedLike] = useState<Like | null>(null);
  const [responding, setResponding] = useState<string | null>(null);

  // Pagination and filters
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'createdAt' | 'lastActiveAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [filterRead, setFilterRead] = useState<'all' | 'unread' | 'read'>('all');
  const [filterType, setFilterType] = useState<'all' | 'like' | 'super_like'>('all');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>(undefined);

  const limit = 20;
  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadLikes();
    loadStats();
  }, [user, router, page, sortBy, sortOrder, filterRead, filterType, filterVerified]);

  const loadLikes = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy,
        sortOrder,
        verifiedOnly: filterVerified
      };

      if (filterRead !== 'all') {
        filters.isRead = filterRead === 'read';
      }

      if (filterType !== 'all') {
        filters.type = filterType as any;
      }

      const response = await getReceivedLikes(filters);
      setLikes(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки лайков');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getLikesStats();
      setStats(statsData);
    } catch (err: any) {
      // Не показываем ошибку для статистики
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, like: Like) => {
    setAnchorEl(event.currentTarget);
    setSelectedLike(like);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedLike(null);
  };

  const handleLikeBack = async (like: Like) => {
    try {
      setResponding(like.id);
      setError(null);

      const result = await sendLike({
        targetUserId: like.fromUserId,
        type: 'like'
      });

      if (result.success) {
        if (result.isMatch) {
          setSuccess(`Это совпадение! Вы можете начать общение с ${like.fromUser?.firstName}`);
          // Обновляем лайк как взаимный
          setLikes(prev => prev.map(l =>
            l.id === like.id ? { ...l, isMutual: true } : l
          ));
        } else {
          setSuccess('Лайк отправлен');
        }

        // Отмечаем как прочитанный
        await markLikesAsRead([like.id]);
        setLikes(prev => prev.map(l =>
          l.id === like.id ? { ...l, isRead: true } : l
        ));
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка отправки лайка');
    } finally {
      setResponding(null);
    }
  };

  const handleSuperLikeBack = async (like: Like) => {
    try {
      setResponding(like.id);
      setError(null);

      const result = await sendLike({
        targetUserId: like.fromUserId,
        type: 'super_like'
      });

      if (result.success) {
        setSuccess(result.isMatch ? 'Это совпадение!' : 'Супер-лайк отправлен');
        if (result.isMatch) {
          setLikes(prev => prev.map(l =>
            l.id === like.id ? { ...l, isMutual: true } : l
          ));
        }

        await markLikesAsRead([like.id]);
        setLikes(prev => prev.map(l =>
          l.id === like.id ? { ...l, isRead: true } : l
        ));
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка отправки супер-лайка');
    } finally {
      setResponding(null);
    }
  };

  const handlePass = async (like: Like) => {
    try {
      setResponding(like.id);
      await sendLike({
        targetUserId: like.fromUserId,
        type: 'pass'
      });

      // Удаляем из списка
      setLikes(prev => prev.filter(l => l.id !== like.id));
    } catch (err: any) {
      setError('Ошибка отклонения');
    } finally {
      setResponding(null);
    }
  };
  const handleViewProfile = (userId: string) => {
    router.push(`/users/${userId}`);
    handleMenuClose();
  };

  const handleSendMessage = (userId: string) => {
    router.push(`/chat?userId=${userId}`);
    handleMenuClose();
  };

  const handleMarkAsRead = async (likeIds: string[]) => {
    try {
      await markLikesAsRead(likeIds);
      setLikes(prev => prev.map(like =>
        likeIds.includes(like.id) ? { ...like, isRead: true } : like
      ));
    } catch (err: any) {
      setError('Ошибка отметки как прочитанное');
    }
  };

  const formatTime = (createdAt: string) => {
    const now = new Date();
    const likeTime = new Date(createdAt);
    const diffInHours = Math.floor((now.getTime() - likeTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const unreadCount = likes.filter(l => !l.isRead).length;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Полученные лайки - Likes & Love</title>
        <meta
          name="description"
          content="Посмотрите, кто поставил вам лайк в приложении знакомств Likes & Love"
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/likes" />
      </Head>

      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <Badge badgeContent={unreadCount} color="primary">
                      <FavoriteIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'error.main' }} />
                    </Badge>
                    Полученные лайки
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stats ? `${stats.totalReceived} всего, ${stats.todayReceived} сегодня` : 'Загрузка статистики...'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes/sent')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Отправленные
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes/mutual')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Взаимные
                  </Button>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {/* Stats cards */}
              {stats && (
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="error.main">
                          {stats.totalReceived}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Всего лайков
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="warning.main">
                          {stats.superLikesReceived}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Супер-лайков
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="success.main">
                          {stats.totalMutual}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Взаимных
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="primary.main">
                          {Math.round(stats.matchRate)}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Совпадений
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              )}

              {/* Filters */}
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Сортировка</InputLabel>
                      <Select
                        value={`${sortBy}-${sortOrder}`}
                        label="Сортировка"
                        onChange={(e) => {
                          const [newSortBy, newSortOrder] = e.target.value.split('-');
                          setSortBy(newSortBy as any);
                          setSortOrder(newSortOrder as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="createdAt-desc">Сначала новые</MenuItem>
                        <MenuItem value="createdAt-asc">Сначала старые</MenuItem>
                        <MenuItem value="lastActiveAt-desc">По активности</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Тип</InputLabel>
                      <Select
                        value={filterType}
                        label="Тип"
                        onChange={(e) => {
                          setFilterType(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="like">Лайки</MenuItem>
                        <MenuItem value="super_like">Супер-лайки</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Статус</InputLabel>
                      <Select
                        value={filterRead}
                        label="Статус"
                        onChange={(e) => {
                          setFilterRead(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="unread">Новые</MenuItem>
                        <MenuItem value="read">Просмотренные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Верификация</InputLabel>
                      <Select
                        value={filterVerified === undefined ? 'all' : filterVerified ? 'verified' : 'unverified'}
                        label="Верификация"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterVerified(
                            value === 'all' ? undefined : value === 'verified'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="verified">Верифицированные</MenuItem>
                        <MenuItem value="unverified">Неверифицированные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка лайков...
                  </Typography>
                </Box>
              ) : likes.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <FavoriteBorderIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Пока никто не поставил вам лайк
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Улучшите свой профиль, чтобы получать больше лайков
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/profile/edit')}
                  >
                    Улучшить профиль
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Likes grid */}
                    <Grid container spacing={2}>
                      {likes.map((like) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={like.id}>
                          <Card
                            sx={{
                              position: 'relative',
                              border: !like.isRead ? `2px solid ${theme.palette.primary.main}` : 'none',
                              '&:hover': {
                                boxShadow: theme.shadows[4]
                              }
                            }}
                          >
                            {!like.isRead && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 8,
                                  left: 8,
                                  width: 12,
                                  height: 12,
                                  borderRadius: '50%',
                                  backgroundColor: theme.palette.primary.main,
                                  zIndex: 1
                                }}
                              />
                            )}

                            <CardContent sx={{ pb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Badge
                                  overlap="circular"
                                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                  badgeContent={
                                    like.fromUser?.isOnline ? (
                                      <Box
                                        sx={{
                                          width: 12,
                                          height: 12,
                                          borderRadius: '50%',
                                          backgroundColor: 'success.main',
                                          border: '2px solid white'
                                        }}
                                      />
                                    ) : null
                                  }
                                >
                                  <Avatar
                                    src={like.fromUser?.avatarUrl}
                                    sx={{ width: 56, height: 56 }}
                                  >
                                    {like.fromUser?.firstName[0]}
                                  </Avatar>
                                </Badge>
                                <Box sx={{ ml: 2, flexGrow: 1 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Typography variant="subtitle2" noWrap>
                                      {like.fromUser?.firstName}
                                    </Typography>
                                    {like.fromUser?.verificationStatus.phone && (
                                      <VerifiedIcon
                                        sx={{
                                          ml: 0.5,
                                          fontSize: 16,
                                          color: 'primary.main'
                                        }}
                                      />
                                    )}
                                  </Box>
                                  <Typography variant="caption" color="text.secondary">
                                    {like.fromUser?.age} лет
                                  </Typography>
                                </Box>
                                <IconButton
                                  size="small"
                                  onClick={(e) => handleMenuOpen(e, like)}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </Box>

                              <Box sx={{ mb: 1 }}>
                                <Chip
                                  icon={like.type === 'super_like' ? <StarIcon /> : <FavoriteIcon />}
                                  label={like.type === 'super_like' ? 'Супер-лайк' : 'Лайк'}
                                  size="small"
                                  color={like.type === 'super_like' ? 'warning' : 'error'}
                                  variant="outlined"
                                  sx={{ mr: 1 }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {formatTime(like.createdAt)}
                                </Typography>
                              </Box>

                              {like.message && (
                                <Typography variant="body2" sx={{ mb: 1, fontStyle: 'italic' }}>
                                  "{like.message}"
                                </Typography>
                              )}

                              {like.isMutual && (
                                <Chip
                                  label="Взаимный лайк"
                                  size="small"
                                  color="success"
                                  sx={{ mb: 1 }}
                                />
                              )}
                            </CardContent>

                            <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                              {!like.isMutual ? (
                                <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
                                  <Button
                                    size="small"
                                    variant="outlined"
                                    onClick={() => handlePass(like)}
                                    disabled={responding === like.id}
                                    sx={{ flex: 1 }}
                                  >
                                    Пропустить
                                  </Button>
                                  <Button
                                    size="small"
                                    variant="contained"
                                    color="error"
                                    onClick={() => handleLikeBack(like)}
                                    disabled={responding === like.id}
                                    startIcon={responding === like.id ? <CircularProgress size={16} /> : <FavoriteIcon />}
                                    sx={{ flex: 1 }}
                                  >
                                    Лайк
                                  </Button>
                                </Box>
                              ) : (
                                <Button
                                  size="small"
                                  variant="contained"
                                  onClick={() => handleSendMessage(like.fromUserId)}
                                  startIcon={<MessageIcon />}
                                  fullWidth
                                >
                                  Написать
                                </Button>
                              )}
                            </CardActions>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Context menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedLike && handleViewProfile(selectedLike.fromUserId)}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Посмотреть профиль
          </MenuItem>
          {selectedLike?.isMutual && (
            <MenuItem onClick={() => selectedLike && handleSendMessage(selectedLike.fromUserId)}>
              <MessageIcon sx={{ mr: 1 }} />
              Написать сообщение
            </MenuItem>
          )}
          {!selectedLike?.isRead && (
            <MenuItem
              onClick={() => selectedLike && handleMarkAsRead([selectedLike.id])}
            >
              <MarkReadIcon sx={{ mr: 1 }} />
              Отметить как прочитанное
            </MenuItem>
          )}
          <MenuItem onClick={() => selectedLike && handlePass(selectedLike)}>
            <BlockIcon sx={{ mr: 1 }} />
            Скрыть
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default ReceivedLikesPage;
