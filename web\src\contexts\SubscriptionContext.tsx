import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import {
  SubscriptionPlan,
  UserSubscription,
  PaymentMethod,
  PaymentTransaction,
  SubscriptionUsage,
  SubscriptionContextType,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  CancelSubscriptionRequest,
  CreatePaymentMethodRequest,
  ProcessPaymentRequest,
  ValidatePromoCodeRequest,
  ValidatePromoCodeResponse
} from '../types/subscription.types';
import * as subscriptionService from '../services/subscriptionService';

// State interface
interface SubscriptionState {
  currentSubscription: UserSubscription | null;
  availablePlans: SubscriptionPlan[];
  paymentMethods: PaymentMethod[];
  transactionHistory: PaymentTransaction[];
  usage: SubscriptionUsage | null;
  loading: boolean;
  error: string | null;
}

// Action types
type SubscriptionAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURRENT_SUBSCRIPTION'; payload: UserSubscription | null }
  | { type: 'SET_AVAILABLE_PLANS'; payload: SubscriptionPlan[] }
  | { type: 'SET_PAYMENT_METHODS'; payload: PaymentMethod[] }
  | { type: 'SET_TRANSACTION_HISTORY'; payload: PaymentTransaction[] }
  | { type: 'SET_USAGE'; payload: SubscriptionUsage | null }
  | { type: 'ADD_PAYMENT_METHOD'; payload: PaymentMethod }
  | { type: 'UPDATE_PAYMENT_METHOD'; payload: { id: string; updates: Partial<PaymentMethod> } }
  | { type: 'REMOVE_PAYMENT_METHOD'; payload: string }
  | { type: 'ADD_TRANSACTION'; payload: PaymentTransaction }
  | { type: 'UPDATE_USAGE'; payload: Partial<SubscriptionUsage> };

// Initial state
const initialState: SubscriptionState = {
  currentSubscription: null,
  availablePlans: [],
  paymentMethods: [],
  transactionHistory: [],
  usage: null,
  loading: false,
  error: null,
};

// Reducer
const subscriptionReducer = (state: SubscriptionState, action: SubscriptionAction): SubscriptionState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_CURRENT_SUBSCRIPTION':
      return { ...state, currentSubscription: action.payload };
    
    case 'SET_AVAILABLE_PLANS':
      return { ...state, availablePlans: action.payload };
    
    case 'SET_PAYMENT_METHODS':
      return { ...state, paymentMethods: action.payload };
    
    case 'SET_TRANSACTION_HISTORY':
      return { ...state, transactionHistory: action.payload };
    
    case 'SET_USAGE':
      return { ...state, usage: action.payload };
    
    case 'ADD_PAYMENT_METHOD':
      return { 
        ...state, 
        paymentMethods: [...state.paymentMethods, action.payload] 
      };
    
    case 'UPDATE_PAYMENT_METHOD':
      return {
        ...state,
        paymentMethods: state.paymentMethods.map(method =>
          method.id === action.payload.id
            ? { ...method, ...action.payload.updates }
            : method
        )
      };
    
    case 'REMOVE_PAYMENT_METHOD':
      return {
        ...state,
        paymentMethods: state.paymentMethods.filter(method => method.id !== action.payload)
      };
    
    case 'ADD_TRANSACTION':
      return {
        ...state,
        transactionHistory: [action.payload, ...state.transactionHistory]
      };
    
    case 'UPDATE_USAGE':
      return {
        ...state,
        usage: state.usage ? { ...state.usage, ...action.payload } : null
      };
    
    default:
      return state;
  }
};

// Create context
const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

// Provider component
interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(subscriptionReducer, initialState);

  // Helper function to handle async operations
  const handleAsync = async <T,>(
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    showLoading: boolean = true
  ): Promise<T | null> => {
    try {
      if (showLoading) {
        dispatch({ type: 'SET_LOADING', payload: true });
      }
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const result = await operation();
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return null;
    } finally {
      if (showLoading) {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  };

  // Load current subscription
  const loadCurrentSubscription = async (): Promise<void> => {
    await handleAsync(
      () => subscriptionService.getCurrentSubscription(),
      (subscription) => dispatch({ type: 'SET_CURRENT_SUBSCRIPTION', payload: subscription })
    );
  };

  // Load available plans
  const loadAvailablePlans = async (): Promise<void> => {
    await handleAsync(
      () => subscriptionService.getAvailablePlans(),
      (plans) => dispatch({ type: 'SET_AVAILABLE_PLANS', payload: plans })
    );
  };

  // Load payment methods
  const loadPaymentMethods = async (): Promise<void> => {
    await handleAsync(
      () => subscriptionService.getPaymentMethods(),
      (methods) => dispatch({ type: 'SET_PAYMENT_METHODS', payload: methods })
    );
  };

  // Load transaction history
  const loadTransactionHistory = async (): Promise<void> => {
    await handleAsync(
      () => subscriptionService.getTransactionHistory(),
      (response) => dispatch({ type: 'SET_TRANSACTION_HISTORY', payload: response.data })
    );
  };

  // Load usage
  const loadUsage = async (): Promise<void> => {
    await handleAsync(
      () => subscriptionService.getSubscriptionUsage(),
      (usage) => dispatch({ type: 'SET_USAGE', payload: usage })
    );
  };

  // Create subscription
  const createSubscription = async (request: CreateSubscriptionRequest): Promise<UserSubscription> => {
    const result = await handleAsync(
      () => subscriptionService.createSubscription(request),
      (subscription) => {
        dispatch({ type: 'SET_CURRENT_SUBSCRIPTION', payload: subscription });
        // Reload usage after creating subscription
        loadUsage();
      }
    );
    return result!;
  };

  // Update subscription
  const updateSubscription = async (request: UpdateSubscriptionRequest): Promise<UserSubscription> => {
    const result = await handleAsync(
      () => subscriptionService.updateSubscription(request),
      (subscription) => dispatch({ type: 'SET_CURRENT_SUBSCRIPTION', payload: subscription })
    );
    return result!;
  };

  // Cancel subscription
  const cancelSubscription = async (request: CancelSubscriptionRequest): Promise<void> => {
    await handleAsync(
      () => subscriptionService.cancelSubscription(request),
      () => {
        // Reload current subscription to get updated status
        loadCurrentSubscription();
      }
    );
  };

  // Reactivate subscription
  const reactivateSubscription = async (): Promise<UserSubscription> => {
    const result = await handleAsync(
      () => subscriptionService.reactivateSubscription(),
      (subscription) => dispatch({ type: 'SET_CURRENT_SUBSCRIPTION', payload: subscription })
    );
    return result!;
  };

  // Add payment method
  const addPaymentMethod = async (request: CreatePaymentMethodRequest): Promise<PaymentMethod> => {
    const result = await handleAsync(
      () => subscriptionService.addPaymentMethod(request),
      (method) => dispatch({ type: 'ADD_PAYMENT_METHOD', payload: method })
    );
    return result!;
  };

  // Update payment method
  const updatePaymentMethod = async (id: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod> => {
    const result = await handleAsync(
      () => subscriptionService.updatePaymentMethod(id, updates),
      () => dispatch({ type: 'UPDATE_PAYMENT_METHOD', payload: { id, updates } })
    );
    return result!;
  };

  // Delete payment method
  const deletePaymentMethod = async (id: string): Promise<void> => {
    await handleAsync(
      () => subscriptionService.deletePaymentMethod(id),
      () => dispatch({ type: 'REMOVE_PAYMENT_METHOD', payload: id })
    );
  };

  // Set default payment method
  const setDefaultPaymentMethod = async (id: string): Promise<void> => {
    await handleAsync(
      () => subscriptionService.setDefaultPaymentMethod(id),
      () => {
        // Update all methods to set only this one as default
        const updates = state.paymentMethods.map(method => ({
          id: method.id,
          updates: { isDefault: method.id === id }
        }));
        
        updates.forEach(({ id: methodId, updates: methodUpdates }) => {
          dispatch({ type: 'UPDATE_PAYMENT_METHOD', payload: { id: methodId, updates: methodUpdates } });
        });
      }
    );
  };

  // Process payment
  const processPayment = async (request: ProcessPaymentRequest): Promise<PaymentTransaction> => {
    const result = await handleAsync(
      () => subscriptionService.processPayment(request),
      (transaction) => dispatch({ type: 'ADD_TRANSACTION', payload: transaction })
    );
    return result!;
  };

  // Validate promo code
  const validatePromoCode = async (request: ValidatePromoCodeRequest): Promise<ValidatePromoCodeResponse> => {
    const result = await handleAsync(
      () => subscriptionService.validatePromoCode(request),
      undefined,
      false // Don't show loading for promo code validation
    );
    return result!;
  };

  // Helper functions
  const getFeatureUsage = (feature: string) => {
    return subscriptionService.getFeatureUsage(state.usage, feature);
  };

  const hasFeature = (feature: string): boolean => {
    return subscriptionService.hasFeature(state.currentSubscription, feature);
  };

  const isFeatureUnlimited = (feature: string): boolean => {
    const limit = subscriptionService.getFeatureLimit(state.currentSubscription, feature);
    return limit === 'unlimited';
  };

  const getRemainingUsage = (feature: string): number | 'unlimited' => {
    const usage = getFeatureUsage(feature);
    if (usage.limit === 'unlimited') {
      return 'unlimited';
    }
    return Math.max(0, usage.limit - usage.used);
  };

  // Load initial data on mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([
        loadCurrentSubscription(),
        loadAvailablePlans(),
        loadPaymentMethods(),
        loadUsage()
      ]);
    };

    loadInitialData();
  }, []);

  const contextValue: SubscriptionContextType = {
    // State
    currentSubscription: state.currentSubscription,
    availablePlans: state.availablePlans,
    paymentMethods: state.paymentMethods,
    transactionHistory: state.transactionHistory,
    usage: state.usage,
    loading: state.loading,
    error: state.error,

    // Actions
    loadCurrentSubscription,
    loadAvailablePlans,
    loadPaymentMethods,
    loadTransactionHistory,
    loadUsage,
    createSubscription,
    updateSubscription,
    cancelSubscription,
    reactivateSubscription,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPaymentMethod,
    processPayment,
    validatePromoCode,
    getFeatureUsage,
    hasFeature,
    isFeatureUnlimited,
    getRemainingUsage,
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
};

// Hook to use subscription context
export const useSubscription = (): SubscriptionContextType => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

export default SubscriptionContext;
