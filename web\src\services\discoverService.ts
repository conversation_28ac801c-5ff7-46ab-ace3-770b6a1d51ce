import axios from 'axios';
import { config } from '../config';
import {
  DiscoverUser,
  DiscoverFilters,
  DiscoverSettings,
  SwipeAction,
  SwipeResult,
  UserRecommendation,
  PopularProfile,
  NearbyUser,
  OnlineUser,
  NewUser,
  DiscoverStats,
  BoostSession,
  SuperLike,
  SearchCriteria,
  SearchResult,
  TrendingTopic,
  CompatibilityTest,
  CompatibilityResult,
  PaginatedResponse
} from '../types/discover.types';

const discoverApi = axios.create({
  baseURL: `${config.api.baseUrl}/discover`,
  withCredentials: true,
});

// Main discover feed
export const getDiscoverFeed = async (
  filters?: DiscoverFilters,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<DiscoverUser>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await discoverApi.get(`/feed?${params.toString()}`);
  return data;
};

export const refreshDiscoverFeed = async (): Promise<DiscoverUser[]> => {
  const { data } = await discoverApi.post('/feed/refresh');
  return data.users;
};

// Recommendations
export const getRecommendations = async (limit: number = 10): Promise<UserRecommendation[]> => {
  const { data } = await discoverApi.get(`/recommendations?limit=${limit}`);
  return data.recommendations;
};

export const getTopPicks = async (): Promise<UserRecommendation[]> => {
  const { data } = await discoverApi.get('/recommendations/top-picks');
  return data.topPicks;
};

// Popular profiles
export const getPopularProfiles = async (
  timeframe: 'day' | 'week' | 'month' = 'week',
  limit: number = 20
): Promise<PopularProfile[]> => {
  const { data } = await discoverApi.get(`/popular?timeframe=${timeframe}&limit=${limit}`);
  return data.profiles;
};

// Nearby users
export const getNearbyUsers = async (
  radius: number = 50,
  limit: number = 50
): Promise<NearbyUser[]> => {
  const { data } = await discoverApi.get(`/nearby?radius=${radius}&limit=${limit}`);
  return data.users;
};

export const updateLocation = async (
  latitude: number,
  longitude: number,
  address?: string
): Promise<void> => {
  await discoverApi.post('/location', {
    latitude,
    longitude,
    address
  });
};

// Online users
export const getOnlineUsers = async (limit: number = 50): Promise<OnlineUser[]> => {
  const { data } = await discoverApi.get(`/online?limit=${limit}`);
  return data.users;
};

// New users
export const getNewUsers = async (
  days: number = 7,
  limit: number = 20
): Promise<NewUser[]> => {
  const { data } = await discoverApi.get(`/new?days=${days}&limit=${limit}`);
  return data.users;
};

// Swipe actions
export const swipeUser = async (action: SwipeAction): Promise<SwipeResult> => {
  try {
    const { data } = await discoverApi.post('/swipe', action);
    return {
      success: true,
      isMatch: data.isMatch || false,
      match: data.match,
      remainingLikes: data.remainingLikes,
      remainingSuperLikes: data.remainingSuperLikes
    };
  } catch (error: any) {
    return {
      success: false,
      isMatch: false,
      error: error.response?.data?.message || 'Ошибка при свайпе'
    };
  }
};

export const undoLastSwipe = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const { data } = await discoverApi.post('/swipe/undo');
    return {
      success: true,
      message: data.message || 'Последнее действие отменено'
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка отмены действия'
    };
  }
};

// Super likes
export const sendSuperLike = async (userId: string, message?: string): Promise<SuperLike> => {
  const { data } = await discoverApi.post('/super-like', {
    userId,
    message
  });
  return data.superLike;
};

export const getSuperLikesCount = async (): Promise<{
  available: number;
  total: number;
  nextRefresh: string;
}> => {
  const { data } = await discoverApi.get('/super-likes/count');
  return data;
};

export const purchaseSuperLikes = async (count: number): Promise<{
  success: boolean;
  message: string;
  newCount?: number;
}> => {
  try {
    const { data } = await discoverApi.post('/super-likes/purchase', { count });
    return {
      success: true,
      message: data.message,
      newCount: data.newCount
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка покупки супер-лайков'
    };
  }
};

// Boost
export const boostProfile = async (
  type: 'profile_boost' | 'super_boost' | 'location_boost',
  duration: number = 30
): Promise<BoostSession> => {
  const { data } = await discoverApi.post('/boost', {
    type,
    duration
  });
  return data.session;
};

export const getActiveBoosts = async (): Promise<BoostSession[]> => {
  const { data } = await discoverApi.get('/boost/active');
  return data.sessions;
};

export const getBoostHistory = async (): Promise<BoostSession[]> => {
  const { data } = await discoverApi.get('/boost/history');
  return data.sessions;
};

// Settings
export const getDiscoverSettings = async (): Promise<DiscoverSettings> => {
  const { data } = await discoverApi.get('/settings');
  return data.settings;
};

export const updateDiscoverSettings = async (settings: Partial<DiscoverSettings>): Promise<void> => {
  await discoverApi.put('/settings', settings);
};

// Filters
export const saveFilters = async (filters: DiscoverFilters): Promise<void> => {
  await discoverApi.post('/filters/save', filters);
};

export const getSavedFilters = async (): Promise<DiscoverFilters[]> => {
  const { data } = await discoverApi.get('/filters/saved');
  return data.filters;
};

export const deleteSavedFilter = async (filterId: string): Promise<void> => {
  await discoverApi.delete(`/filters/saved/${filterId}`);
};

// Search
export const searchUsers = async (
  criteria: SearchCriteria,
  page: number = 1,
  limit: number = 20
): Promise<SearchResult> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  Object.entries(criteria).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        params.append(key, JSON.stringify(value));
      } else {
        params.append(key, value.toString());
      }
    }
  });

  const { data } = await discoverApi.get(`/search?${params.toString()}`);
  return data;
};

export const getSearchSuggestions = async (query: string): Promise<string[]> => {
  const { data } = await discoverApi.get(`/search/suggestions?query=${encodeURIComponent(query)}`);
  return data.suggestions;
};

export const saveSearch = async (criteria: SearchCriteria, name: string): Promise<void> => {
  await discoverApi.post('/search/save', { criteria, name });
};

export const getSavedSearches = async (): Promise<{ id: string; name: string; criteria: SearchCriteria }[]> => {
  const { data } = await discoverApi.get('/search/saved');
  return data.searches;
};

// Statistics
export const getDiscoverStats = async (): Promise<DiscoverStats> => {
  const { data } = await discoverApi.get('/stats');
  return data.stats;
};

export const getUserStats = async (userId: string): Promise<{
  profileViews: number;
  likesReceived: number;
  likesGiven: number;
  matchesCount: number;
  responseRate: number;
}> => {
  const { data } = await discoverApi.get(`/users/${userId}/stats`);
  return data.stats;
};

// Trending
export const getTrendingTopics = async (): Promise<TrendingTopic[]> => {
  const { data } = await discoverApi.get('/trending');
  return data.topics;
};

export const getTrendingInterests = async (): Promise<{ interest: string; count: number }[]> => {
  const { data } = await discoverApi.get('/trending/interests');
  return data.interests;
};

// Compatibility
export const getCompatibilityTests = async (): Promise<CompatibilityTest[]> => {
  const { data } = await discoverApi.get('/compatibility/tests');
  return data.tests;
};

export const takeCompatibilityTest = async (
  testId: string,
  answers: { questionId: string; answer: any }[]
): Promise<CompatibilityResult> => {
  const { data } = await discoverApi.post(`/compatibility/tests/${testId}/take`, { answers });
  return data.result;
};

export const getCompatibilityResults = async (): Promise<CompatibilityResult[]> => {
  const { data } = await discoverApi.get('/compatibility/results');
  return data.results;
};

export const calculateCompatibility = async (userId: string): Promise<{
  score: number;
  breakdown: any;
  recommendations: string[];
}> => {
  const { data } = await discoverApi.get(`/compatibility/calculate/${userId}`);
  return data;
};

// User actions
export const viewProfile = async (userId: string): Promise<void> => {
  await discoverApi.post(`/users/${userId}/view`);
};

export const reportUser = async (userId: string, reason: string, details?: string): Promise<void> => {
  await discoverApi.post(`/users/${userId}/report`, { reason, details });
};

export const blockUser = async (userId: string): Promise<void> => {
  await discoverApi.post(`/users/${userId}/block`);
};

export const unblockUser = async (userId: string): Promise<void> => {
  await discoverApi.post(`/users/${userId}/unblock`);
};

// Preferences learning
export const recordUserPreference = async (
  userId: string,
  action: 'like' | 'pass' | 'super_like',
  features: string[]
): Promise<void> => {
  await discoverApi.post('/preferences/record', {
    userId,
    action,
    features
  });
};

export const getPersonalizedRecommendations = async (): Promise<UserRecommendation[]> => {
  const { data } = await discoverApi.get('/recommendations/personalized');
  return data.recommendations;
};

// Location-based features
export const getPopularPlaces = async (radius: number = 10): Promise<{
  id: string;
  name: string;
  type: string;
  userCount: number;
  coordinates: { latitude: number; longitude: number };
}[]> => {
  const { data } = await discoverApi.get(`/places/popular?radius=${radius}`);
  return data.places;
};

export const getUsersAtPlace = async (placeId: string): Promise<DiscoverUser[]> => {
  const { data } = await discoverApi.get(`/places/${placeId}/users`);
  return data.users;
};

// Events and meetups
export const getNearbyEvents = async (radius: number = 25): Promise<{
  id: string;
  name: string;
  description: string;
  date: string;
  location: string;
  attendeeCount: number;
  interestedUsers: DiscoverUser[];
}[]> => {
  const { data } = await discoverApi.get(`/events/nearby?radius=${radius}`);
  return data.events;
};

export const expressInterestInEvent = async (eventId: string): Promise<void> => {
  await discoverApi.post(`/events/${eventId}/interest`);
};

// Premium features
export const getPremiumFeatures = async (): Promise<{
  unlimitedLikes: boolean;
  superLikesPerDay: number;
  boostsPerMonth: number;
  seeWhoLikesYou: boolean;
  advancedFilters: boolean;
  readReceipts: boolean;
  undoSwipes: boolean;
}> => {
  const { data } = await discoverApi.get('/premium/features');
  return data.features;
};

export const activateInvisibleMode = async (duration: number): Promise<void> => {
  await discoverApi.post('/premium/invisible-mode', { duration });
};

export const deactivateInvisibleMode = async (): Promise<void> => {
  await discoverApi.post('/premium/invisible-mode/deactivate');
};
