import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardMedia,
  useTheme,
  useMediaQuery,
  Fade,
  Grid,
  Chip,
  LinearProgress,
  Avatar,
  AvatarGroup
} from '@mui/material';
import {
  Games as GamesIcon,
  Psychology as PsychologyIcon,
  Quiz as QuizIcon,
  EmojiEvents as TrophyIcon,
  PlayArrow as PlayIcon,
  People as PeopleIcon,
  Timer as TimerIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  Favorite as HeartIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для игр
interface Game {
  id: string;
  title: string;
  description: string;
  image: string;
  category: 'compatibility' | 'quiz' | 'icebreaker' | 'challenge';
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number; // в минутах
  playersCount: number;
  rating: number;
  isPopular: boolean;
  isNew: boolean;
  requiredLevel: number;
  rewards: {
    points: number;
    badges: string[];
  };
}

interface GameStats {
  totalGamesPlayed: number;
  totalPoints: number;
  level: number;
  nextLevelProgress: number;
  badges: string[];
  rank: number;
}

const GamesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [games, setGames] = useState<Game[]>([]);
  const [gameStats, setGameStats] = useState<GameStats | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Категории игр
  const categories = [
    { id: 'all', name: 'Все игры', icon: <GamesIcon /> },
    { id: 'compatibility', name: 'Совместимость', icon: <HeartIcon /> },
    { id: 'quiz', name: 'Викторины', icon: <QuizIcon /> },
    { id: 'icebreaker', name: 'Знакомство', icon: <PeopleIcon /> },
    { id: 'challenge', name: 'Вызовы', icon: <TrophyIcon /> }
  ];

  // Загрузка игр при монтировании компонента
  useEffect(() => {
    loadGames();
    loadGameStats();
  }, []);

  const loadGames = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения игр
      // const response = await getGames();
      
      // Мок данные
      const mockGames: Game[] = [
        {
          id: 'compatibility-test',
          title: 'Тест совместимости',
          description: 'Узнайте, насколько вы совместимы с другими пользователями',
          image: '/games/compatibility.jpg',
          category: 'compatibility',
          difficulty: 'medium',
          duration: 10,
          playersCount: 15420,
          rating: 4.8,
          isPopular: true,
          isNew: false,
          requiredLevel: 1,
          rewards: {
            points: 50,
            badges: ['Психолог', 'Аналитик']
          }
        },
        {
          id: 'questions-game',
          title: 'Игра в вопросы',
          description: 'Отвечайте на интересные вопросы и узнавайте друг друга лучше',
          image: '/games/questions.jpg',
          category: 'icebreaker',
          difficulty: 'easy',
          duration: 5,
          playersCount: 8930,
          rating: 4.6,
          isPopular: false,
          isNew: true,
          requiredLevel: 1,
          rewards: {
            points: 25,
            badges: ['Собеседник']
          }
        },
        {
          id: 'personality-quiz',
          title: 'Тест личности',
          description: 'Определите свой тип личности и найдите подходящих партнеров',
          image: '/games/personality.jpg',
          category: 'quiz',
          difficulty: 'hard',
          duration: 15,
          playersCount: 12340,
          rating: 4.9,
          isPopular: true,
          isNew: false,
          requiredLevel: 3,
          rewards: {
            points: 100,
            badges: ['Мыслитель', 'Эксперт']
          }
        },
        {
          id: 'speed-dating',
          title: 'Быстрые свидания',
          description: 'Короткие вопросы для быстрого знакомства',
          image: '/games/speed-dating.jpg',
          category: 'challenge',
          difficulty: 'medium',
          duration: 3,
          playersCount: 6780,
          rating: 4.4,
          isPopular: false,
          isNew: true,
          requiredLevel: 2,
          rewards: {
            points: 30,
            badges: ['Спринтер']
          }
        },
        {
          id: 'truth-or-dare',
          title: 'Правда или действие',
          description: 'Классическая игра для раскрепощения и веселья',
          image: '/games/truth-dare.jpg',
          category: 'icebreaker',
          difficulty: 'easy',
          duration: 8,
          playersCount: 9560,
          rating: 4.3,
          isPopular: false,
          isNew: false,
          requiredLevel: 1,
          rewards: {
            points: 40,
            badges: ['Смельчак']
          }
        },
        {
          id: 'love-language',
          title: 'Языки любви',
          description: 'Определите свой язык любви и найдите совместимых партнеров',
          image: '/games/love-language.jpg',
          category: 'compatibility',
          difficulty: 'medium',
          duration: 12,
          playersCount: 11200,
          rating: 4.7,
          isPopular: true,
          isNew: false,
          requiredLevel: 2,
          rewards: {
            points: 60,
            badges: ['Романтик', 'Знаток любви']
          }
        }
      ];

      setGames(mockGames);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке игр');
    } finally {
      setLoading(false);
    }
  };

  const loadGameStats = async () => {
    try {
      // Здесь будет вызов API для получения статистики игр
      // const response = await getGameStats();
      
      // Мок данные
      const mockStats: GameStats = {
        totalGamesPlayed: 23,
        totalPoints: 1250,
        level: 5,
        nextLevelProgress: 65,
        badges: ['Новичок', 'Психолог', 'Собеседник', 'Аналитик'],
        rank: 1247
      };

      setGameStats(mockStats);
    } catch (err) {
      console.error('Ошибка при загрузке статистики:', err);
    }
  };

  const handleGameClick = (game: Game) => {
    // Проверяем уровень пользователя
    if (gameStats && gameStats.level < game.requiredLevel) {
      setError(`Для этой игры требуется ${game.requiredLevel} уровень. Ваш уровень: ${gameStats.level}`);
      return;
    }

    // Перенаправляем на соответствующую игру
    switch (game.id) {
      case 'compatibility-test':
        router.push('/games/compatibility');
        break;
      case 'questions-game':
        router.push('/games/questions');
        break;
      default:
        router.push(`/games/${game.id}`);
    }
  };

  const handleViewAchievements = () => {
    router.push('/games/achievements');
  };

  const getFilteredGames = () => {
    if (selectedCategory === 'all') return games;
    return games.filter(game => game.category === selectedCategory);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'error';
      default: return 'default';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} мин`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}ч ${mins}м` : `${hours}ч`;
  };

  if (!user) {
    return (
      <Layout title="Игры">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  const filteredGames = getFilteredGames();

  return (
    <>
      <Head>
        <title>Игры и развлечения - Likes & Love</title>
        <meta name="description" content="Играйте в интересные игры, проходите тесты и узнавайте друг друга лучше в приложении знакомств Likes & Love" />
        <meta name="keywords" content="игры, тесты, совместимость, знакомства, развлечения, викторины" />
      </Head>

      <Layout title="Игры">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 2 }}>
                Игры и развлечения
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Играйте, узнавайте друг друга лучше и зарабатывайте награды
              </Typography>
            </Box>

            {/* Статистика пользователя */}
            {gameStats && (
              <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                <CardContent>
                  <Grid container spacing={3} alignItems="center">
                    <Grid item xs={12} md={8}>
                      <Box sx={{ color: 'white' }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                          Ваша игровая статистика
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {gameStats.level}
                              </Typography>
                              <Typography variant="body2">Уровень</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {gameStats.totalPoints}
                              </Typography>
                              <Typography variant="body2">Очки</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                {gameStats.totalGamesPlayed}
                              </Typography>
                              <Typography variant="body2">Игр сыграно</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" fontWeight="bold">
                                #{gameStats.rank}
                              </Typography>
                              <Typography variant="body2">Рейтинг</Typography>
                            </Box>
                          </Grid>
                        </Grid>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Прогресс до {gameStats.level + 1} уровня
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={gameStats.nextLevelProgress}
                            sx={{ 
                              height: 8, 
                              borderRadius: 4,
                              backgroundColor: 'rgba(255,255,255,0.3)',
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: 'white'
                              }
                            }}
                          />
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Button
                          variant="contained"
                          onClick={handleViewAchievements}
                          startIcon={<TrophyIcon />}
                          sx={{ 
                            backgroundColor: 'white', 
                            color: 'primary.main',
                            '&:hover': { backgroundColor: 'grey.100' }
                          }}
                        >
                          Достижения ({gameStats.badges.length})
                        </Button>
                        <Box sx={{ mt: 2 }}>
                          <AvatarGroup max={4}>
                            {gameStats.badges.slice(0, 4).map((badge, index) => (
                              <Avatar 
                                key={index}
                                sx={{ 
                                  bgcolor: 'white', 
                                  color: 'primary.main',
                                  width: 32,
                                  height: 32,
                                  fontSize: 12
                                }}
                              >
                                🏆
                              </Avatar>
                            ))}
                          </AvatarGroup>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {/* Категории */}
            <Box sx={{ mb: 4 }}>
              <Grid container spacing={1}>
                {categories.map((category) => (
                  <Grid item key={category.id}>
                    <Chip
                      icon={category.icon}
                      label={category.name}
                      onClick={() => setSelectedCategory(category.id)}
                      color={selectedCategory === category.id ? 'primary' : 'default'}
                      variant={selectedCategory === category.id ? 'filled' : 'outlined'}
                      sx={{ cursor: 'pointer' }}
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Сетка игр */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {filteredGames.map((game) => (
                  <Grid item xs={12} sm={6} md={4} key={game.id}>
                    <Card 
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        position: 'relative',
                        '&:hover': {
                          transform: 'scale(1.02)',
                          transition: 'transform 0.2s',
                          boxShadow: theme.shadows[8]
                        }
                      }}
                      onClick={() => handleGameClick(game)}
                    >
                      <CardMedia
                        component="img"
                        height="200"
                        image={game.image}
                        alt={game.title}
                        sx={{ objectFit: 'cover' }}
                      />
                      
                      {/* Бейджи */}
                      <Box sx={{ position: 'absolute', top: 8, left: 8, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {game.isNew && (
                          <Chip label="Новое" size="small" color="success" />
                        )}
                        {game.isPopular && (
                          <Chip label="Популярное" size="small" color="warning" />
                        )}
                        <Chip 
                          label={game.difficulty} 
                          size="small" 
                          color={getDifficultyColor(game.difficulty) as any}
                        />
                      </Box>

                      {/* Уровень требования */}
                      {game.requiredLevel > 1 && (
                        <Chip 
                          label={`Уровень ${game.requiredLevel}+`}
                          size="small"
                          sx={{ 
                            position: 'absolute', 
                            top: 8, 
                            right: 8,
                            backgroundColor: gameStats && gameStats.level >= game.requiredLevel ? 'success.main' : 'error.main',
                            color: 'white'
                          }}
                        />
                      )}

                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {game.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {game.description}
                        </Typography>

                        {/* Статистика игры */}
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <TimerIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              {formatDuration(game.duration)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <PeopleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              {game.playersCount.toLocaleString()}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <StarIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                            <Typography variant="caption" color="text.secondary">
                              {game.rating}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Награды */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TrophyIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                            <Typography variant="caption" color="text.secondary">
                              +{game.rewards.points} очков
                            </Typography>
                          </Box>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<PlayIcon />}
                            disabled={gameStats ? gameStats.level < game.requiredLevel : false}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleGameClick(game);
                            }}
                          >
                            Играть
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Пустое состояние */}
            {!loading && filteredGames.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <GamesIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                  Игры не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  В этой категории пока нет игр
                </Typography>
              </Box>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default GamesPage;
