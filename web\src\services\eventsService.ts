import axios from 'axios';
import { config } from '../config';
import {
  Event,
  EventRegistration,
  EventNotification,
  EventStatistics,
  EventSearchResult,
  EventTemplate,
  EventSuggestion,
  EventChat,
  CreateEventRequest,
  UpdateEventRequest,
  EventFilters,
  PaginatedResponse
} from '../types/events.types';

const eventsApi = axios.create({
  baseURL: `${config.api.baseUrl}/events`,
  withCredentials: true,
});

// Events CRUD
export const getEvents = async (
  filters?: EventFilters,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Event>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await eventsApi.get(`/?${params.toString()}`);
  return data;
};

export const getEvent = async (id: string): Promise<Event> => {
  const { data } = await eventsApi.get(`/${id}`);
  return data.event;
};

export const createEvent = async (request: CreateEventRequest): Promise<Event> => {
  const formData = new FormData();
  
  // Add basic fields
  Object.entries(request).forEach(([key, value]) => {
    if (key === 'coverImage' && value) {
      formData.append('coverImage', value as File);
    } else if (key === 'photos' && value) {
      (value as File[]).forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });
    } else if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    }
  });

  const { data } = await eventsApi.post('/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.event;
};

export const updateEvent = async (id: string, request: UpdateEventRequest): Promise<Event> => {
  const { data } = await eventsApi.put(`/${id}`, request);
  return data.event;
};

export const deleteEvent = async (id: string): Promise<void> => {
  await eventsApi.delete(`/${id}`);
};

export const publishEvent = async (id: string): Promise<Event> => {
  const { data } = await eventsApi.post(`/${id}/publish`);
  return data.event;
};

export const cancelEvent = async (id: string, reason?: string): Promise<void> => {
  await eventsApi.post(`/${id}/cancel`, { reason });
};

export const postponeEvent = async (
  id: string,
  newStartDate: string,
  newEndDate: string,
  reason?: string
): Promise<Event> => {
  const { data } = await eventsApi.post(`/${id}/postpone`, {
    newStartDate,
    newEndDate,
    reason
  });
  return data.event;
};

// Event registration
export const registerForEvent = async (
  eventId: string,
  ticketId?: string,
  additionalInfo?: any
): Promise<EventRegistration> => {
  const { data } = await eventsApi.post(`/${eventId}/register`, {
    ticketId,
    additionalInfo
  });
  return data.registration;
};

export const cancelRegistration = async (registrationId: string): Promise<void> => {
  await eventsApi.delete(`/registrations/${registrationId}`);
};

export const updateRegistrationStatus = async (
  registrationId: string,
  status: 'going' | 'interested' | 'maybe' | 'not_going'
): Promise<void> => {
  await eventsApi.put(`/registrations/${registrationId}/status`, { status });
};

export const checkInToEvent = async (
  eventId: string,
  checkInCode: string,
  location?: { latitude: number; longitude: number }
): Promise<void> => {
  await eventsApi.post(`/${eventId}/check-in`, { checkInCode, location });
};

// My events and registrations
export const getMyEvents = async (
  type: 'organized' | 'registered' | 'all' = 'all',
  status?: Event['status'],
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Event>> => {
  const params = new URLSearchParams({
    type,
    page: page.toString(),
    limit: limit.toString()
  });

  if (status) {
    params.append('status', status);
  }

  const { data } = await eventsApi.get(`/my?${params.toString()}`);
  return data;
};

export const getMyRegistrations = async (
  status?: EventRegistration['status'],
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<EventRegistration>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  if (status) {
    params.append('status', status);
  }

  const { data } = await eventsApi.get(`/registrations/my?${params.toString()}`);
  return data;
};

export const getUpcomingEvents = async (limit: number = 10): Promise<Event[]> => {
  const { data } = await eventsApi.get(`/upcoming?limit=${limit}`);
  return data.events;
};

// Search and discovery
export const searchEvents = async (
  query: string,
  filters?: EventFilters,
  page: number = 1,
  limit: number = 20
): Promise<EventSearchResult> => {
  const params = new URLSearchParams({
    query,
    page: page.toString(),
    limit: limit.toString()
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });
  }

  const { data } = await eventsApi.get(`/search?${params.toString()}`);
  return data;
};

export const getEventSuggestions = async (limit: number = 10): Promise<EventSuggestion[]> => {
  const { data } = await eventsApi.get(`/suggestions?limit=${limit}`);
  return data.suggestions;
};

export const getTrendingEvents = async (
  timeframe: 'day' | 'week' | 'month' = 'week',
  limit: number = 20
): Promise<Event[]> => {
  const { data } = await eventsApi.get(`/trending?timeframe=${timeframe}&limit=${limit}`);
  return data.events;
};

export const getFeaturedEvents = async (limit: number = 10): Promise<Event[]> => {
  const { data } = await eventsApi.get(`/featured?limit=${limit}`);
  return data.events;
};

export const getNearbyEvents = async (
  latitude: number,
  longitude: number,
  radius: number = 25,
  limit: number = 20
): Promise<Event[]> => {
  const { data } = await eventsApi.get(
    `/nearby?lat=${latitude}&lng=${longitude}&radius=${radius}&limit=${limit}`
  );
  return data.events;
};

export const getEventsByCategory = async (
  category: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Event>> => {
  const { data } = await eventsApi.get(`/category/${category}?page=${page}&limit=${limit}`);
  return data;
};

export const getEventsByOrganizer = async (
  organizerId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<Event>> => {
  const { data } = await eventsApi.get(`/organizer/${organizerId}?page=${page}&limit=${limit}`);
  return data;
};

// Event templates
export const getEventTemplates = async (): Promise<EventTemplate[]> => {
  const { data } = await eventsApi.get('/templates');
  return data.templates;
};

export const createEventFromTemplate = async (
  templateId: string,
  customizations: Partial<CreateEventRequest>
): Promise<Event> => {
  const { data } = await eventsApi.post(`/templates/${templateId}/create`, customizations);
  return data.event;
};

// Event chat and networking
export const getEventChat = async (eventId: string): Promise<EventChat> => {
  const { data } = await eventsApi.get(`/${eventId}/chat`);
  return data.chat;
};

export const sendEventChatMessage = async (
  eventId: string,
  message: string
): Promise<void> => {
  await eventsApi.post(`/${eventId}/chat/messages`, { message });
};

export const getEventNetworking = async (eventId: string): Promise<any> => {
  const { data } = await eventsApi.get(`/${eventId}/networking`);
  return data.networking;
};

export const connectWithParticipant = async (
  eventId: string,
  participantId: string,
  message?: string
): Promise<void> => {
  await eventsApi.post(`/${eventId}/networking/connect`, {
    participantId,
    message
  });
};

// Notifications
export const getEventNotifications = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<EventNotification>> => {
  const { data } = await eventsApi.get(`/notifications?page=${page}&limit=${limit}`);
  return data;
};

export const markNotificationAsRead = async (notificationId: string): Promise<void> => {
  await eventsApi.put(`/notifications/${notificationId}/read`);
};

export const markAllNotificationsAsRead = async (): Promise<void> => {
  await eventsApi.put('/notifications/read-all');
};

// Feedback and rating
export const rateEvent = async (
  eventId: string,
  rating: number,
  comment?: string,
  aspects?: any
): Promise<void> => {
  await eventsApi.post(`/${eventId}/rate`, { rating, comment, aspects });
};

export const getEventReviews = async (
  eventId: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponse<any>> => {
  const { data } = await eventsApi.get(`/${eventId}/reviews?page=${page}&limit=${limit}`);
  return data;
};

export const markReviewHelpful = async (reviewId: string): Promise<void> => {
  await eventsApi.post(`/reviews/${reviewId}/helpful`);
};

// Reporting
export const reportEvent = async (
  eventId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await eventsApi.post(`/${eventId}/report`, { reason, details });
};

export const reportEventParticipant = async (
  eventId: string,
  participantId: string,
  reason: string,
  details?: string
): Promise<void> => {
  await eventsApi.post(`/${eventId}/participants/${participantId}/report`, {
    reason,
    details
  });
};

// Statistics and analytics
export const getEventStatistics = async (): Promise<EventStatistics> => {
  const { data } = await eventsApi.get('/statistics');
  return data.statistics;
};

export const getEventAnalytics = async (eventId: string): Promise<any> => {
  const { data } = await eventsApi.get(`/${eventId}/analytics`);
  return data.analytics;
};

export const getOrganizerStatistics = async (organizerId: string): Promise<any> => {
  const { data } = await eventsApi.get(`/organizer/${organizerId}/statistics`);
  return data.statistics;
};

// Categories and tags
export const getEventCategories = async (): Promise<string[]> => {
  const { data } = await eventsApi.get('/categories');
  return data.categories;
};

export const getPopularTags = async (limit: number = 50): Promise<{ tag: string; count: number }[]> => {
  const { data } = await eventsApi.get(`/tags/popular?limit=${limit}`);
  return data.tags;
};

// Organizer management
export const followOrganizer = async (organizerId: string): Promise<void> => {
  await eventsApi.post(`/organizer/${organizerId}/follow`);
};

export const unfollowOrganizer = async (organizerId: string): Promise<void> => {
  await eventsApi.post(`/organizer/${organizerId}/unfollow`);
};

export const getFollowedOrganizers = async (): Promise<any[]> => {
  const { data } = await eventsApi.get('/organizers/following');
  return data.organizers;
};

// Event sharing
export const shareEvent = async (
  eventId: string,
  platform: 'facebook' | 'twitter' | 'linkedin' | 'whatsapp' | 'telegram' | 'copy'
): Promise<string> => {
  const { data } = await eventsApi.post(`/${eventId}/share`, { platform });
  return data.shareUrl;
};

export const getEventShareStats = async (eventId: string): Promise<any> => {
  const { data } = await eventsApi.get(`/${eventId}/share/stats`);
  return data.stats;
};

// Calendar integration
export const exportEventToCalendar = async (
  eventId: string,
  format: 'ics' | 'google' | 'outlook'
): Promise<string> => {
  const { data } = await eventsApi.get(`/${eventId}/export/${format}`);
  return data.url || data.content;
};

export const syncEventsWithCalendar = async (
  provider: 'google' | 'outlook' | 'apple',
  accessToken: string
): Promise<void> => {
  await eventsApi.post('/calendar/sync', { provider, accessToken });
};

// Bulk operations
export const bulkUpdateEvents = async (
  eventIds: string[],
  updates: Partial<UpdateEventRequest>
): Promise<void> => {
  await eventsApi.put('/bulk-update', { eventIds, updates });
};

export const bulkDeleteEvents = async (eventIds: string[]): Promise<void> => {
  await eventsApi.delete('/bulk-delete', { data: { eventIds } });
};

// Waitlist management
export const joinWaitlist = async (eventId: string): Promise<void> => {
  await eventsApi.post(`/${eventId}/waitlist/join`);
};

export const leaveWaitlist = async (eventId: string): Promise<void> => {
  await eventsApi.post(`/${eventId}/waitlist/leave`);
};

export const getWaitlistPosition = async (eventId: string): Promise<number> => {
  const { data } = await eventsApi.get(`/${eventId}/waitlist/position`);
  return data.position;
};

// Live streaming
export const startLiveStream = async (eventId: string): Promise<string> => {
  const { data } = await eventsApi.post(`/${eventId}/stream/start`);
  return data.streamUrl;
};

export const stopLiveStream = async (eventId: string): Promise<void> => {
  await eventsApi.post(`/${eventId}/stream/stop`);
};

export const getStreamViewers = async (eventId: string): Promise<number> => {
  const { data } = await eventsApi.get(`/${eventId}/stream/viewers`);
  return data.viewerCount;
};
