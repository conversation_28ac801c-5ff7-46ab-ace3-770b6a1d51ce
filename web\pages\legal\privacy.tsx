import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  ArrowBack,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';

// Типы для разделов политики конфиденциальности
interface PrivacySection {
  id: string;
  title: string;
  content: string[];
  subsections?: {
    title: string;
    content: string[];
  }[];
  important?: boolean;
  table?: {
    headers: string[];
    rows: string[][];
  };
}

const PrivacyPolicyPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [expandedSection, setExpandedSection] = useState<string | false>('overview');

  const lastUpdated = '15 декабря 2024 г.';
  const effectiveDate = '1 января 2025 г.';

  // Разделы политики конфиденциальности
  const privacySections: PrivacySection[] = [
    {
      id: 'overview',
      title: '1. Обзор',
      important: true,
      content: [
        'Настоящая Политика конфиденциальности описывает, как мы собираем, используем и защищаем вашу личную информацию при использовании приложения "Likes & Love".',
        'Мы серьезно относимся к защите вашей конфиденциальности и обязуемся обеспечивать безопасность ваших персональных данных.',
        'Используя наш Сервис, вы соглашаетесь на сбор и использование информации в соответствии с данной политикой.',
        'Мы обновляем эту политику по мере необходимости и уведомляем вас о существенных изменениях.'
      ]
    },
    {
      id: 'data_collection',
      title: '2. Какую информацию мы собираем',
      important: true,
      content: [
        'Мы собираем различные типы информации для предоставления и улучшения нашего Сервиса:'
      ],
      subsections: [
        {
          title: 'Информация, которую вы предоставляете',
          content: [
            '• Регистрационные данные (имя, email, дата рождения)',
            '• Информация профиля (фотографии, описание, интересы)',
            '• Контент, который вы создаете (сообщения, комментарии)',
            '• Настройки и предпочтения',
            '• Информация для верификации'
          ]
        },
        {
          title: 'Автоматически собираемая информация',
          content: [
            '• Данные об устройстве (модель, операционная система)',
            '• Информация о сети (IP-адрес, провайдер)',
            '• Данные об использовании (время в приложении, функции)',
            '• Геолокационные данные (с вашего согласия)',
            '• Файлы cookie и аналогичные технологии'
          ]
        }
      ]
    },
    {
      id: 'data_usage',
      title: '3. Как мы используем вашу информацию',
      content: [
        'Мы используем собранную информацию для следующих целей:'
      ],
      table: {
        headers: ['Цель использования', 'Типы данных', 'Правовое основание'],
        rows: [
          ['Предоставление сервиса', 'Профильные данные, сообщения', 'Исполнение договора'],
          ['Подбор совпадений', 'Предпочтения, геолокация', 'Исполнение договора'],
          ['Безопасность', 'Все типы данных', 'Законные интересы'],
          ['Улучшение сервиса', 'Данные об использовании', 'Законные интересы'],
          ['Маркетинг', 'Контактные данные', 'Согласие'],
          ['Поддержка клиентов', 'Контактные данные, сообщения', 'Исполнение договора']
        ]
      }
    },
    {
      id: 'data_sharing',
      title: '4. Передача данных третьим лицам',
      important: true,
      content: [
        'Мы не продаем ваши персональные данные. Мы можем передавать информацию в следующих случаях:'
      ],
      subsections: [
        {
          title: 'Поставщики услуг',
          content: [
            'Облачные сервисы для хранения данных',
            'Платежные системы для обработки платежей',
            'Аналитические сервисы для улучшения приложения',
            'Службы доставки уведомлений'
          ]
        },
        {
          title: 'Правовые требования',
          content: [
            'По требованию правоохранительных органов',
            'Для защиты наших прав и безопасности',
            'В случае реорганизации или продажи компании',
            'С вашего явного согласия'
          ]
        }
      ]
    },
    {
      id: 'data_security',
      title: '5. Безопасность данных',
      content: [
        'Мы применяем современные технические и организационные меры для защиты ваших данных:',
        '• Шифрование данных при передаче и хранении',
        '• Регулярные аудиты безопасности',
        '• Ограниченный доступ к персональным данным',
        '• Обучение сотрудников вопросам конфиденциальности',
        '• Мониторинг подозрительной активности'
      ]
    },
    {
      id: 'data_retention',
      title: '6. Хранение данных',
      content: [
        'Мы храним ваши данные только в течение необходимого периода:',
        '• Данные профиля - до удаления аккаунта',
        '• Сообщения - в течение 7 лет после удаления',
        '• Данные об использовании - до 2 лет',
        '• Финансовые данные - в соответствии с законодательством',
        '• Данные для безопасности - до 5 лет'
      ]
    },
    {
      id: 'user_rights',
      title: '7. Ваши права',
      important: true,
      content: [
        'В соответствии с применимым законодательством вы имеете следующие права:'
      ],
      subsections: [
        {
          title: 'Права доступа и контроля',
          content: [
            'Право на доступ к вашим персональным данным',
            'Право на исправление неточных данных',
            'Право на удаление данных ("право на забвение")',
            'Право на ограничение обработки',
            'Право на портируемость данных'
          ]
        },
        {
          title: 'Как реализовать права',
          content: [
            'Через настройки приложения',
            'Обращение в службу поддержки',
            'Отправка запроса на <EMAIL>',
            'Мы отвечаем на запросы в течение 30 дней'
          ]
        }
      ]
    },
    {
      id: 'cookies',
      title: '8. Файлы cookie и отслеживание',
      content: [
        'Мы используем файлы cookie и аналогичные технологии для:',
        '• Обеспечения работы основных функций',
        '• Запоминания ваших предпочтений',
        '• Анализа использования приложения',
        '• Персонализации контента',
        '• Показа релевантной рекламы'
      ],
      subsections: [
        {
          title: 'Управление cookie',
          content: [
            'Вы можете управлять cookie через настройки браузера',
            'Отключение cookie может ограничить функциональность',
            'Некоторые cookie необходимы для работы сервиса'
          ]
        }
      ]
    },
    {
      id: 'international',
      title: '9. Международные передачи',
      content: [
        'Ваши данные могут обрабатываться в странах за пределами вашего местоположения.',
        'Мы обеспечиваем адекватный уровень защиты при международных передачах.',
        'Используем стандартные договорные положения ЕС для передач в третьи страны.',
        'Все передачи осуществляются в соответствии с применимым законодательством.'
      ]
    },
    {
      id: 'children',
      title: '10. Защита детей',
      content: [
        'Наш Сервис предназначен для лиц старше 18 лет.',
        'Мы сознательно не собираем данные детей младше 18 лет.',
        'Если нам станет известно о сборе данных ребенка, мы немедленно удалим эту информацию.',
        'Родители могут обратиться к нам для удаления данных своих детей.'
      ]
    },
    {
      id: 'changes',
      title: '11. Изменения в политике',
      content: [
        'Мы можем обновлять данную Политику конфиденциальности время от времени.',
        'О существенных изменениях мы уведомим вас через приложение или email.',
        'Рекомендуем периодически просматривать эту страницу.',
        'Продолжение использования Сервиса означает согласие с обновленной политикой.'
      ]
    },
    {
      id: 'contact',
      title: '12. Контактная информация',
      content: [
        'По вопросам конфиденциальности обращайтесь:',
        '• Email: <EMAIL>',
        '• Телефон: +7 (800) 555-0123',
        '• Почтовый адрес: 123456, г. Москва, ул. Примерная, д. 1',
        '• Через форму обратной связи в приложении',
        '• К нашему специалисту по защите данных (DPO)'
      ]
    }
  ];

  const handleSectionExpand = (sectionId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? sectionId : false);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Здесь будет логика скачивания PDF версии
    console.log('Скачивание PDF версии политики конфиденциальности');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <Head>
        <title>Политика конфиденциальности - Likes & Love</title>
        <meta name="description" content="Политика конфиденциальности приложения знакомств Likes & Love" />
        <meta name="keywords" content="политика конфиденциальности, защита данных, приватность, GDPR, Likes & Love" />
      </Head>

      <Layout title="Политика конфиденциальности">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Политика конфиденциальности
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Как мы защищаем ваши персональные данные
              </Typography>
            </Box>

            {/* Информация о документе */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Последнее обновление: {lastUpdated}
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                      Дата вступления в силу: {effectiveDate}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PrintIcon />}
                      onClick={handlePrint}
                    >
                      Печать
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={handleDownload}
                    >
                      PDF
                    </Button>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Эта политика объясняет, как мы собираем, используем и защищаем ваши персональные данные. 
                    Мы соблюдаем требования GDPR и российского законодательства о персональных данных.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>

            {/* Разделы политики */}
            {privacySections.map((section) => (
              <Accordion
                key={section.id}
                expanded={expandedSection === section.id}
                onChange={handleSectionExpand(section.id)}
                sx={{ mb: 2 }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`${section.id}-content`}
                  id={`${section.id}-header`}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <SecurityIcon color="primary" />
                    <Typography variant="h6" fontWeight="bold" sx={{ flexGrow: 1 }}>
                      {section.title}
                    </Typography>
                    {section.important && (
                      <Chip 
                        label="Важно" 
                        color="warning" 
                        size="small"
                        icon={<WarningIcon />}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ pl: 5 }}>
                    {section.content.map((paragraph, index) => (
                      <Typography 
                        key={index} 
                        variant="body2" 
                        sx={{ 
                          mb: 1.5,
                          lineHeight: 1.6,
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {paragraph}
                      </Typography>
                    ))}
                    
                    {section.table && (
                      <TableContainer component={Paper} sx={{ mt: 2, mb: 2 }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              {section.table.headers.map((header, index) => (
                                <TableCell key={index} sx={{ fontWeight: 'bold' }}>
                                  {header}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {section.table.rows.map((row, rowIndex) => (
                              <TableRow key={rowIndex}>
                                {row.map((cell, cellIndex) => (
                                  <TableCell key={cellIndex}>
                                    {cell}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                    
                    {section.subsections && section.subsections.map((subsection, subIndex) => (
                      <Box key={subIndex} sx={{ mt: 3, pl: 2, borderLeft: 2, borderColor: 'primary.light' }}>
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                          {subsection.title}
                        </Typography>
                        {subsection.content.map((subParagraph, subParIndex) => (
                          <Typography 
                            key={subParIndex} 
                            variant="body2" 
                            sx={{ 
                              mb: 1,
                              lineHeight: 1.6,
                              whiteSpace: 'pre-line'
                            }}
                          >
                            {subParagraph}
                          </Typography>
                        ))}
                      </Box>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}

            {/* Быстрые действия */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Управление вашими данными
                </Typography>
                <Typography variant="body2" sx={{ mb: 3 }}>
                  Воспользуйтесь этими ссылками для управления своими персональными данными:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<VisibilityIcon />}
                    onClick={() => router.push('/settings/privacy')}
                  >
                    Настройки приватности
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<LockIcon />}
                    onClick={() => router.push('/settings/security')}
                  >
                    Безопасность
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/help/contact')}
                  >
                    Связаться с DPO
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/legal/cookies')}
                  >
                    Политика cookies
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Соответствие стандартам */}
            <Alert severity="success" sx={{ mt: 4 }}>
              <Typography variant="body2">
                <strong>Соответствие стандартам:</strong> Наша политика конфиденциальности соответствует 
                требованиям GDPR (Общий регламент по защите данных ЕС), Федерального закона "О персональных данных" 
                №152-ФЗ и другим применимым нормам в области защиты персональных данных.
              </Typography>
            </Alert>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PrivacyPolicyPage;
