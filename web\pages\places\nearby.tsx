import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  LocationOn as LocationIcon,
  MyLocation as MyLocationIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Map as MapIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Verified as VerifiedIcon,
  Event as EventIcon,
  People as MeetingIcon,
  Tune as TuneIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getNearbyPlaces,
  getPlaceCategories,
  addToFavorites,
  removeFromFavorites,
  getUserLocation
} from '../../src/services/placesService';
import { 
  Place,
  PlaceCategory 
} from '../../src/types/places.types';

const NearbyPlacesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [places, setPlaces] = useState<Place[]>([]);
  const [categories, setCategories] = useState<PlaceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [locationLoading, setLocationLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  
  // Filter states
  const [radius, setRadius] = useState(5); // km
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  useEffect(() => {
    if (userLocation) {
      loadNearbyPlaces();
    }
  }, [userLocation, radius, selectedCategory]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load categories
      const categoriesData = await getPlaceCategories();
      setCategories(categoriesData);
      
      // Get user location
      await getCurrentLocation();
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      setLocationLoading(true);
      setError(null);
      
      const location = await getUserLocation();
      setUserLocation(location);
      setSuccess('Местоположение определено');
    } catch (err: any) {
      setError('Не удалось определить местоположение. Разрешите доступ к геолокации.');
    } finally {
      setLocationLoading(false);
    }
  };

  const loadNearbyPlaces = async () => {
    if (!userLocation) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const nearbyPlaces = await getNearbyPlaces(
        userLocation.latitude,
        userLocation.longitude,
        radius,
        selectedCategory || undefined
      );
      
      setPlaces(nearbyPlaces);
    } catch (err: any) {
      setError('Ошибка загрузки мест рядом');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFavorite = async (place: Place) => {
    try {
      setActionLoading(place.id);
      setError(null);

      if (place.userInteraction?.isFavorite) {
        await removeFromFavorites(place.id);
        setSuccess('Место удалено из избранного');
      } else {
        await addToFavorites(place.id);
        setSuccess('Место добавлено в избранное');
      }
      
      // Update local state
      setPlaces(places.map(p => 
        p.id === place.id 
          ? { 
              ...p, 
              userInteraction: { 
                ...p.userInteraction, 
                isFavorite: !p.userInteraction?.isFavorite 
              } 
            }
          : p
      ));
    } catch (err: any) {
      setError('Ошибка обновления избранного');
    } finally {
      setActionLoading(null);
    }
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatDistance = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    if (distance.value < 1) {
      return `${Math.round(distance.value * 1000)} м`;
    }
    return `${distance.value.toFixed(1)} км`;
  };

  const getWalkingTime = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    // Approximate walking time: 5 km/h
    const walkingTimeMinutes = Math.round((distance.value / 5) * 60);
    
    if (walkingTimeMinutes < 60) {
      return `${walkingTimeMinutes} мин пешком`;
    }
    
    const hours = Math.floor(walkingTimeMinutes / 60);
    const minutes = walkingTimeMinutes % 60;
    return `${hours}ч ${minutes}м пешком`;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Места рядом - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите места для встреч рядом с вами в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <LocationIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Места рядом ({places.length})
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton 
                  onClick={getCurrentLocation} 
                  disabled={locationLoading}
                  color={userLocation ? 'primary' : 'default'}
                >
                  <MyLocationIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/places/map?nearby=true')}>
                  <MapIcon />
                </IconButton>
                <IconButton onClick={() => setShowFilters(!showFilters)}>
                  <TuneIcon />
                </IconButton>
                <IconButton onClick={loadNearbyPlaces} disabled={loading || !userLocation}>
                  <RefreshIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Location Status */}
            {!userLocation && !locationLoading && (
              <Alert severity="warning" sx={{ mb: 3 }} action={
                <Button color="inherit" size="small" onClick={getCurrentLocation}>
                  Определить
                </Button>
              }>
                Для поиска мест рядом необходимо определить ваше местоположение
              </Alert>
            )}

            {locationLoading && (
              <Alert severity="info" sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CircularProgress size={20} />
                  Определение местоположения...
                </Box>
              </Alert>
            )}

            {/* Filters */}
            {showFilters && userLocation && (
              <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Фильтры
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" gutterBottom>
                      Радиус поиска: {radius} км
                    </Typography>
                    <Slider
                      value={radius}
                      onChange={(_, value) => setRadius(value as number)}
                      min={1}
                      max={25}
                      step={1}
                      marks={[
                        { value: 1, label: '1км' },
                        { value: 5, label: '5км' },
                        { value: 10, label: '10км' },
                        { value: 25, label: '25км' }
                      ]}
                      valueLabelDisplay="auto"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Категория</InputLabel>
                      <Select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        label="Категория"
                      >
                        <MenuItem value="">Все категории</MenuItem>
                        {categories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Paper>
            )}

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  {userLocation ? 'Поиск мест рядом...' : 'Загрузка...'}
                </Typography>
              </Box>
            ) : !userLocation ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <MyLocationIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Местоположение не определено
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Разрешите доступ к геолокации, чтобы найти места рядом с вами
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<MyLocationIcon />}
                  onClick={getCurrentLocation}
                  disabled={locationLoading}
                >
                  {locationLoading ? 'Определение...' : 'Определить местоположение'}
                </Button>
              </Box>
            ) : places.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <LocationIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Места рядом не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Попробуйте увеличить радиус поиска или изменить категорию
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => setRadius(Math.min(radius * 2, 25))}
                  disabled={radius >= 25}
                >
                  Увеличить радиус поиска
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {places.map((place) => (
                    <Grid item xs={12} sm={6} md={4} key={place.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/places/${place.id}`)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={place.photos[0]?.url || '/placeholder-place.jpg'}
                            alt={place.name}
                            sx={{ objectFit: 'cover' }}
                          />
                          
                          {/* Favorite Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleFavorite(place);
                              }}
                              disabled={actionLoading === place.id}
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.9)',
                                '&:hover': { backgroundColor: 'rgba(255,255,255,1)' }
                              }}
                            >
                              {place.userInteraction?.isFavorite ? (
                                <FavoriteIcon color="error" />
                              ) : (
                                <FavoriteBorderIcon />
                              )}
                            </IconButton>
                          </Box>

                          {/* Distance Badge */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            zIndex: 1
                          }}>
                            <Chip
                              label={formatDistance(place.distance)}
                              size="small"
                              color="primary"
                              sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                            />
                          </Box>

                          {/* Verification Badge */}
                          {place.verification.isVerified && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<VerifiedIcon />}
                                label="Проверено"
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}
                        </Box>

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" component="h3" gutterBottom>
                            {place.name}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Rating
                              value={place.rating.average}
                              precision={0.1}
                              size="small"
                              readOnly
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              {place.rating.average.toFixed(1)} ({place.rating.count})
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {place.location.district || place.location.city}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <AttachMoney fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatPriceRange(place.priceRange)}
                            </Typography>
                          </Box>

                          {place.distance && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {getWalkingTime(place.distance)}
                              </Typography>
                            </Box>
                          )}

                          {place.shortDescription && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {place.shortDescription.length > 80 
                                ? `${place.shortDescription.substring(0, 80)}...`
                                : place.shortDescription
                              }
                            </Typography>
                          )}

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            <Chip
                              label={place.category.name}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            {place.tags.slice(0, 1).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {place.tags.length > 1 && (
                              <Chip
                                label={`+${place.tags.length - 1}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              size="small"
                              startIcon={<MeetingIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/meetings/create?placeId=${place.id}`);
                              }}
                            >
                              Встреча
                            </Button>
                            <Button
                              size="small"
                              startIcon={<EventIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/events/create?placeId=${place.id}`);
                              }}
                            >
                              Событие
                            </Button>
                          </Box>

                          <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                            {formatDistance(place.distance)}
                          </Typography>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NearbyPlacesPage;
