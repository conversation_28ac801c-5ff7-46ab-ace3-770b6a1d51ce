import axios from 'axios';
import {
  SubscriptionPlan,
  UserSubscription,
  PaymentMethod,
  PaymentTransaction,
  SubscriptionUsage,
  SubscriptionAnalytics,
  PromoCode,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  CancelSubscriptionRequest,
  CreatePaymentMethodRequest,
  ProcessPaymentRequest,
  ValidatePromoCodeRequest,
  ValidatePromoCodeResponse,
  PaginatedResponse
} from '../types/subscription.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// Subscription Plans
export const getAvailablePlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const response = await api.get('/subscription/plans');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки тарифных планов');
  }
};

export const getPlanById = async (planId: string): Promise<SubscriptionPlan> => {
  try {
    const response = await api.get(`/subscription/plans/${planId}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки тарифного плана');
  }
};

// User Subscription
export const getCurrentSubscription = async (): Promise<UserSubscription | null> => {
  try {
    const response = await api.get('/subscription/current');
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null; // No active subscription
    }
    throw new Error(error.response?.data?.message || 'Ошибка загрузки подписки');
  }
};

export const createSubscription = async (request: CreateSubscriptionRequest): Promise<UserSubscription> => {
  try {
    const response = await api.post('/subscription/create', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка создания подписки');
  }
};

export const updateSubscription = async (request: UpdateSubscriptionRequest): Promise<UserSubscription> => {
  try {
    const response = await api.put('/subscription/update', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления подписки');
  }
};

export const cancelSubscription = async (request: CancelSubscriptionRequest): Promise<void> => {
  try {
    await api.post('/subscription/cancel', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка отмены подписки');
  }
};

export const reactivateSubscription = async (): Promise<UserSubscription> => {
  try {
    const response = await api.post('/subscription/reactivate');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка возобновления подписки');
  }
};

// Subscription Usage
export const getSubscriptionUsage = async (): Promise<SubscriptionUsage> => {
  try {
    const response = await api.get('/subscription/usage');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки статистики использования');
  }
};

export const resetUsageLimit = async (feature: string): Promise<void> => {
  try {
    await api.post(`/subscription/usage/reset/${feature}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка сброса лимита');
  }
};

// Payment Methods
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  try {
    const response = await api.get('/payment/methods');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки способов оплаты');
  }
};

export const addPaymentMethod = async (request: CreatePaymentMethodRequest): Promise<PaymentMethod> => {
  try {
    const response = await api.post('/payment/methods', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка добавления способа оплаты');
  }
};

export const updatePaymentMethod = async (id: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod> => {
  try {
    const response = await api.put(`/payment/methods/${id}`, updates);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления способа оплаты');
  }
};

export const deletePaymentMethod = async (id: string): Promise<void> => {
  try {
    await api.delete(`/payment/methods/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка удаления способа оплаты');
  }
};

export const setDefaultPaymentMethod = async (id: string): Promise<void> => {
  try {
    await api.post(`/payment/methods/${id}/set-default`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка установки способа оплаты по умолчанию');
  }
};

// Payment Transactions
export const getTransactionHistory = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<PaymentTransaction>> => {
  try {
    const response = await api.get('/payment/transactions', {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки истории платежей');
  }
};

export const getTransactionById = async (id: string): Promise<PaymentTransaction> => {
  try {
    const response = await api.get(`/payment/transactions/${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки транзакции');
  }
};

export const processPayment = async (request: ProcessPaymentRequest): Promise<PaymentTransaction> => {
  try {
    const response = await api.post('/payment/process', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обработки платежа');
  }
};

export const refundTransaction = async (transactionId: string, reason: string): Promise<PaymentTransaction> => {
  try {
    const response = await api.post(`/payment/transactions/${transactionId}/refund`, { reason });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка возврата платежа');
  }
};

// Promo Codes
export const validatePromoCode = async (request: ValidatePromoCodeRequest): Promise<ValidatePromoCodeResponse> => {
  try {
    const response = await api.post('/subscription/promo-code/validate', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка проверки промокода');
  }
};

export const applyPromoCode = async (code: string, subscriptionId: string): Promise<UserSubscription> => {
  try {
    const response = await api.post('/subscription/promo-code/apply', { code, subscriptionId });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка применения промокода');
  }
};

// Analytics (for admin/premium users)
export const getSubscriptionAnalytics = async (): Promise<SubscriptionAnalytics> => {
  try {
    const response = await api.get('/subscription/analytics');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки аналитики подписок');
  }
};

// Billing
export const downloadInvoice = async (transactionId: string): Promise<Blob> => {
  try {
    const response = await api.get(`/payment/transactions/${transactionId}/invoice`, {
      responseType: 'blob'
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки счета');
  }
};

export const getUpcomingInvoice = async (): Promise<{
  amount: number;
  currency: string;
  dueDate: string;
  items: Array<{
    description: string;
    amount: number;
    quantity: number;
  }>;
}> => {
  try {
    const response = await api.get('/payment/upcoming-invoice');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки предстоящего счета');
  }
};

// Subscription Features Helper Functions
export const hasFeature = (subscription: UserSubscription | null, feature: string): boolean => {
  if (!subscription || subscription.status !== 'active') {
    return false;
  }
  
  const plan = subscription.plan;
  return plan.features.some(f => f.name === feature);
};

export const getFeatureLimit = (subscription: UserSubscription | null, feature: string): number | 'unlimited' => {
  if (!subscription || subscription.status !== 'active') {
    return 0;
  }
  
  const limits = subscription.plan.limits;
  return (limits as any)[feature] || 0;
};

export const getFeatureUsage = (
  usage: SubscriptionUsage | null,
  feature: string
): { used: number; limit: number | 'unlimited'; canUse: boolean } => {
  if (!usage) {
    return { used: 0, limit: 0, canUse: false };
  }
  
  const featureUsage = (usage.limits as any)[feature];
  if (!featureUsage) {
    return { used: 0, limit: 0, canUse: false };
  }
  
  const { used, limit } = featureUsage;
  const canUse = limit === 'unlimited' || used < limit;
  
  return { used, limit, canUse };
};

export const formatCurrency = (amount: number, currency: string = 'RUB'): string => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const calculateDiscount = (originalPrice: number, discountedPrice: number): number => {
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
};

export const getNextBillingDate = (subscription: UserSubscription): Date | null => {
  if (!subscription.nextBillingDate) {
    return null;
  }
  return new Date(subscription.nextBillingDate);
};

export const isTrialActive = (subscription: UserSubscription): boolean => {
  return subscription.isTrialActive && 
         subscription.trialEndDate && 
         new Date(subscription.trialEndDate) > new Date();
};

export const getDaysUntilTrialEnd = (subscription: UserSubscription): number => {
  if (!subscription.trialEndDate) {
    return 0;
  }
  
  const trialEnd = new Date(subscription.trialEndDate);
  const now = new Date();
  const diffTime = trialEnd.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
};
