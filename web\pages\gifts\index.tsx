import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardMedia,
  useTheme,
  useMediaQuery,
  Fade,
  Grid,
  Chip,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  CardGiftcard as GiftIcon,
  Favorite as FavoriteIcon,
  LocalFlorist as FlowerIcon,
  Cake as CakeIcon,
  Coffee as CoffeeIcon,
  Diamond as DiamondIcon,
  Send as SendIcon,
  History as HistoryIcon,
  Inbox as InboxIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для подарков
interface Gift {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  category: string;
  isPopular: boolean;
  isNew: boolean;
}

interface SendGiftForm {
  recipientId: string;
  message: string;
  isAnonymous: boolean;
}

// Схема валидации для отправки подарка
const sendGiftSchema = yup.object({
  recipientId: yup.string().required('Выберите получателя'),
  message: yup.string().max(200, 'Максимум 200 символов'),
  isAnonymous: yup.boolean()
});

const GiftsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedGift, setSelectedGift] = useState<Gift | null>(null);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [recentMatches, setRecentMatches] = useState<any[]>([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<SendGiftForm>({
    resolver: yupResolver(sendGiftSchema),
    defaultValues: {
      recipientId: '',
      message: '',
      isAnonymous: false
    }
  });

  // Категории подарков
  const categories = [
    { id: 'all', name: 'Все', icon: <GiftIcon /> },
    { id: 'flowers', name: 'Цветы', icon: <FlowerIcon /> },
    { id: 'sweets', name: 'Сладости', icon: <CakeIcon /> },
    { id: 'drinks', name: 'Напитки', icon: <CoffeeIcon /> },
    { id: 'jewelry', name: 'Украшения', icon: <DiamondIcon /> },
    { id: 'romantic', name: 'Романтика', icon: <FavoriteIcon /> }
  ];

  // Загрузка подарков при монтировании компонента
  useEffect(() => {
    loadGifts();
    loadRecentMatches();
  }, []);

  const loadGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения подарков
      // const response = await getGifts();
      
      // Мок данные
      const mockGifts: Gift[] = [
        {
          id: 'gift1',
          name: 'Букет роз',
          description: 'Красивый букет красных роз',
          image: '/gifts/roses.jpg',
          price: 500,
          category: 'flowers',
          isPopular: true,
          isNew: false
        },
        {
          id: 'gift2',
          name: 'Коробка конфет',
          description: 'Элитные шоколадные конфеты',
          image: '/gifts/chocolates.jpg',
          price: 300,
          category: 'sweets',
          isPopular: false,
          isNew: true
        },
        {
          id: 'gift3',
          name: 'Кофе премиум',
          description: 'Ароматный кофе высшего качества',
          image: '/gifts/coffee.jpg',
          price: 200,
          category: 'drinks',
          isPopular: true,
          isNew: false
        },
        {
          id: 'gift4',
          name: 'Серебряное кольцо',
          description: 'Элегантное серебряное кольцо',
          image: '/gifts/ring.jpg',
          price: 1500,
          category: 'jewelry',
          isPopular: false,
          isNew: true
        },
        {
          id: 'gift5',
          name: 'Романтический ужин',
          description: 'Сертификат на романтический ужин',
          image: '/gifts/dinner.jpg',
          price: 2000,
          category: 'romantic',
          isPopular: true,
          isNew: false
        }
      ];

      setGifts(mockGifts);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке подарков');
    } finally {
      setLoading(false);
    }
  };

  const loadRecentMatches = async () => {
    try {
      // Здесь будет вызов API для получения недавних совпадений
      // const response = await getRecentMatches();
      
      // Мок данные
      const mockMatches = [
        { id: 'user1', name: 'Анна Петрова', avatar: '/avatars/anna.jpg' },
        { id: 'user2', name: 'Михаил Иванов', avatar: '/avatars/mikhail.jpg' },
        { id: 'user3', name: 'Елена Сидорова', avatar: '/avatars/elena.jpg' }
      ];

      setRecentMatches(mockMatches);
    } catch (err) {
      console.error('Ошибка при загрузке совпадений:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleGiftSelect = (gift: Gift) => {
    setSelectedGift(gift);
    setSendDialogOpen(true);
  };

  const handleSendGift = async (data: SendGiftForm) => {
    if (!selectedGift) return;

    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отправки подарка
      // await sendGift({
      //   giftId: selectedGift.id,
      //   recipientId: data.recipientId,
      //   message: data.message,
      //   isAnonymous: data.isAnonymous
      // });

      setSuccess(`Подарок "${selectedGift.name}" успешно отправлен!`);
      setSendDialogOpen(false);
      setSelectedGift(null);
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке подарка');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredGifts = () => {
    if (activeTab === 0) return gifts; // Все подарки
    const selectedCategory = categories[activeTab];
    return gifts.filter(gift => gift.category === selectedCategory.id);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (!user) {
    return (
      <Layout title="Подарки">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Магазин подарков - Likes & Love</title>
        <meta name="description" content="Отправляйте виртуальные подарки в приложении знакомств Likes & Love" />
        <meta name="keywords" content="подарки, виртуальные подарки, знакомства, романтика" />
      </Head>

      <Layout title="Подарки">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 2 }}>
                Магазин подарков
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Удивите своих совпадений виртуальными подарками
              </Typography>
            </Box>

            {/* Быстрые действия */}
            <Grid container spacing={2} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={4}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={() => router.push('/gifts/received')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <InboxIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Полученные
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Подарки от других пользователей
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={() => router.push('/gifts/send')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <SendIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Отправить
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Быстрая отправка подарка
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <HistoryIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      История
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Отправленные подарки
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Категории */}
            <Box sx={{ mb: 4 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                {categories.map((category, index) => (
                  <Tab
                    key={category.id}
                    icon={category.icon}
                    label={category.name}
                    iconPosition="start"
                  />
                ))}
              </Tabs>
            </Box>

            {/* Сетка подарков */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {getFilteredGifts().map((gift) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={gift.id}>
                    <Card 
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        position: 'relative',
                        '&:hover': {
                          transform: 'scale(1.02)',
                          transition: 'transform 0.2s',
                          boxShadow: theme.shadows[8]
                        }
                      }}
                      onClick={() => handleGiftSelect(gift)}
                    >
                      <CardMedia
                        component="img"
                        height="200"
                        image={gift.image}
                        alt={gift.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      
                      {/* Бейджи */}
                      <Box sx={{ position: 'absolute', top: 8, left: 8, display: 'flex', gap: 1 }}>
                        {gift.isNew && (
                          <Chip label="Новинка" size="small" color="success" />
                        )}
                        {gift.isPopular && (
                          <Chip label="Популярное" size="small" color="warning" />
                        )}
                      </Box>

                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {gift.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {gift.description}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="h6" color="primary" fontWeight="bold">
                            {formatPrice(gift.price)}
                          </Typography>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<SendIcon />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleGiftSelect(gift);
                            }}
                          >
                            Отправить
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Пустое состояние */}
            {!loading && getFilteredGifts().length === 0 && (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <GiftIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                  Подарки не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  В этой категории пока нет подарков
                </Typography>
              </Box>
            )}

            {/* Диалог отправки подарка */}
            <Dialog
              open={sendDialogOpen}
              onClose={() => setSendDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Отправить подарок: {selectedGift?.name}
              </DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 2 }}>
                  <form onSubmit={handleSubmit(handleSendGift)}>
                    <Controller
                      name="recipientId"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth sx={{ mb: 2 }}>
                          <InputLabel>Получатель</InputLabel>
                          <Select
                            {...field}
                            label="Получатель"
                            error={!!errors.recipientId}
                          >
                            {recentMatches.map((match) => (
                              <MenuItem key={match.id} value={match.id}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <img 
                                    src={match.avatar} 
                                    alt={match.name}
                                    style={{ width: 24, height: 24, borderRadius: '50%' }}
                                  />
                                  {match.name}
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                          {errors.recipientId && (
                            <Typography variant="caption" color="error">
                              {errors.recipientId.message}
                            </Typography>
                          )}
                        </FormControl>
                      )}
                    />

                    <Controller
                      name="message"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Сообщение (необязательно)"
                          multiline
                          rows={3}
                          fullWidth
                          error={!!errors.message}
                          helperText={errors.message?.message || `${field.value?.length || 0}/200`}
                          sx={{ mb: 2 }}
                        />
                      )}
                    />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6" color="primary">
                        Стоимость: {selectedGift && formatPrice(selectedGift.price)}
                      </Typography>
                    </Box>
                  </form>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setSendDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleSubmit(handleSendGift)}
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                >
                  {loading ? 'Отправка...' : 'Отправить подарок'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default GiftsPage;
