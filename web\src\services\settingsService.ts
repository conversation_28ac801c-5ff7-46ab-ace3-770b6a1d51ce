import axios from 'axios';
import {
  UserSettings,
  SecurityEvent,
  PrivacyReport,
  TrustedDevice,
  LoginSession,
  DataExportRequest,
  AccountDeleteRequest,
  UpdatePrivacySettingsRequest,
  UpdateNotificationSettingsRequest,
  UpdatePreferencesRequest,
  UpdateSecuritySettingsRequest,
  Enable2FARequest,
  Verify2FARequest,
  ChangePasswordRequest,
  ChangeEmailRequest,
  ChangePhoneRequest,
  DeactivateAccountRequest,
  DeleteAccountRequest,
  BlockUserRequest,
  ReportUserRequest,
  ExportDataRequest,
  PaginatedResponse
} from '../types/settings.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// Settings Management
export const getUserSettings = async (): Promise<UserSettings> => {
  try {
    const response = await api.get('/settings');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки настроек');
  }
};

export const updatePrivacySettings = async (request: UpdatePrivacySettingsRequest): Promise<UserSettings> => {
  try {
    const response = await api.put('/settings/privacy', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления настроек приватности');
  }
};

export const updateNotificationSettings = async (request: UpdateNotificationSettingsRequest): Promise<UserSettings> => {
  try {
    const response = await api.put('/settings/notifications', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления настроек уведомлений');
  }
};

export const updatePreferences = async (request: UpdatePreferencesRequest): Promise<UserSettings> => {
  try {
    const response = await api.put('/settings/preferences', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления предпочтений');
  }
};

export const updateSecuritySettings = async (request: UpdateSecuritySettingsRequest): Promise<UserSettings> => {
  try {
    const response = await api.put('/settings/security', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка обновления настроек безопасности');
  }
};

// Two-Factor Authentication
export const enable2FA = async (request: Enable2FARequest): Promise<{ qrCode?: string; backupCodes: string[] }> => {
  try {
    const response = await api.post('/settings/security/2fa/enable', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка включения двухфакторной аутентификации');
  }
};

export const verify2FA = async (request: Verify2FARequest): Promise<void> => {
  try {
    await api.post('/settings/security/2fa/verify', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка верификации двухфакторной аутентификации');
  }
};

export const disable2FA = async (password: string): Promise<void> => {
  try {
    await api.post('/settings/security/2fa/disable', { password });
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка отключения двухфакторной аутентификации');
  }
};

export const regenerate2FABackupCodes = async (): Promise<string[]> => {
  try {
    const response = await api.post('/settings/security/2fa/regenerate-codes');
    return response.data.backupCodes;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка генерации резервных кодов');
  }
};

// Password and Account Security
export const changePassword = async (request: ChangePasswordRequest): Promise<void> => {
  try {
    await api.post('/settings/security/change-password', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка смены пароля');
  }
};

export const changeEmail = async (request: ChangeEmailRequest): Promise<void> => {
  try {
    await api.post('/settings/account/change-email', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка смены email');
  }
};

export const changePhone = async (request: ChangePhoneRequest): Promise<void> => {
  try {
    await api.post('/settings/account/change-phone', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка смены номера телефона');
  }
};

export const verifyEmailChange = async (token: string): Promise<void> => {
  try {
    await api.post('/settings/account/verify-email-change', { token });
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка подтверждения смены email');
  }
};

export const verifyPhoneChange = async (code: string): Promise<void> => {
  try {
    await api.post('/settings/account/verify-phone-change', { code });
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка подтверждения смены номера телефона');
  }
};

// Device and Session Management
export const getTrustedDevices = async (): Promise<TrustedDevice[]> => {
  try {
    const response = await api.get('/settings/security/trusted-devices');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки доверенных устройств');
  }
};

export const addTrustedDevice = async (deviceName: string): Promise<TrustedDevice> => {
  try {
    const response = await api.post('/settings/security/trusted-devices', { deviceName });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка добавления доверенного устройства');
  }
};

export const removeTrustedDevice = async (deviceId: string): Promise<void> => {
  try {
    await api.delete(`/settings/security/trusted-devices/${deviceId}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка удаления доверенного устройства');
  }
};

export const getLoginSessions = async (): Promise<LoginSession[]> => {
  try {
    const response = await api.get('/settings/security/sessions');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки активных сессий');
  }
};

export const terminateSession = async (sessionId: string): Promise<void> => {
  try {
    await api.delete(`/settings/security/sessions/${sessionId}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка завершения сессии');
  }
};

export const terminateAllSessions = async (): Promise<void> => {
  try {
    await api.delete('/settings/security/sessions');
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка завершения всех сессий');
  }
};

// Security Events
export const getSecurityEvents = async (
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<SecurityEvent>> => {
  try {
    const response = await api.get('/settings/security/events', {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки событий безопасности');
  }
};

export const acknowledgeSecurityEvent = async (eventId: string): Promise<void> => {
  try {
    await api.post(`/settings/security/events/${eventId}/acknowledge`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка подтверждения события безопасности');
  }
};

export const clearSecurityEvents = async (): Promise<void> => {
  try {
    await api.delete('/settings/security/events');
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка очистки событий безопасности');
  }
};

// User Blocking and Reporting
export const blockUser = async (request: BlockUserRequest): Promise<void> => {
  try {
    await api.post('/settings/privacy/block-user', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка блокировки пользователя');
  }
};

export const unblockUser = async (userId: string): Promise<void> => {
  try {
    await api.delete(`/settings/privacy/block-user/${userId}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка разблокировки пользователя');
  }
};

export const getBlockedUsers = async (): Promise<string[]> => {
  try {
    const response = await api.get('/settings/privacy/blocked-users');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки заблокированных пользователей');
  }
};

export const reportUser = async (request: ReportUserRequest): Promise<void> => {
  try {
    await api.post('/settings/privacy/report-user', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка отправки жалобы');
  }
};

// Data Export and Account Management
export const exportData = async (request: ExportDataRequest): Promise<DataExportRequest> => {
  try {
    const response = await api.post('/settings/account/export-data', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка запроса экспорта данных');
  }
};

export const getDataExportRequests = async (): Promise<DataExportRequest[]> => {
  try {
    const response = await api.get('/settings/account/export-requests');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки запросов экспорта');
  }
};

export const downloadDataExport = async (requestId: string): Promise<Blob> => {
  try {
    const response = await api.get(`/settings/account/export-requests/${requestId}/download`, {
      responseType: 'blob'
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка скачивания экспорта данных');
  }
};

export const deactivateAccount = async (request: DeactivateAccountRequest): Promise<void> => {
  try {
    await api.post('/settings/account/deactivate', request);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка деактивации аккаунта');
  }
};

export const reactivateAccount = async (password: string): Promise<void> => {
  try {
    await api.post('/settings/account/reactivate', { password });
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка реактивации аккаунта');
  }
};

export const deleteAccount = async (request: DeleteAccountRequest): Promise<AccountDeleteRequest> => {
  try {
    const response = await api.post('/settings/account/delete', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка удаления аккаунта');
  }
};

export const cancelAccountDeletion = async (): Promise<void> => {
  try {
    await api.post('/settings/account/cancel-deletion');
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка отмены удаления аккаунта');
  }
};

export const getAccountDeleteRequests = async (): Promise<AccountDeleteRequest[]> => {
  try {
    const response = await api.get('/settings/account/delete-requests');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки запросов удаления');
  }
};

// Privacy Report
export const getPrivacyReport = async (): Promise<PrivacyReport> => {
  try {
    const response = await api.get('/settings/privacy/report');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Ошибка загрузки отчета о конфиденциальности');
  }
};

// Helper functions
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Пароль должен содержать минимум 8 символов');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Пароль должен содержать заглавные буквы');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Пароль должен содержать строчные буквы');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Пароль должен содержать цифры');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Пароль должен содержать специальные символы');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const formatDeviceInfo = (device: TrustedDevice): string => {
  return `${device.name} (${device.os}, ${device.browser})`;
};

export const formatLastSeen = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return 'Сегодня';
  } else if (diffDays === 1) {
    return 'Вчера';
  } else if (diffDays < 7) {
    return `${diffDays} дней назад`;
  } else {
    return date.toLocaleDateString('ru-RU');
  }
};

export const getSecurityEventSeverityColor = (severity: SecurityEvent['severity']): string => {
  switch (severity) {
    case 'low':
      return '#4caf50';
    case 'medium':
      return '#ff9800';
    case 'high':
      return '#f44336';
    case 'critical':
      return '#d32f2f';
    default:
      return '#757575';
  }
};

export const isPasswordStrong = (password: string): boolean => {
  return validatePassword(password).isValid;
};
