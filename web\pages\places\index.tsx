import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  Rating,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Place as PlaceIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Map as MapIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Wifi as WifiIcon,
  LocalParking as ParkingIcon,
  Accessible as AccessibleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getPlaces,
  getPlaceCategories,
  getNearbyPlaces,
  getPopularPlaces,
  addToFavorites,
  removeFromFavorites,
  getUserLocation
} from '../../src/services/placesService';
import { 
  Place,
  PlaceCategory,
  PlaceFilters 
} from '../../src/types/places.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`places-tabpanel-${index}`}
      aria-labelledby={`places-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const PlacesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [places, setPlaces] = useState<Place[]>([]);
  const [nearbyPlaces, setNearbyPlaces] = useState<Place[]>([]);
  const [popularPlaces, setPopularPlaces] = useState<Place[]>([]);
  const [categories, setCategories] = useState<PlaceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);

  const tabs = [
    { label: 'Все места', key: 'all' },
    { label: 'Рядом', key: 'nearby' },
    { label: 'Популярные', key: 'popular' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  useEffect(() => {
    loadTabData();
  }, [activeTab, userLocation]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load categories first
      const categoriesData = await getPlaceCategories();
      setCategories(categoriesData);
      
      // Try to get user location
      try {
        const location = await getUserLocation();
        setUserLocation(location);
      } catch (err) {
        console.warn('Could not get user location:', err);
      }
      
      // Load initial places data
      await loadTabData();
    } catch (err: any) {
      setError('Ошибка загрузки мест');
    } finally {
      setLoading(false);
    }
  };

  const loadTabData = async () => {
    try {
      setError(null);
      
      switch (activeTab) {
        case 0: // All places
          const allPlaces = await getPlaces();
          setPlaces(allPlaces.data);
          break;
        case 1: // Nearby places
          if (userLocation) {
            const nearby = await getNearbyPlaces(userLocation.latitude, userLocation.longitude);
            setNearbyPlaces(nearby);
          }
          break;
        case 2: // Popular places
          const popular = await getPopularPlaces();
          setPopularPlaces(popular);
          break;
      }
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, place: Place) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedPlace(place);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPlace(null);
  };

  const handleToggleFavorite = async (place: Place) => {
    if (!place) return;
    
    try {
      setActionLoading(place.id);
      setError(null);

      if (place.userInteraction?.isFavorite) {
        await removeFromFavorites(place.id);
        setSuccess('Место удалено из избранного');
      } else {
        await addToFavorites(place.id);
        setSuccess('Место добавлено в избранное');
      }
      
      // Update local state
      const updatePlaces = (places: Place[]) => 
        places.map(p => 
          p.id === place.id 
            ? { 
                ...p, 
                userInteraction: { 
                  ...p.userInteraction, 
                  isFavorite: !p.userInteraction?.isFavorite 
                } 
              }
            : p
        );
      
      setPlaces(updatePlaces);
      setNearbyPlaces(updatePlaces);
      setPopularPlaces(updatePlaces);
      
    } catch (err: any) {
      setError('Ошибка обновления избранного');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatDistance = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    return `${distance.value.toFixed(1)} ${distance.unit === 'km' ? 'км' : 'миль'}`;
  };

  const getCurrentPlaces = () => {
    switch (activeTab) {
      case 0:
        return places;
      case 1:
        return nearbyPlaces;
      case 2:
        return popularPlaces;
      default:
        return [];
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Места для встреч - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите лучшие места для встреч и свиданий в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/places" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Typography variant={isMobile ? "h5" : "h4"}>
                <PlaceIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Места для встреч
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/places/map')}>
                  <MapIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/places?filters=true')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/places/search')}>
                  <SearchIcon />
                </IconButton>
                <IconButton onClick={loadTabData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/places/create')}
                  size={isMobile ? "small" : "medium"}
                >
                  Добавить место
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Location Alert */}
            {activeTab === 1 && !userLocation && (
              <Alert severity="info" sx={{ mb: 3 }}>
                Разрешите доступ к геолокации, чтобы видеть места рядом с вами
              </Alert>
            )}

            {/* Tabs */}
            <Paper elevation={2} sx={{ mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
              >
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </Paper>

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка мест...
                </Typography>
              </Box>
            ) : getCurrentPlaces().length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <PlaceIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {activeTab === 1 ? 'Места рядом не найдены' : 'Места не найдены'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {activeTab === 1 
                    ? 'Попробуйте увеличить радиус поиска или разрешите доступ к геолокации'
                    : 'Попробуйте изменить фильтры или добавьте новое место'
                  }
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/places/create')}
                >
                  Добавить место
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {getCurrentPlaces().map((place) => (
                    <Grid item xs={12} sm={6} md={4} key={place.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/places/${place.id}`)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={place.photos[0]?.url || '/placeholder-place.jpg'}
                            alt={place.name}
                            sx={{ objectFit: 'cover' }}
                          />
                          
                          {/* Favorite Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleFavorite(place);
                              }}
                              disabled={actionLoading === place.id}
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.9)',
                                '&:hover': { backgroundColor: 'rgba(255,255,255,1)' }
                              }}
                            >
                              {place.userInteraction?.isFavorite ? (
                                <FavoriteIcon color="error" />
                              ) : (
                                <FavoriteBorderIcon />
                              )}
                            </IconButton>
                          </Box>

                          {/* Menu Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, place)}
                              sx={{ 
                                backgroundColor: 'rgba(0,0,0,0.5)', 
                                color: 'white',
                                '&:hover': { backgroundColor: 'rgba(0,0,0,0.7)' }
                              }}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>

                          {/* Verification Badge */}
                          {place.verification.isVerified && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<VerifiedIcon />}
                                label="Проверено"
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}

                          {/* Distance Badge */}
                          {place.distance && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              right: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                label={formatDistance(place.distance)}
                                size="small"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}
                        </Box>

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" component="h3" gutterBottom>
                            {place.name}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Rating
                              value={place.rating.average}
                              precision={0.1}
                              size="small"
                              readOnly
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              {place.rating.average.toFixed(1)} ({place.rating.count})
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {place.location.district || place.location.city}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <MoneyIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatPriceRange(place.priceRange)}
                            </Typography>
                          </Box>

                          {place.shortDescription && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {place.shortDescription.length > 100 
                                ? `${place.shortDescription.substring(0, 100)}...`
                                : place.shortDescription
                              }
                            </Typography>
                          )}

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                            <Chip
                              label={place.category.name}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            {place.hasWifi && (
                              <Chip
                                icon={<WifiIcon />}
                                label="Wi-Fi"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.hasParking && (
                              <Chip
                                icon={<ParkingIcon />}
                                label="Парковка"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.isAccessible && (
                              <Chip
                                icon={<AccessibleIcon />}
                                label="Доступно"
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {place.tags.slice(0, 2).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {place.tags.length > 2 && (
                              <Chip
                                label={`+${place.tags.length - 2}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              {place.workingHours.isAlwaysOpen ? '24/7' : 'Работает'}
                            </Typography>
                          </Box>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/places/${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Подробности
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              handleToggleFavorite(selectedPlace);
            }
          }}>
            {selectedPlace?.userInteraction?.isFavorite ? 'Удалить из избранного' : 'Добавить в избранное'}
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/meetings/create?placeId=${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Назначить встречу здесь
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              // Share place logic
            }
            handleMenuClose();
          }}>
            Поделиться
          </MenuItem>
          
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            Пожаловаться
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default PlacesPage;
