import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon,
  CreditCard as CreditCardIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Help as HelpIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Support as SupportIcon,
  Payment as PaymentIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';

interface ErrorReason {
  code: string;
  title: string;
  description: string;
  solution: string;
  icon: React.ReactNode;
}

const PaymentFailedPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  
  const { 
    error, 
    errorCode, 
    transactionId, 
    planId, 
    amount, 
    currency = 'RUB' 
  } = router.query;

  const [expandedFaq, setExpandedFaq] = useState<string | false>(false);

  const errorReasons: ErrorReason[] = [
    {
      code: 'insufficient_funds',
      title: 'Недостаточно средств',
      description: 'На вашей карте недостаточно средств для совершения платежа',
      solution: 'Пополните баланс карты или используйте другой способ оплаты',
      icon: <CreditCardIcon color="error" />
    },
    {
      code: 'card_declined',
      title: 'Карта отклонена',
      description: 'Ваш банк отклонил транзакцию',
      solution: 'Обратитесь в банк или попробуйте другую карту',
      icon: <CreditCardIcon color="error" />
    },
    {
      code: 'expired_card',
      title: 'Карта просрочена',
      description: 'Срок действия вашей карты истек',
      solution: 'Используйте действующую карту или обновите данные',
      icon: <WarningIcon color="warning" />
    },
    {
      code: 'invalid_cvc',
      title: 'Неверный CVC код',
      description: 'Введен неправильный код безопасности карты',
      solution: 'Проверьте CVC код на обратной стороне карты',
      icon: <SecurityIcon color="error" />
    },
    {
      code: 'processing_error',
      title: 'Ошибка обработки',
      description: 'Произошла техническая ошибка при обработке платежа',
      solution: 'Попробуйте повторить платеж через несколько минут',
      icon: <ErrorIcon color="error" />
    },
    {
      code: 'network_error',
      title: 'Ошибка сети',
      description: 'Проблемы с интернет-соединением',
      solution: 'Проверьте подключение к интернету и повторите попытку',
      icon: <ErrorIcon color="error" />
    }
  ];

  const faqItems = [
    {
      id: 'retry',
      question: 'Что делать, если платеж не прошел?',
      answer: 'Сначала проверьте данные карты и баланс. Если все в порядке, попробуйте повторить платеж через несколько минут. При повторных ошибках обратитесь в службу поддержки.'
    },
    {
      id: 'refund',
      question: 'Вернутся ли деньги, если платеж не прошел?',
      answer: 'Если платеж не был завершен, деньги не списываются с вашего счета. В случае технических ошибок средства возвращаются автоматически в течение 1-3 рабочих дней.'
    },
    {
      id: 'alternative',
      question: 'Какие есть альтернативные способы оплаты?',
      answer: 'Вы можете использовать другую банковскую карту, PayPal, Apple Pay, Google Pay или банковский перевод. Все способы оплаты доступны в разделе "Способы оплаты".'
    },
    {
      id: 'security',
      question: 'Безопасны ли мои платежные данные?',
      answer: 'Да, все платежные данные защищены 256-битным шифрованием SSL и соответствуют стандартам PCI DSS. Мы не храним данные вашей карты на наших серверах.'
    }
  ];

  const getCurrentError = (): ErrorReason => {
    const foundError = errorReasons.find(reason => reason.code === errorCode);
    return foundError || {
      code: 'unknown',
      title: 'Неизвестная ошибка',
      description: error as string || 'Произошла неизвестная ошибка при обработке платежа',
      solution: 'Попробуйте повторить платеж или обратитесь в службу поддержки',
      icon: <ErrorIcon color="error" />
    };
  };

  const handleRetryPayment = () => {
    router.push('/subscription/plans');
  };

  const handleContactSupport = () => {
    router.push('/help?topic=payment');
  };

  const handleFaqChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedFaq(isExpanded ? panel : false);
  };

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  if (!user) {
    return null;
  }

  const currentError = getCurrentError();

  return (
    <>
      <Head>
        <title>Ошибка оплаты - Likes & Love</title>
        <meta 
          name="description" 
          content="Произошла ошибка при оплате подписки в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Fade in timeout={800}>
              <Box>
                {/* Error Header */}
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <Zoom in timeout={600}>
                    <ErrorIcon 
                      sx={{ 
                        fontSize: 120, 
                        color: 'error.main',
                        mb: 2,
                        filter: 'drop-shadow(0 4px 8px rgba(244, 67, 54, 0.3))'
                      }} 
                    />
                  </Zoom>
                  
                  <Typography variant="h3" gutterBottom fontWeight="bold" color="error.main">
                    Ошибка оплаты
                  </Typography>
                  
                  <Typography variant="h6" gutterBottom>
                    К сожалению, не удалось обработать ваш платеж
                  </Typography>
                  
                  <Typography variant="body1" color="text.secondary">
                    Не волнуйтесь, мы поможем решить эту проблему
                  </Typography>
                </Box>

                {/* Error Details */}
                <Paper elevation={3} sx={{ p: 3, mb: 4, borderLeft: `4px solid ${theme.palette.error.main}` }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 3 }}>
                    {currentError.icon}
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {currentError.title}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" gutterBottom>
                        {currentError.description}
                      </Typography>
                      <Typography variant="body2" color="primary.main" fontWeight="medium">
                        Решение: {currentError.solution}
                      </Typography>
                    </Box>
                  </Box>

                  {transactionId && (
                    <Box>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="body2" color="text.secondary">
                        ID транзакции: {transactionId}
                      </Typography>
                    </Box>
                  )}
                </Paper>

                {/* Quick Actions */}
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    <RefreshIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Что можно сделать
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                        <CardContent sx={{ p: '16px !important' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                            <RefreshIcon color="primary" />
                            <Typography variant="h6">
                              Повторить платеж
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Попробуйте оплатить подписку еще раз
                          </Typography>
                          <Button
                            variant="contained"
                            fullWidth
                            onClick={handleRetryPayment}
                            startIcon={<PaymentIcon />}
                          >
                            Повторить
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Card elevation={1} sx={{ p: 2, height: '100%' }}>
                        <CardContent sx={{ p: '16px !important' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                            <SupportIcon color="primary" />
                            <Typography variant="h6">
                              Связаться с поддержкой
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Получите помощь от нашей команды
                          </Typography>
                          <Button
                            variant="outlined"
                            fullWidth
                            onClick={handleContactSupport}
                            startIcon={<HelpIcon />}
                          >
                            Написать в поддержку
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Contact Information */}
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    <SupportIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Нужна помощь?
                  </Typography>
                  
                  <List>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon>
                        <EmailIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Email поддержка"
                        secondary="<EMAIL> • Ответ в течение 2 часов"
                      />
                    </ListItem>
                    
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon>
                        <PhoneIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Телефон поддержки"
                        secondary="+7 (800) 123-45-67 • Круглосуточно"
                      />
                    </ListItem>
                    
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon>
                        <HelpIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Центр помощи"
                        secondary="Ответы на часто задаваемые вопросы"
                      />
                    </ListItem>
                  </List>
                </Paper>

                {/* FAQ */}
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Часто задаваемые вопросы
                  </Typography>
                  
                  {faqItems.map((item) => (
                    <Accordion
                      key={item.id}
                      expanded={expandedFaq === item.id}
                      onChange={handleFaqChange(item.id)}
                      elevation={0}
                      sx={{ 
                        '&:before': { display: 'none' },
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        mb: 1
                      }}
                    >
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="body1" fontWeight="medium">
                          {item.question}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography variant="body2" color="text.secondary">
                          {item.answer}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </Paper>

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<ArrowBackIcon />}
                    onClick={() => router.back()}
                    sx={{ minWidth: 200 }}
                  >
                    Назад
                  </Button>
                  
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<RefreshIcon />}
                    onClick={handleRetryPayment}
                    sx={{ minWidth: 200 }}
                  >
                    Попробовать снова
                  </Button>
                </Box>

                {/* Security Notice */}
                <Alert severity="info" sx={{ mt: 4 }}>
                  <Typography variant="body2">
                    <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle', fontSize: 'inherit' }} />
                    Ваши платежные данные защищены и не сохраняются на наших серверах. 
                    Все транзакции обрабатываются через защищенные платежные системы.
                  </Typography>
                </Alert>
              </Box>
            </Fade>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PaymentFailedPage;
