import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  LinearProgress,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Verified as VerifiedIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  CheckCircle,
  Schedule,
  Cancel,
  Refresh,
  Phone as PhoneIcon,
  Email as EmailIcon,
  PhotoCamera as PhotoIcon,
  Description as DocumentIcon,
  Share as SocialIcon,
  AccountBalance as GosuslugiIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/contexts/AuthContext';
import { getProfileVerifications } from '../../../src/services/profileService';
import { ProfileVerification } from '../../../src/types/profile.types';

const VerificationStatusPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [verifications, setVerifications] = useState<ProfileVerification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const verificationIcons = {
    phone: <PhoneIcon />,
    email: <EmailIcon />,
    photo: <PhotoIcon />,
    document: <DocumentIcon />,
    social: <SocialIcon />,
    gosuslugi: <GosuslugiIcon />
  };

  const verificationTitles = {
    phone: 'Телефон',
    email: 'Email',
    photo: 'Фото-верификация',
    document: 'Документ',
    social: 'Социальные сети',
    gosuslugi: 'Госуслуги'
  };

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadVerifications();
  }, [user, router]);

  const loadVerifications = async () => {
    try {
      setLoading(true);
      setError(null);
      const verificationsData = await getProfileVerifications();
      setVerifications(verificationsData);
    } catch (err: any) {
      setError('Ошибка загрузки данных верификации');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle color="success" />;
      case 'pending':
        return <Schedule color="warning" />;
      case 'rejected':
        return <ErrorIcon color="error" />;
      case 'expired':
        return <Cancel color="error" />;
      default:
        return <PendingIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
      case 'expired':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'verified':
        return 'Подтверждено';
      case 'pending':
        return 'На проверке';
      case 'rejected':
        return 'Отклонено';
      case 'expired':
        return 'Истекло';
      default:
        return 'Неизвестно';
    }
  };

  const getVerificationProgress = () => {
    const totalTypes = 6; // phone, email, photo, document, social, gosuslugi
    const verifiedCount = verifications.filter(v => v.status === 'verified').length;
    return (verifiedCount / totalTypes) * 100;
  };

  const getVerificationLevel = () => {
    const verifiedCount = verifications.filter(v => v.status === 'verified').length;
    if (verifiedCount >= 5) return { level: 'Максимальный', color: 'success' };
    if (verifiedCount >= 3) return { level: 'Высокий', color: 'primary' };
    if (verifiedCount >= 2) return { level: 'Средний', color: 'warning' };
    return { level: 'Базовый', color: 'error' };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const verificationLevel = getVerificationLevel();

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Статус верификации - Likes & Love</title>
        <meta 
          name="description" 
          content="Просмотрите статус верификации вашего профиля и уровень доверия в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/profile/verification/status" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                <Button
                  startIcon={<ArrowBack />}
                  onClick={() => router.back()}
                  sx={{ mr: 2 }}
                >
                  Назад
                </Button>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Статус верификации
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Детальная информация о подтверждении вашего профиля
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  onClick={loadVerifications}
                  disabled={loading}
                >
                  Обновить
                </Button>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка статуса верификации...
                  </Typography>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Overall status */}
                    <Grid container spacing={3} sx={{ mb: 4 }}>
                      <Grid item xs={12} md={6}>
                        <Card sx={{ p: 3, textAlign: 'center' }}>
                          <VerifiedIcon 
                            sx={{ 
                              fontSize: 60, 
                              color: `${verificationLevel.color}.main`, 
                              mb: 2 
                            }} 
                          />
                          <Typography variant="h5" gutterBottom>
                            Уровень доверия: {verificationLevel.level}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {verifications.filter(v => v.status === 'verified').length} из 6 типов верификации
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={getVerificationProgress()}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              backgroundColor: theme.palette.grey[200],
                              '& .MuiLinearProgress-bar': {
                                borderRadius: 4,
                                backgroundColor: theme.palette[verificationLevel.color as keyof typeof theme.palette].main
                              }
                            }}
                          />
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                            {Math.round(getVerificationProgress())}% завершено
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Card sx={{ p: 3 }}>
                          <Typography variant="h6" gutterBottom>
                            Преимущества верификации
                          </Typography>
                          <Box component="ul" sx={{ m: 0, pl: 2 }}>
                            <li>
                              <Typography variant="body2" color="text.secondary">
                                Повышение доверия других пользователей
                              </Typography>
                            </li>
                            <li>
                              <Typography variant="body2" color="text.secondary">
                                Приоритет в результатах поиска
                              </Typography>
                            </li>
                            <li>
                              <Typography variant="body2" color="text.secondary">
                                Защита от блокировки профиля
                              </Typography>
                            </li>
                            <li>
                              <Typography variant="body2" color="text.secondary">
                                Доступ к дополнительным функциям
                              </Typography>
                            </li>
                          </Box>
                        </Card>
                      </Grid>
                    </Grid>

                    {/* Verification timeline */}
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="h6" gutterBottom>
                        История верификации
                      </Typography>
                      
                      {verifications.length === 0 ? (
                        <Alert severity="info">
                          У вас пока нет запросов на верификацию. 
                          <Button 
                            variant="text" 
                            onClick={() => router.push('/profile/verification')}
                            sx={{ ml: 1 }}
                          >
                            Начать верификацию
                          </Button>
                        </Alert>
                      ) : (
                        <Timeline>
                          {verifications
                            .sort((a, b) => new Date(b.verifiedAt || '').getTime() - new Date(a.verifiedAt || '').getTime())
                            .map((verification, index) => (
                            <TimelineItem key={verification.id}>
                              <TimelineSeparator>
                                <TimelineDot color={getStatusColor(verification.status) as any}>
                                  {getStatusIcon(verification.status)}
                                </TimelineDot>
                                {index < verifications.length - 1 && <TimelineConnector />}
                              </TimelineSeparator>
                              <TimelineContent>
                                <Card sx={{ p: 2, mb: 2 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    {verificationIcons[verification.type]}
                                    <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
                                      {verificationTitles[verification.type]}
                                    </Typography>
                                    <Chip
                                      label={getStatusText(verification.status)}
                                      color={getStatusColor(verification.status) as any}
                                      size="small"
                                    />
                                  </Box>
                                  
                                  {verification.verifiedAt && (
                                    <Typography variant="body2" color="text.secondary">
                                      Подтверждено: {formatDate(verification.verifiedAt)}
                                    </Typography>
                                  )}
                                  
                                  {verification.expiresAt && verification.status === 'verified' && (
                                    <Typography variant="body2" color="text.secondary">
                                      Действительно до: {formatDate(verification.expiresAt)}
                                    </Typography>
                                  )}
                                  
                                  {verification.rejectionReason && (
                                    <Alert severity="error" sx={{ mt: 1 }}>
                                      <Typography variant="body2">
                                        Причина отклонения: {verification.rejectionReason}
                                      </Typography>
                                    </Alert>
                                  )}

                                  {verification.status === 'pending' && (
                                    <Alert severity="info" sx={{ mt: 1 }}>
                                      <Typography variant="body2">
                                        Ваш запрос находится на рассмотрении. Обычно проверка занимает 1-3 рабочих дня.
                                      </Typography>
                                    </Alert>
                                  )}

                                  {verification.status === 'expired' && (
                                    <Alert severity="warning" sx={{ mt: 1 }}>
                                      <Typography variant="body2">
                                        Срок действия верификации истек. Необходимо пройти повторную проверку.
                                      </Typography>
                                    </Alert>
                                  )}
                                </Card>
                              </TimelineContent>
                            </TimelineItem>
                          ))}
                        </Timeline>
                      )}
                    </Box>

                    {/* Action buttons */}
                    <Box sx={{ textAlign: 'center', mt: 4 }}>
                      <Button
                        variant="contained"
                        onClick={() => router.push('/profile/verification')}
                        sx={{
                          mr: 2,
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                        }}
                      >
                        Добавить верификацию
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={() => router.push('/profile')}
                      >
                        Вернуться к профилю
                      </Button>
                    </Box>

                    {/* Tips */}
                    <Alert severity="info" sx={{ mt: 4 }}>
                      <Typography variant="body2">
                        💡 <strong>Совет:</strong> Для максимального уровня доверия рекомендуем пройти верификацию 
                        через Госуслуги и загрузить фото документа. Это значительно повысит ваши шансы на успешные знакомства.
                      </Typography>
                    </Alert>
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default VerificationStatusPage;
