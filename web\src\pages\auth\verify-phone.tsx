import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  Typography, 
  Box, 
  CircularProgress, 
  Al<PERSON>, 
  <PERSON><PERSON>,
  TextField,
  Paper
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuthContext } from '@/contexts/AuthContext';
import { verifyPhone } from '@/services/authService';
import Layout from '@/components/Layout/Layout';
import { VerificationResult } from '@/types/auth.types';

interface VerifyPhoneFormData {
  code: string;
}

const schema = yup.object({
  code: yup.string()
    .required('Код подтверждения обязателен')
    .matches(/^\d{6}$/, 'Код должен состоять из 6 цифр')
});

export default function VerifyPhone() {
  const router = useRouter();
  const { phoneNumber } = router.query;
  const { user, updateUser } = useAuthContext();
  const [verificationStatus, setVerificationStatus] = useState<VerificationResult>({
    loading: false,
    success: false,
    message: '',
  });

  const { control, handleSubmit, formState: { errors } } = useForm<VerifyPhoneFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      code: ''
    }
  });

  const onSubmit = async (data: VerifyPhoneFormData) => {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      setVerificationStatus({
        loading: false,
        success: false,
        message: 'Номер телефона не указан'
      });
      return;
    }

    setVerificationStatus({
      loading: true,
      success: false,
      message: ''
    });

    try {
      const result = await verifyPhone(data.code, phoneNumber);
      setVerificationStatus({
        loading: false,
        success: true,
        message: 'Ваш номер телефона успешно подтвержден!'
      });
      
      if (user) {
        updateUser({
          ...user,
          phoneVerified: true
        });
      }
    } catch (error: any) {
      setVerificationStatus({
        loading: false,
        success: false,
        message: error.message || 'Ошибка при подтверждении номера телефона'
      });
    }
  };

  const handleContinue = () => {
    if (user) {
      router.push('/profile');
    } else {
      router.push('/auth/login');
    }
  };

  return (
    <Layout>
      <Head>
        <title>Подтверждение телефона | Likes & Love</title>
        <meta name="description" content="Подтверждение номера телефона в приложении Likes & Love" />
      </Head>
      
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: '60vh',
          padding: 3
        }}
      >
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            width: '100%', 
            maxWidth: 500,
            borderRadius: 2
          }}
        >
          <Typography variant="h5" component="h1" gutterBottom align="center">
            Подтверждение номера телефона
          </Typography>
          
          {phoneNumber && (
            <Typography variant="body2" sx={{ mb: 3, textAlign: 'center' }}>
              Мы отправили код подтверждения на номер {phoneNumber}
            </Typography>
          )}
          
          {verificationStatus.message && (
            <Alert 
              severity={verificationStatus.success ? "success" : "error"}
              sx={{ mb: 3 }}
            >
              {verificationStatus.message}
            </Alert>
          )}
          
          {!verificationStatus.success ? (
            <form onSubmit={handleSubmit(onSubmit)}>
              <Controller
                name="code"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Код подтверждения"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                    error={!!errors.code}
                    helperText={errors.code?.message}
                    inputProps={{ maxLength: 6 }}
                  />
                )}
              />
              
              <Button 
                type="submit"
                variant="contained" 
                color="primary"
                fullWidth
                size="large"
                sx={{ mt: 2 }}
                disabled={verificationStatus.loading}
              >
                {verificationStatus.loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Подтвердить'
                )}
              </Button>
            </form>
          ) : (
            <Button 
              variant="contained" 
              color="primary"
              fullWidth
              size="large"
              onClick={handleContinue}
            >
              Продолжить
            </Button>
          )}
        </Paper>
      </Box>
    </Layout>
  );
}