import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from '@mui/material';
import {
  ArrowBack,
  ExpandMore as ExpandMoreIcon,
  Gavel as GavelIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';

// Типы для разделов условий
interface TermsSection {
  id: string;
  title: string;
  content: string[];
  subsections?: {
    title: string;
    content: string[];
  }[];
  important?: boolean;
}

const TermsOfServicePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [expandedSection, setExpandedSection] = useState<string | false>('acceptance');

  const lastUpdated = '15 декабря 2024 г.';
  const effectiveDate = '1 января 2025 г.';

  // Разделы условий использования
  const termsSections: TermsSection[] = [
    {
      id: 'acceptance',
      title: '1. Принятие условий',
      important: true,
      content: [
        'Добро пожаловать в приложение знакомств "Likes & Love" (далее - "Сервис", "Приложение", "мы").',
        'Используя наш Сервис, вы соглашаетесь соблюдать настоящие Условия использования (далее - "Условия").',
        'Если вы не согласны с любой частью данных Условий, пожалуйста, не используйте наш Сервис.',
        'Мы оставляем за собой право изменять данные Условия в любое время. Об изменениях мы уведомим вас через приложение или по электронной почте.'
      ]
    },
    {
      id: 'eligibility',
      title: '2. Право на использование',
      important: true,
      content: [
        'Для использования Сервиса вы должны:',
        '• Быть не младше 18 лет',
        '• Иметь право заключать юридически обязывающие соглашения',
        '• Не быть запрещенным лицом согласно применимому законодательству',
        '• Соблюдать все местные, государственные и международные законы'
      ]
    },
    {
      id: 'account',
      title: '3. Учетная запись пользователя',
      content: [
        'При создании учетной записи вы обязуетесь предоставить точную и полную информацию.',
        'Вы несете ответственность за сохранение конфиденциальности своей учетной записи.',
        'Вы обязуетесь немедленно уведомить нас о любом несанкционированном использовании вашей учетной записи.',
        'Мы не несем ответственности за любые потери, возникшие в результате несанкционированного использования вашей учетной записи.'
      ],
      subsections: [
        {
          title: 'Требования к профилю',
          content: [
            'Используйте только свои настоящие фотографии',
            'Предоставляйте правдивую информацию о себе',
            'Не создавайте поддельные или дублирующие профили',
            'Не выдавайте себя за другого человека'
          ]
        }
      ]
    },
    {
      id: 'conduct',
      title: '4. Правила поведения',
      important: true,
      content: [
        'При использовании Сервиса вы соглашаетесь НЕ:',
        '• Домогаться, запугивать или угрожать другим пользователям',
        '• Публиковать неприемлемый, оскорбительный или незаконный контент',
        '• Спамить или отправлять нежелательные сообщения',
        '• Использовать Сервис в коммерческих целях без разрешения',
        '• Нарушать права интеллектуальной собственности',
        '• Пытаться получить несанкционированный доступ к системам'
      ]
    },
    {
      id: 'content',
      title: '5. Пользовательский контент',
      content: [
        'Вы сохраняете права на контент, который публикуете в Сервисе.',
        'Публикуя контент, вы предоставляете нам лицензию на его использование в рамках предоставления Сервиса.',
        'Вы гарантируете, что имеете право публиковать данный контент.',
        'Мы оставляем за собой право удалить любой контент, нарушающий данные Условия.'
      ],
      subsections: [
        {
          title: 'Запрещенный контент',
          content: [
            'Контент сексуального характера или нагота',
            'Контент, пропагандирующий насилие или ненависть',
            'Спам или мошеннический контент',
            'Контент, нарушающий авторские права',
            'Личная информация третьих лиц'
          ]
        }
      ]
    },
    {
      id: 'payments',
      title: '6. Платежи и подписки',
      content: [
        'Некоторые функции Сервиса могут требовать оплаты.',
        'Все платежи обрабатываются через безопасные платежные системы.',
        'Подписки продлеваются автоматически, если не отменены заранее.',
        'Возврат средств осуществляется в соответствии с нашей Политикой возврата.'
      ],
      subsections: [
        {
          title: 'Отмена подписки',
          content: [
            'Вы можете отменить подписку в любое время в настройках аккаунта',
            'Отмена вступает в силу в конце текущего платежного периода',
            'Доступ к премиум-функциям сохраняется до окончания оплаченного периода'
          ]
        }
      ]
    },
    {
      id: 'privacy',
      title: '7. Конфиденциальность',
      content: [
        'Ваша конфиденциальность важна для нас.',
        'Обработка персональных данных регулируется нашей Политикой конфиденциальности.',
        'Мы используем ваши данные только для предоставления и улучшения Сервиса.',
        'Мы не продаем ваши персональные данные третьим лицам.'
      ]
    },
    {
      id: 'termination',
      title: '8. Прекращение использования',
      content: [
        'Вы можете прекратить использование Сервиса в любое время, удалив свою учетную запись.',
        'Мы можем приостановить или прекратить ваш доступ при нарушении данных Условий.',
        'При прекращении использования некоторые данные могут быть сохранены в соответствии с законодательством.',
        'Положения, которые по своей природе должны сохраняться, остаются в силе после прекращения.'
      ]
    },
    {
      id: 'liability',
      title: '9. Ограничение ответственности',
      important: true,
      content: [
        'Сервис предоставляется "как есть" без каких-либо гарантий.',
        'Мы не гарантируем, что Сервис будет работать без перебоев или ошибок.',
        'Мы не несем ответственности за действия других пользователей.',
        'Наша ответственность ограничена максимальной суммой, разрешенной законом.'
      ]
    },
    {
      id: 'governing_law',
      title: '10. Применимое право',
      content: [
        'Данные Условия регулируются законодательством Российской Федерации.',
        'Все споры подлежат рассмотрению в судах Российской Федерации.',
        'Если какое-либо положение признается недействительным, остальные положения остаются в силе.'
      ]
    },
    {
      id: 'contact',
      title: '11. Контактная информация',
      content: [
        'По вопросам, связанным с данными Условиями, обращайтесь:',
        '• Email: <EMAIL>',
        '• Телефон: +7 (800) 555-0123',
        '• Почтовый адрес: 123456, г. Москва, ул. Примерная, д. 1',
        '• Через форму обратной связи в приложении'
      ]
    }
  ];

  const handleSectionExpand = (sectionId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? sectionId : false);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Здесь будет логика скачивания PDF версии
    console.log('Скачивание PDF версии условий использования');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <Head>
        <title>Условия использования - Likes & Love</title>
        <meta name="description" content="Условия использования приложения знакомств Likes & Love" />
        <meta name="keywords" content="условия использования, пользовательское соглашение, правила, Likes & Love" />
      </Head>

      <Layout title="Условия использования">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Условия использования
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Пользовательское соглашение Likes & Love
              </Typography>
            </Box>

            {/* Информация о документе */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Последнее обновление: {lastUpdated}
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                      Дата вступления в силу: {effectiveDate}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PrintIcon />}
                      onClick={handlePrint}
                    >
                      Печать
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={handleDownload}
                    >
                      PDF
                    </Button>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Внимательно ознакомьтесь с условиями использования перед началом работы с приложением. 
                    Использование сервиса означает ваше согласие с данными условиями.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>

            {/* Разделы условий */}
            {termsSections.map((section) => (
              <Accordion
                key={section.id}
                expanded={expandedSection === section.id}
                onChange={handleSectionExpand(section.id)}
                sx={{ mb: 2 }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`${section.id}-content`}
                  id={`${section.id}-header`}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <GavelIcon color="primary" />
                    <Typography variant="h6" fontWeight="bold" sx={{ flexGrow: 1 }}>
                      {section.title}
                    </Typography>
                    {section.important && (
                      <Chip 
                        label="Важно" 
                        color="warning" 
                        size="small"
                        icon={<WarningIcon />}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ pl: 5 }}>
                    {section.content.map((paragraph, index) => (
                      <Typography 
                        key={index} 
                        variant="body2" 
                        sx={{ 
                          mb: 1.5,
                          lineHeight: 1.6,
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {paragraph}
                      </Typography>
                    ))}
                    
                    {section.subsections && section.subsections.map((subsection, subIndex) => (
                      <Box key={subIndex} sx={{ mt: 3, pl: 2, borderLeft: 2, borderColor: 'primary.light' }}>
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                          {subsection.title}
                        </Typography>
                        {subsection.content.map((subParagraph, subParIndex) => (
                          <Typography 
                            key={subParIndex} 
                            variant="body2" 
                            sx={{ 
                              mb: 1,
                              lineHeight: 1.6,
                              whiteSpace: 'pre-line'
                            }}
                          >
                            {subParagraph}
                          </Typography>
                        ))}
                      </Box>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}

            {/* Дополнительная информация */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Дополнительная информация
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Данные Условия использования являются юридически обязывающим соглашением между вами и 
                  ООО "Лайкс энд Лав". Пожалуйста, сохраните копию данного документа для своих записей.
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Если у вас есть вопросы относительно данных Условий, пожалуйста, свяжитесь с нашей 
                  службой поддержки до начала использования Сервиса.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/legal/privacy')}
                  >
                    Политика конфиденциальности
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/legal/cookies')}
                  >
                    Политика cookies
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/help/contact')}
                  >
                    Связаться с нами
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Согласие */}
            <Alert severity="success" sx={{ mt: 4 }}>
              <Typography variant="body2">
                <strong>Используя приложение Likes & Love, вы подтверждаете, что:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                • Прочитали и поняли данные Условия использования<br/>
                • Согласны соблюдать все положения и требования<br/>
                • Понимаете последствия нарушения данных Условий
              </Typography>
            </Alert>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default TermsOfServicePage;
