import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  IconButton,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  CallEnd as EndCallIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  Videocam as VideocamIcon,
  VideocamOff as VideocamOffIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Message as MessageIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { 
  getCallSession,
  acceptCall,
  declineCall,
  endCall
} from '../../src/services/chatService';
import { CallSession } from '../../src/types/chat.types';

const CallPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;

  const [callSession, setCallSession] = useState<CallSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadCallSession(id);
    }
  }, [user, router, id]);

  useEffect(() => {
    if (callSession?.status === 'active' && callSession.startedAt) {
      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        const startTime = new Date(callSession.startedAt!).getTime();
        const currentTime = new Date().getTime();
        const duration = Math.floor((currentTime - startTime) / 1000);
        setCallDuration(duration);
      }, 1000);

      return () => {
        if (durationIntervalRef.current) {
          clearInterval(durationIntervalRef.current);
        }
      };
    }
  }, [callSession]);

  const loadCallSession = async (sessionId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const session = await getCallSession(sessionId);
      setCallSession(session);
      
      // Initialize video if it's a video call
      if (session.type === 'video') {
        initializeVideo();
      }
    } catch (err: any) {
      setError('Ошибка загрузки сессии звонка');
    } finally {
      setLoading(false);
    }
  };

  const initializeVideo = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      });
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error('Error accessing media devices:', err);
      setError('Не удалось получить доступ к камере и микрофону');
    }
  };

  const handleAcceptCall = async () => {
    if (!callSession) return;
    
    try {
      setActionLoading('accept');
      await acceptCall(callSession.id);
      
      // Reload session to get updated status
      await loadCallSession(callSession.id);
    } catch (err: any) {
      setError('Ошибка принятия звонка');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeclineCall = async () => {
    if (!callSession) return;
    
    try {
      setActionLoading('decline');
      await declineCall(callSession.id);
      router.push('/calls');
    } catch (err: any) {
      setError('Ошибка отклонения звонка');
    } finally {
      setActionLoading(null);
    }
  };

  const handleEndCall = async () => {
    if (!callSession) return;
    
    try {
      setActionLoading('end');
      await endCall(callSession.id);
      router.push('/calls');
    } catch (err: any) {
      setError('Ошибка завершения звонка');
    } finally {
      setActionLoading(null);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    // Here you would implement actual mute functionality
  };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
    // Here you would implement actual video toggle functionality
  };

  const toggleSpeaker = () => {
    setIsSpeakerOn(!isSpeakerOn);
    // Here you would implement actual speaker toggle functionality
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getCallStatusLabel = (status: CallSession['status']) => {
    switch (status) {
      case 'initiating':
        return 'Инициация звонка...';
      case 'ringing':
        return 'Звонок...';
      case 'active':
        return 'Активный звонок';
      case 'ended':
        return 'Звонок завершен';
      case 'declined':
        return 'Звонок отклонен';
      case 'missed':
        return 'Пропущенный звонок';
      default:
        return status;
    }
  };

  const isIncomingCall = callSession && callSession.initiatorId !== user?.id;
  const isCallActive = callSession?.status === 'active';
  const isCallRinging = callSession?.status === 'ringing';

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка звонка - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка звонка...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !callSession) {
    return (
      <>
        <Head>
          <title>Ошибка звонка - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (!callSession) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`${callSession.type === 'video' ? 'Видеозвонок' : 'Аудиозвонок'} - Likes & Love`}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 1, md: 2 } }}>
            <Paper 
              elevation={3} 
              sx={{ 
                height: '90vh', 
                display: 'flex', 
                flexDirection: 'column',
                backgroundColor: callSession.type === 'video' ? 'black' : 'background.paper'
              }}
            >
              {error && (
                <Alert severity="error" sx={{ m: 2 }}>
                  {error}
                </Alert>
              )}

              {/* Video Call Interface */}
              {callSession.type === 'video' ? (
                <Box sx={{ position: 'relative', flexGrow: 1 }}>
                  {/* Remote Video */}
                  <video
                    ref={remoteVideoRef}
                    autoPlay
                    playsInline
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      backgroundColor: 'black'
                    }}
                  />
                  
                  {/* Local Video */}
                  <Box sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    width: 200,
                    height: 150,
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: '2px solid white'
                  }}>
                    <video
                      ref={localVideoRef}
                      autoPlay
                      playsInline
                      muted
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  </Box>

                  {/* Call Info Overlay */}
                  <Box sx={{
                    position: 'absolute',
                    top: 16,
                    left: 16,
                    color: 'white',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    p: 2,
                    borderRadius: 2
                  }}>
                    <Typography variant="h6">
                      {getCallStatusLabel(callSession.status)}
                    </Typography>
                    {isCallActive && (
                      <Typography variant="body2">
                        {formatDuration(callDuration)}
                      </Typography>
                    )}
                  </Box>
                </Box>
              ) : (
                /* Audio Call Interface */
                <Box sx={{ 
                  flexGrow: 1, 
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  p: 4
                }}>
                  <Avatar
                    sx={{ 
                      width: 200, 
                      height: 200, 
                      mb: 4,
                      fontSize: '4rem'
                    }}
                  >
                    👤
                  </Avatar>
                  
                  <Typography variant="h4" gutterBottom>
                    {getCallStatusLabel(callSession.status)}
                  </Typography>
                  
                  {isCallActive && (
                    <Typography variant="h6" color="text.secondary">
                      {formatDuration(callDuration)}
                    </Typography>
                  )}
                  
                  <Chip
                    label={callSession.type === 'video' ? 'Видеозвонок' : 'Аудиозвонок'}
                    color="primary"
                    sx={{ mt: 2 }}
                  />
                </Box>
              )}

              {/* Call Controls */}
              <Box sx={{ 
                p: 3, 
                backgroundColor: callSession.type === 'video' ? 'rgba(0,0,0,0.8)' : 'background.paper',
                display: 'flex',
                justifyContent: 'center',
                gap: 2
              }}>
                {/* Incoming Call Controls */}
                {isIncomingCall && isCallRinging && (
                  <Fade in timeout={600}>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        variant="contained"
                        color="error"
                        size="large"
                        startIcon={actionLoading === 'decline' ? <CircularProgress size={20} /> : <EndCallIcon />}
                        onClick={handleDeclineCall}
                        disabled={!!actionLoading}
                        sx={{ minWidth: 120 }}
                      >
                        Отклонить
                      </Button>
                      <Button
                        variant="contained"
                        color="success"
                        size="large"
                        startIcon={actionLoading === 'accept' ? <CircularProgress size={20} /> : <VideocamIcon />}
                        onClick={handleAcceptCall}
                        disabled={!!actionLoading}
                        sx={{ minWidth: 120 }}
                      >
                        Принять
                      </Button>
                    </Box>
                  </Fade>
                )}

                {/* Active Call Controls */}
                {isCallActive && (
                  <Fade in timeout={600}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton
                        onClick={toggleMute}
                        color={isMuted ? 'error' : 'default'}
                        sx={{ 
                          backgroundColor: isMuted ? 'error.light' : 'action.hover',
                          color: callSession.type === 'video' ? 'white' : 'inherit'
                        }}
                      >
                        {isMuted ? <MicOffIcon /> : <MicIcon />}
                      </IconButton>

                      {callSession.type === 'video' && (
                        <IconButton
                          onClick={toggleVideo}
                          color={!isVideoEnabled ? 'error' : 'default'}
                          sx={{ 
                            backgroundColor: !isVideoEnabled ? 'error.light' : 'action.hover',
                            color: 'white'
                          }}
                        >
                          {isVideoEnabled ? <VideocamIcon /> : <VideocamOffIcon />}
                        </IconButton>
                      )}

                      <IconButton
                        onClick={toggleSpeaker}
                        color={isSpeakerOn ? 'primary' : 'default'}
                        sx={{ 
                          backgroundColor: isSpeakerOn ? 'primary.light' : 'action.hover',
                          color: callSession.type === 'video' ? 'white' : 'inherit'
                        }}
                      >
                        {isSpeakerOn ? <VolumeUpIcon /> : <VolumeOffIcon />}
                      </IconButton>

                      {callSession.type === 'video' && (
                        <IconButton
                          onClick={toggleFullscreen}
                          sx={{ color: 'white' }}
                        >
                          {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                        </IconButton>
                      )}

                      <IconButton
                        onClick={() => router.push(`/chat/${callSession.conversationId}`)}
                        sx={{ 
                          color: callSession.type === 'video' ? 'white' : 'inherit'
                        }}
                      >
                        <MessageIcon />
                      </IconButton>

                      <IconButton
                        onClick={handleEndCall}
                        disabled={actionLoading === 'end'}
                        sx={{ 
                          backgroundColor: 'error.main',
                          color: 'white',
                          '&:hover': { backgroundColor: 'error.dark' }
                        }}
                      >
                        {actionLoading === 'end' ? <CircularProgress size={20} /> : <EndCallIcon />}
                      </IconButton>
                    </Box>
                  </Fade>
                )}

                {/* Ended Call Controls */}
                {(callSession.status === 'ended' || callSession.status === 'declined' || callSession.status === 'missed') && (
                  <Button
                    variant="contained"
                    onClick={() => router.push('/calls')}
                  >
                    Вернуться к истории звонков
                  </Button>
                )}
              </Box>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CallPage;
