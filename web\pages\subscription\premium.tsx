import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import {
  ArrowBack,
  Diamond as DiamondIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Visibility as VisibilityIcon,
  Chat as ChatIcon,
  VideoCall as VideoCallIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  Support as SupportIcon,
  Verified as VerifiedIcon,
  Favorite as FavoriteIcon,
  FlashOn as FlashOnIcon,
  LocationOn as LocationIcon,
  FilterList as FilterIcon,
  Notifications as NotificationsIcon,
  Analytics as AnalyticsIcon,
  VpnKey as VpnKeyIcon,
  Speed as SpeedIcon,
  Shield as ShieldIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSubscription } from '../../src/contexts/SubscriptionContext';

interface PremiumFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'discovery' | 'communication' | 'profile' | 'analytics' | 'security';
  isAvailable: boolean;
  requiresPlan: string[];
}

const PremiumPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { currentSubscription, availablePlans, loading, hasFeature } = useSubscription();

  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const premiumFeatures: PremiumFeature[] = [
    // Discovery Features
    {
      id: 'unlimited_likes',
      title: 'Безлимитные лайки',
      description: 'Ставьте лайки без ограничений и находите больше совпадений',
      icon: <FavoriteIcon sx={{ fontSize: 40, color: '#e91e63' }} />,
      category: 'discovery',
      isAvailable: hasFeature('unlimited_likes'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'super_likes',
      title: 'Супер-лайки',
      description: 'Выделитесь среди других пользователей с супер-лайками',
      icon: <StarIcon sx={{ fontSize: 40, color: '#ffc107' }} />,
      category: 'discovery',
      isAvailable: hasFeature('super_likes'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'profile_boost',
      title: 'Буст профиля',
      description: 'Станьте одним из топ-профилей в вашем регионе на 30 минут',
      icon: <FlashOnIcon sx={{ fontSize: 40, color: '#ff5722' }} />,
      category: 'discovery',
      isAvailable: hasFeature('profile_boost'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'advanced_filters',
      title: 'Расширенные фильтры',
      description: 'Фильтруйте по образованию, росту, знаку зодиака и другим параметрам',
      icon: <FilterIcon sx={{ fontSize: 40, color: '#9c27b0' }} />,
      category: 'discovery',
      isAvailable: hasFeature('advanced_filters'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'passport',
      title: 'Паспорт',
      description: 'Знакомьтесь с людьми в любом городе мира',
      icon: <LocationIcon sx={{ fontSize: 40, color: '#2196f3' }} />,
      category: 'discovery',
      isAvailable: hasFeature('passport'),
      requiresPlan: ['vip']
    },
    {
      id: 'top_picks',
      title: 'Лучшие выборы',
      description: 'Ежедневная подборка самых подходящих вам профилей',
      icon: <DiamondIcon sx={{ fontSize: 40, color: '#00bcd4' }} />,
      category: 'discovery',
      isAvailable: hasFeature('top_picks'),
      requiresPlan: ['premium', 'vip']
    },

    // Communication Features
    {
      id: 'read_receipts',
      title: 'Уведомления о прочтении',
      description: 'Узнавайте, когда ваши сообщения прочитаны',
      icon: <CheckIcon sx={{ fontSize: 40, color: '#4caf50' }} />,
      category: 'communication',
      isAvailable: hasFeature('read_receipts'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'video_calls',
      title: 'Видеозвонки',
      description: 'Общайтесь лицом к лицу с вашими совпадениями',
      icon: <VideoCallIcon sx={{ fontSize: 40, color: '#673ab7' }} />,
      category: 'communication',
      isAvailable: hasFeature('video_calls'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'priority_messages',
      title: 'Приоритетные сообщения',
      description: 'Ваши сообщения будут показаны первыми',
      icon: <ChatIcon sx={{ fontSize: 40, color: '#ff9800' }} />,
      category: 'communication',
      isAvailable: hasFeature('priority_messages'),
      requiresPlan: ['vip']
    },

    // Profile Features
    {
      id: 'incognito_mode',
      title: 'Режим инкогнито',
      description: 'Просматривайте профили незаметно',
      icon: <VisibilityIcon sx={{ fontSize: 40, color: '#607d8b' }} />,
      category: 'profile',
      isAvailable: hasFeature('incognito_mode'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'profile_verification',
      title: 'Верификация профиля',
      description: 'Получите синюю галочку и повысьте доверие',
      icon: <VerifiedIcon sx={{ fontSize: 40, color: '#2196f3' }} />,
      category: 'profile',
      isAvailable: hasFeature('profile_verification'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'unlimited_photos',
      title: 'Безлимитные фото',
      description: 'Загружайте неограниченное количество фотографий',
      icon: <SpeedIcon sx={{ fontSize: 40, color: '#795548' }} />,
      category: 'profile',
      isAvailable: hasFeature('unlimited_photos'),
      requiresPlan: ['premium', 'vip']
    },

    // Analytics Features
    {
      id: 'profile_analytics',
      title: 'Аналитика профиля',
      description: 'Подробная статистика просмотров и лайков',
      icon: <AnalyticsIcon sx={{ fontSize: 40, color: '#3f51b5' }} />,
      category: 'analytics',
      isAvailable: hasFeature('profile_analytics'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'match_insights',
      title: 'Инсайты совпадений',
      description: 'Узнайте, что привлекло ваши совпадения',
      icon: <TrendingUpIcon sx={{ fontSize: 40, color: '#009688' }} />,
      category: 'analytics',
      isAvailable: hasFeature('match_insights'),
      requiresPlan: ['vip']
    },

    // Security Features
    {
      id: 'priority_support',
      title: 'Приоритетная поддержка',
      description: 'Быстрые ответы от службы поддержки 24/7',
      icon: <SupportIcon sx={{ fontSize: 40, color: '#8bc34a' }} />,
      category: 'security',
      isAvailable: hasFeature('priority_support'),
      requiresPlan: ['premium', 'vip']
    },
    {
      id: 'enhanced_security',
      title: 'Усиленная безопасность',
      description: 'Дополнительные меры защиты вашего аккаунта',
      icon: <ShieldIcon sx={{ fontSize: 40, color: '#f44336' }} />,
      category: 'security',
      isAvailable: hasFeature('enhanced_security'),
      requiresPlan: ['vip']
    }
  ];

  const categories = [
    { id: 'all', name: 'Все функции', icon: <DiamondIcon /> },
    { id: 'discovery', name: 'Поиск', icon: <VisibilityIcon /> },
    { id: 'communication', name: 'Общение', icon: <ChatIcon /> },
    { id: 'profile', name: 'Профиль', icon: <VerifiedIcon /> },
    { id: 'analytics', name: 'Аналитика', icon: <TrendingUpIcon /> },
    { id: 'security', name: 'Безопасность', icon: <SecurityIcon /> }
  ];

  const filteredFeatures = selectedCategory === 'all' 
    ? premiumFeatures 
    : premiumFeatures.filter(feature => feature.category === selectedCategory);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  const getPlanName = (planIds: string[]) => {
    if (planIds.includes('premium') && planIds.includes('vip')) {
      return 'Premium или VIP';
    } else if (planIds.includes('premium')) {
      return 'Premium';
    } else if (planIds.includes('vip')) {
      return 'VIP';
    }
    return 'Premium план';
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Premium функции - Likes & Love</title>
        <meta 
          name="description" 
          content="Откройте все возможности приложения знакомств Likes & Love с Premium подпиской" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/subscription/premium" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <DiamondIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Premium функции
              </Typography>
              <Button
                variant="contained"
                startIcon={<StarIcon />}
                onClick={() => router.push('/subscription/plans')}
              >
                Выбрать план
              </Button>
            </Box>

            {/* Current Subscription Status */}
            {currentSubscription && (
              <Alert 
                severity={currentSubscription.status === 'active' ? 'success' : 'info'} 
                sx={{ mb: 4 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DiamondIcon />
                  <Typography variant="body1">
                    {currentSubscription.status === 'active' 
                      ? `У вас активна подписка ${currentSubscription.plan.displayName}`
                      : `Ваша подписка ${currentSubscription.plan.displayName} неактивна`
                    }
                  </Typography>
                </Box>
              </Alert>
            )}

            {/* Category Filter */}
            <Paper elevation={2} sx={{ p: 2, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Категории функций
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {categories.map((category) => (
                  <Chip
                    key={category.id}
                    label={category.name}
                    icon={category.icon}
                    onClick={() => setSelectedCategory(category.id)}
                    color={selectedCategory === category.id ? 'primary' : 'default'}
                    variant={selectedCategory === category.id ? 'filled' : 'outlined'}
                    clickable
                  />
                ))}
              </Box>
            </Paper>

            {/* Features Grid */}
            <Grid container spacing={3}>
              {filteredFeatures.map((feature, index) => (
                <Grid item xs={12} sm={6} md={4} key={feature.id}>
                  <Zoom in timeout={300 + index * 50}>
                    <Card
                      elevation={feature.isAvailable ? 4 : 2}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        opacity: feature.isAvailable ? 1 : 0.7,
                        border: feature.isAvailable ? `2px solid ${theme.palette.success.main}` : 'none',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: theme.shadows[8]
                        },
                        transition: 'all 0.3s ease-in-out'
                      }}
                    >
                      {/* Available Badge */}
                      {feature.isAvailable && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}
                        >
                          <Chip
                            label="Доступно"
                            color="success"
                            size="small"
                            icon={<CheckIcon />}
                          />
                        </Box>
                      )}

                      <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                        {/* Feature Icon */}
                        <Box sx={{ mb: 2 }}>
                          {feature.icon}
                        </Box>

                        {/* Feature Title */}
                        <Typography variant="h6" gutterBottom>
                          {feature.title}
                        </Typography>

                        {/* Feature Description */}
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {feature.description}
                        </Typography>

                        {/* Required Plan */}
                        <Chip
                          label={`Требует: ${getPlanName(feature.requiresPlan)}`}
                          size="small"
                          color={feature.isAvailable ? 'success' : 'primary'}
                          variant="outlined"
                        />
                      </CardContent>

                      <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                        {feature.isAvailable ? (
                          <Button
                            variant="outlined"
                            color="success"
                            startIcon={<CheckIcon />}
                            disabled
                          >
                            Активно
                          </Button>
                        ) : (
                          <Button
                            variant="contained"
                            color="primary"
                            startIcon={<StarIcon />}
                            onClick={() => router.push('/subscription/plans')}
                          >
                            Получить доступ
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Zoom>
                </Grid>
              ))}
            </Grid>

            {/* Call to Action */}
            {!currentSubscription && (
              <Box sx={{ textAlign: 'center', mt: 6, p: 4, backgroundColor: 'background.paper', borderRadius: 2 }}>
                <DiamondIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Откройте все возможности Likes & Love
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  Получите доступ ко всем Premium функциям и найдите свою любовь быстрее
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<StarIcon />}
                  onClick={() => router.push('/subscription/plans')}
                >
                  Выбрать план подписки
                </Button>
              </Box>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PremiumPage;
