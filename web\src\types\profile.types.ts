export interface ProfilePhoto {
  id: string;
  url: string;
  thumbnailUrl: string;
  isMain: boolean;
  order: number;
  uploadedAt: string;
  isVerified: boolean;
  moderationStatus: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
}

export interface ProfileVerification {
  id: string;
  type: 'phone' | 'email' | 'photo' | 'document' | 'social' | 'gosuslugi';
  status: 'pending' | 'verified' | 'rejected' | 'expired';
  verifiedAt?: string;
  expiresAt?: string;
  rejectionReason?: string;
  documentType?: 'passport' | 'driver_license' | 'id_card';
  socialProvider?: 'vk' | 'google' | 'yandex' | 'sber';
}

export interface ProfileVisitor {
  id: string;
  userId: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    age: number;
    avatarUrl?: string;
    isOnline: boolean;
    lastActiveAt: string;
    verificationStatus: {
      phone: boolean;
      email: boolean;
      photo: boolean;
    };
  };
  visitedAt: string;
  viewType: 'profile' | 'photo' | 'search';
  isRead: boolean;
}

export interface BlockedUser {
  id: string;
  userId: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    avatarUrl?: string;
  };
  blockedAt: string;
  reason: 'spam' | 'inappropriate' | 'harassment' | 'fake' | 'other';
  customReason?: string;
}

export interface Like {
  id: string;
  fromUserId: string;
  toUserId: string;
  fromUser?: {
    id: string;
    firstName: string;
    lastName: string;
    age: number;
    avatarUrl?: string;
    isOnline: boolean;
    bio?: string;
    verificationStatus: {
      phone: boolean;
      email: boolean;
      photo: boolean;
    };
  };
  toUser?: {
    id: string;
    firstName: string;
    lastName: string;
    age: number;
    avatarUrl?: string;
    isOnline: boolean;
    bio?: string;
    verificationStatus: {
      phone: boolean;
      email: boolean;
      photo: boolean;
    };
  };
  type: 'like' | 'super_like' | 'pass';
  message?: string;
  createdAt: string;
  isRead: boolean;
  isMutual: boolean;
}

export interface Match {
  id: string;
  user1Id: string;
  user2Id: string;
  user1: {
    id: string;
    firstName: string;
    lastName: string;
    age: number;
    avatarUrl?: string;
    isOnline: boolean;
    lastActiveAt: string;
    bio?: string;
    interests: string[];
    verificationStatus: {
      phone: boolean;
      email: boolean;
      photo: boolean;
    };
  };
  user2: {
    id: string;
    firstName: string;
    lastName: string;
    age: number;
    avatarUrl?: string;
    isOnline: boolean;
    lastActiveAt: string;
    bio?: string;
    interests: string[];
    verificationStatus: {
      phone: boolean;
      email: boolean;
      photo: boolean;
    };
  };
  matchedAt: string;
  lastMessageAt?: string;
  lastMessage?: {
    id: string;
    text: string;
    senderId: string;
    sentAt: string;
  };
  isRead: boolean;
  compatibilityScore: number;
  commonInterests: string[];
  status: 'active' | 'archived' | 'blocked';
}

export interface PhotoUploadData {
  file: File;
  isMain?: boolean;
  order?: number;
}

export interface PhotoUploadResponse {
  success: boolean;
  message: string;
  photo?: ProfilePhoto;
  error?: string;
}

export interface VerificationRequest {
  type: 'phone' | 'email' | 'photo' | 'document' | 'social';
  data?: {
    phoneNumber?: string;
    email?: string;
    documentType?: string;
    documentPhoto?: File;
    socialProvider?: string;
  };
}

export interface VerificationResponse {
  success: boolean;
  message: string;
  verification?: ProfileVerification;
  error?: string;
}

export interface ProfileDeletionRequest {
  reason: 'found_someone' | 'not_working' | 'privacy' | 'too_busy' | 'other';
  customReason?: string;
  feedback?: string;
  deleteImmediately: boolean;
}

export interface ProfileDeletionResponse {
  success: boolean;
  message: string;
  scheduledDeletionDate?: string;
  error?: string;
}

export interface LikeActionRequest {
  targetUserId: string;
  type: 'like' | 'super_like' | 'pass';
  message?: string;
}

export interface LikeActionResponse {
  success: boolean;
  message: string;
  isMatch: boolean;
  match?: Match;
  error?: string;
}

export interface BlockUserRequest {
  userId: string;
  reason: 'spam' | 'inappropriate' | 'harassment' | 'fake' | 'other';
  customReason?: string;
  reportToModerators?: boolean;
}

export interface BlockUserResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface ProfileStats {
  totalVisitors: number;
  todayVisitors: number;
  totalLikes: number;
  todayLikes: number;
  totalMatches: number;
  profileViews: number;
  photoViews: number;
  responseRate: number;
  averageResponseTime: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FilterOptions {
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'visitedAt' | 'matchedAt' | 'lastActiveAt';
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
  isRead?: boolean;
  verifiedOnly?: boolean;
}

export interface ProfileContextType {
  photos: ProfilePhoto[];
  verifications: ProfileVerification[];
  visitors: ProfileVisitor[];
  blockedUsers: BlockedUser[];
  stats: ProfileStats | null;
  loading: boolean;
  error: string | null;
  uploadPhoto: (data: PhotoUploadData) => Promise<PhotoUploadResponse>;
  deletePhoto: (photoId: string) => Promise<void>;
  setMainPhoto: (photoId: string) => Promise<void>;
  reorderPhotos: (photoIds: string[]) => Promise<void>;
  requestVerification: (request: VerificationRequest) => Promise<VerificationResponse>;
  blockUser: (request: BlockUserRequest) => Promise<BlockUserResponse>;
  unblockUser: (userId: string) => Promise<void>;
  deleteProfile: (request: ProfileDeletionRequest) => Promise<ProfileDeletionResponse>;
  markVisitorsAsRead: (visitorIds: string[]) => Promise<void>;
  refreshData: () => Promise<void>;
}
