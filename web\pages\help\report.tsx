import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Breadcrumbs,
  Link,
  FormControlLabel,
  Checkbox,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Report as ReportIcon,
  Send as SendIcon,
  Home as HomeIcon,
  Help as HelpIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Person as PersonIcon,
  Block as BlockIcon,
  Security as SecurityIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Типы для формы жалобы
interface ReportForm {
  reportType: string;
  targetUserId?: string;
  targetUsername?: string;
  reason: string;
  description: string;
  evidence?: string;
  anonymous: boolean;
  blockUser: boolean;
}

// Схема валидации
const reportSchema = yup.object({
  reportType: yup.string().required('Выберите тип жалобы'),
  reason: yup.string().required('Выберите причину'),
  description: yup.string().required('Описание обязательно').min(20, 'Минимум 20 символов'),
  targetUsername: yup.string().when('reportType', {
    is: 'user',
    then: (schema) => schema.required('Укажите имя пользователя'),
    otherwise: (schema) => schema.notRequired()
  })
});

const ReportPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ReportForm>({
    resolver: yupResolver(reportSchema),
    defaultValues: {
      reportType: '',
      targetUserId: '',
      targetUsername: '',
      reason: '',
      description: '',
      evidence: '',
      anonymous: false,
      blockUser: false
    }
  });

  const reportType = watch('reportType');

  // Типы жалоб
  const reportTypes = [
    { value: 'user', label: 'Жалоба на пользователя', icon: <PersonIcon /> },
    { value: 'content', label: 'Неподходящий контент', icon: <WarningIcon /> },
    { value: 'technical', label: 'Техническая проблема', icon: <InfoIcon /> },
    { value: 'security', label: 'Проблема безопасности', icon: <SecurityIcon /> }
  ];

  // Причины жалоб на пользователей
  const userReasons = [
    { value: 'fake_profile', label: 'Поддельный профиль' },
    { value: 'inappropriate_photos', label: 'Неподходящие фото' },
    { value: 'harassment', label: 'Домогательства' },
    { value: 'spam', label: 'Спам или реклама' },
    { value: 'scam', label: 'Мошенничество' },
    { value: 'underage', label: 'Несовершеннолетний' },
    { value: 'hate_speech', label: 'Язык вражды' },
    { value: 'violence', label: 'Угрозы или насилие' },
    { value: 'other', label: 'Другое' }
  ];

  // Причины жалоб на контент
  const contentReasons = [
    { value: 'inappropriate_content', label: 'Неподходящий контент' },
    { value: 'copyright', label: 'Нарушение авторских прав' },
    { value: 'misinformation', label: 'Ложная информация' },
    { value: 'adult_content', label: 'Контент для взрослых' },
    { value: 'other', label: 'Другое' }
  ];

  // Причины технических проблем
  const technicalReasons = [
    { value: 'app_crash', label: 'Приложение вылетает' },
    { value: 'loading_issues', label: 'Проблемы с загрузкой' },
    { value: 'payment_issues', label: 'Проблемы с оплатой' },
    { value: 'notification_issues', label: 'Проблемы с уведомлениями' },
    { value: 'other', label: 'Другое' }
  ];

  // Причины проблем безопасности
  const securityReasons = [
    { value: 'account_hacked', label: 'Аккаунт взломан' },
    { value: 'unauthorized_access', label: 'Несанкционированный доступ' },
    { value: 'data_breach', label: 'Утечка данных' },
    { value: 'phishing', label: 'Фишинг' },
    { value: 'other', label: 'Другое' }
  ];

  const getReasons = () => {
    switch (reportType) {
      case 'user': return userReasons;
      case 'content': return contentReasons;
      case 'technical': return technicalReasons;
      case 'security': return securityReasons;
      default: return [];
    }
  };

  const handleFormSubmit = async (data: ReportForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отправки жалобы
      // await submitReport(data);

      // Симуляция отправки
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSuccess(true);
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке жалобы');
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    'Выберите тип жалобы',
    'Укажите детали',
    'Дополнительная информация'
  ];

  if (success) {
    return (
      <Layout title="Жалоба отправлена">
        <Container maxWidth="md">
          <Box sx={{ py: 6, textAlign: 'center' }}>
            <CheckCircleIcon color="success" sx={{ fontSize: 80, mb: 3 }} />
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 2 }}>
              Жалоба отправлена!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Мы рассмотрим вашу жалобу в течение 24 часов
            </Typography>
            <Alert severity="info" sx={{ mb: 4, textAlign: 'left' }}>
              <Typography variant="body2">
                <strong>Что происходит дальше:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="• Наша команда модерации рассмотрит жалобу" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• При необходимости мы свяжемся с вами для уточнений" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Вы получите уведомление о результатах рассмотрения" />
                </ListItem>
              </List>
            </Alert>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button variant="contained" onClick={() => router.push('/help')}>
                Вернуться в центр помощи
              </Button>
              <Button variant="outlined" onClick={() => setSuccess(false)}>
                Подать еще одну жалобу
              </Button>
            </Box>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Подать жалобу - Likes & Love</title>
        <meta name="description" content="Подайте жалобу на пользователя или сообщите о проблеме в приложении Likes & Love. Безопасность и комфорт наших пользователей - наш приоритет." />
        <meta name="keywords" content="жалоба, безопасность, модерация, поддержка" />
      </Head>

      <Layout title="Подать жалобу">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/help" 
                onClick={(e) => { e.preventDefault(); router.push('/help'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HelpIcon fontSize="small" />
                Помощь
              </Link>
              <Typography color="text.primary">Жалоба</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <ReportIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Подать жалобу
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Сообщите нам о нарушениях или проблемах
              </Typography>
            </Box>

            {/* Важная информация */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Важно:</strong> Ложные жалобы могут привести к ограничениям вашего аккаунта. 
                Пожалуйста, подавайте жалобы только при реальных нарушениях.
              </Typography>
            </Alert>

            <Card>
              <CardContent>
                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <form onSubmit={handleSubmit(handleFormSubmit)}>
                  <Grid container spacing={3}>
                    {/* Тип жалобы */}
                    <Grid item xs={12}>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                        Тип жалобы
                      </Typography>
                      <Controller
                        name="reportType"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth error={!!errors.reportType}>
                            <InputLabel>Выберите тип жалобы</InputLabel>
                            <Select {...field} label="Выберите тип жалобы">
                              {reportTypes.map((type) => (
                                <MenuItem key={type.value} value={type.value}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {type.icon}
                                    {type.label}
                                  </Box>
                                </MenuItem>
                              ))}
                            </Select>
                            {errors.reportType && (
                              <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                                {errors.reportType.message}
                              </Typography>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>

                    {/* Имя пользователя (если жалоба на пользователя) */}
                    {reportType === 'user' && (
                      <Grid item xs={12}>
                        <Controller
                          name="targetUsername"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Имя пользователя или ID"
                              fullWidth
                              error={!!errors.targetUsername}
                              helperText={errors.targetUsername?.message || 'Укажите точное имя пользователя'}
                            />
                          )}
                        />
                      </Grid>
                    )}

                    {/* Причина */}
                    {reportType && (
                      <Grid item xs={12}>
                        <Controller
                          name="reason"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth error={!!errors.reason}>
                              <InputLabel>Причина жалобы</InputLabel>
                              <Select {...field} label="Причина жалобы">
                                {getReasons().map((reason) => (
                                  <MenuItem key={reason.value} value={reason.value}>
                                    {reason.label}
                                  </MenuItem>
                                ))}
                              </Select>
                              {errors.reason && (
                                <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                                  {errors.reason.message}
                                </Typography>
                              )}
                            </FormControl>
                          )}
                        />
                      </Grid>
                    )}

                    {/* Описание */}
                    <Grid item xs={12}>
                      <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Подробное описание"
                            multiline
                            rows={6}
                            fullWidth
                            error={!!errors.description}
                            helperText={errors.description?.message || 'Опишите ситуацию максимально подробно'}
                          />
                        )}
                      />
                    </Grid>

                    {/* Доказательства */}
                    <Grid item xs={12}>
                      <Controller
                        name="evidence"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Ссылки на доказательства (необязательно)"
                            fullWidth
                            helperText="Ссылки на скриншоты, сообщения или другие доказательства"
                          />
                        )}
                      />
                    </Grid>

                    {/* Дополнительные опции */}
                    <Grid item xs={12}>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                        Дополнительные опции
                      </Typography>
                      
                      <Controller
                        name="anonymous"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={<Checkbox {...field} checked={field.value} />}
                            label="Подать жалобу анонимно"
                          />
                        )}
                      />

                      {reportType === 'user' && (
                        <Controller
                          name="blockUser"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Checkbox {...field} checked={field.value} />}
                              label="Заблокировать этого пользователя"
                            />
                          )}
                        />
                      )}
                    </Grid>

                    {/* Кнопка отправки */}
                    <Grid item xs={12}>
                      <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        fullWidth={isMobile}
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                      >
                        {loading ? 'Отправляем...' : 'Отправить жалобу'}
                      </Button>
                    </Grid>
                  </Grid>
                </form>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ReportPage;
