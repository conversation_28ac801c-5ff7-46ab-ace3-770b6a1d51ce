import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  IconButton,
  Avatar,
  LinearProgress,
  Dialog,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  Divider
} from '@mui/material';
import {
  ArrowBack,
  Favorite as FavoriteIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Send as SendIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для истории и комментариев
interface Story {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
  };
  type: 'photo' | 'video' | 'text';
  content: {
    url?: string;
    text?: string;
    backgroundColor?: string;
    textColor?: string;
    fontSize?: number;
  };
  duration: number;
  createdAt: string;
  expiresAt: string;
  views: number;
  likes: number;
  comments: number;
  isViewed: boolean;
  isLiked: boolean;
}

interface Comment {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    avatar: string;
  };
  text: string;
  createdAt: string;
}

interface CommentForm {
  text: string;
}

// Схема валидации для комментариев
const commentSchema = yup.object({
  text: yup.string().required('Введите комментарий').max(200, 'Максимум 200 символов')
});

const StoryDetailPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [story, setStory] = useState<Story | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [commentsOpen, setCommentsOpen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<CommentForm>({
    resolver: yupResolver(commentSchema),
    defaultValues: {
      text: ''
    }
  });

  // Загрузка истории при монтировании компонента
  useEffect(() => {
    if (id) {
      loadStory(id as string);
    }
  }, [id]);

  // Автопрогресс для просмотра истории
  useEffect(() => {
    if (story && isPlaying) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (100 / story.duration);
          if (newProgress >= 100) {
            // История закончилась, возвращаемся к списку
            router.push('/stories');
            return 100;
          }
          return newProgress;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [story, isPlaying, router]);

  const loadStory = async (storyId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения истории
      // const response = await getStory(storyId);
      
      // Мок данные
      const mockStory: Story = {
        id: storyId,
        userId: 'user1',
        user: {
          id: 'user1',
          name: 'Анна Петрова',
          avatar: '/avatars/anna.jpg',
          isVerified: true
        },
        type: 'photo',
        content: {
          url: '/stories/story1.jpg'
        },
        duration: 5,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),
        views: 45,
        likes: 12,
        comments: 3,
        isViewed: false,
        isLiked: false
      };

      const mockComments: Comment[] = [
        {
          id: 'comment1',
          userId: 'user2',
          user: {
            id: 'user2',
            name: 'Михаил Иванов',
            avatar: '/avatars/mikhail.jpg'
          },
          text: 'Красивое фото! 😍',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        },
        {
          id: 'comment2',
          userId: 'user3',
          user: {
            id: 'user3',
            name: 'Елена Сидорова',
            avatar: '/avatars/elena.jpg'
          },
          text: 'Где это место?',
          createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        }
      ];

      setStory(mockStory);
      setComments(mockComments);

      // Отмечаем историю как просмотренную
      await markStoryAsViewed(storyId);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке истории');
    } finally {
      setLoading(false);
    }
  };

  const markStoryAsViewed = async (storyId: string) => {
    try {
      // Здесь будет вызов API для отметки просмотра
      // await viewStory(storyId);
    } catch (err) {
      console.error('Ошибка при отметке просмотра:', err);
    }
  };

  const handleLikeStory = async () => {
    if (!story) return;

    try {
      // Здесь будет вызов API для лайка истории
      // await likeStory(story.id);
      
      setStory(prev => prev ? {
        ...prev,
        isLiked: !prev.isLiked,
        likes: prev.isLiked ? prev.likes - 1 : prev.likes + 1
      } : null);
    } catch (err: any) {
      setError(err.message || 'Ошибка при лайке истории');
    }
  };

  const handleAddComment = async (data: CommentForm) => {
    if (!story || !user) return;

    try {
      // Здесь будет вызов API для добавления комментария
      // const response = await addComment(story.id, data.text);
      
      const newComment: Comment = {
        id: `comment_${Date.now()}`,
        userId: user.id,
        user: {
          id: user.id,
          name: user.name,
          avatar: user.avatar || '/avatars/default.jpg'
        },
        text: data.text,
        createdAt: new Date().toISOString()
      };

      setComments(prev => [...prev, newComment]);
      setStory(prev => prev ? { ...prev, comments: prev.comments + 1 } : null);
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при добавлении комментария');
    }
  };

  const handleShare = async () => {
    if (!story) return;

    try {
      if (navigator.share) {
        await navigator.share({
          title: `История от ${story.user.name}`,
          text: 'Посмотрите эту историю в Likes & Love',
          url: window.location.href
        });
      } else {
        // Fallback - копируем ссылку в буфер обмена
        await navigator.clipboard.writeText(window.location.href);
        alert('Ссылка скопирована в буфер обмена');
      }
    } catch (err) {
      console.error('Ошибка при шаринге:', err);
    }
  };

  const handleBack = () => {
    router.push('/stories');
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ч назад`;
    return `${Math.floor(diffInMinutes / 1440)} дн назад`;
  };

  if (!user) {
    return (
      <Layout title="История">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  if (loading) {
    return (
      <Layout title="История">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  if (!story) {
    return (
      <Layout title="История не найдена">
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>
              История не найдена
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Возможно, история была удалена или срок её показа истёк
            </Typography>
            <Button variant="contained" onClick={handleBack}>
              Вернуться к историям
            </Button>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>{`История от ${story.user.name} - Likes & Love`}</title>
        <meta name="description" content={`Смотрите историю от ${story.user.name} в приложении знакомств Likes & Love`} />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="История">
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'black',
            zIndex: 1300,
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* Прогресс бар */}
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              backgroundColor: 'rgba(255,255,255,0.3)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: 'white'
              }
            }}
          />

          {/* Заголовок */}
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              right: 16,
              zIndex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar src={story.user.avatar} sx={{ width: 32, height: 32 }} />
              <Box>
                <Typography variant="body2" color="white" fontWeight="bold">
                  {story.user.name}
                </Typography>
                <Typography variant="caption" color="rgba(255,255,255,0.8)">
                  {formatTimeAgo(story.createdAt)}
                </Typography>
              </Box>
            </Box>
            <IconButton onClick={handleBack} sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Контент истории */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer'
            }}
            onClick={togglePlayPause}
          >
            {story.type === 'photo' && (
              <img
                src={story.content.url}
                alt="История"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
            )}
            {story.type === 'video' && (
              <video
                src={story.content.url}
                autoPlay={isPlaying}
                muted
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
            )}
            {story.type === 'text' && (
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: story.content.backgroundColor,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  p: 4
                }}
              >
                <Typography
                  sx={{
                    color: story.content.textColor,
                    fontSize: story.content.fontSize,
                    fontWeight: 'bold',
                    textAlign: 'center',
                    wordBreak: 'break-word'
                  }}
                >
                  {story.content.text}
                </Typography>
              </Box>
            )}
          </Box>

          {/* Действия */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              right: 16,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <IconButton
                  onClick={handleLikeStory}
                  sx={{ color: story.isLiked ? 'error.main' : 'white' }}
                >
                  <FavoriteIcon />
                </IconButton>
                <Typography variant="caption" color="white">
                  {story.likes}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <IconButton
                  onClick={() => setCommentsOpen(true)}
                  sx={{ color: 'white' }}
                >
                  <CommentIcon />
                </IconButton>
                <Typography variant="caption" color="white">
                  {story.comments}
                </Typography>
              </Box>
              <IconButton onClick={handleShare} sx={{ color: 'white' }}>
                <ShareIcon />
              </IconButton>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <VisibilityIcon sx={{ color: 'white', fontSize: 20 }} />
              <Typography variant="caption" color="white">
                {story.views}
              </Typography>
            </Box>
          </Box>

          {/* Диалог комментариев */}
          <Dialog
            open={commentsOpen}
            onClose={() => setCommentsOpen(false)}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                position: 'fixed',
                bottom: 0,
                m: 0,
                borderRadius: '16px 16px 0 0'
              }
            }}
          >
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Комментарии ({comments.length})
                </Typography>
                <IconButton onClick={() => setCommentsOpen(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Список комментариев */}
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {comments.map((comment, index) => (
                  <React.Fragment key={comment.id}>
                    <ListItem alignItems="flex-start">
                      <ListItemAvatar>
                        <Avatar src={comment.user.avatar} />
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {comment.user.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatTimeAgo(comment.createdAt)}
                            </Typography>
                          </Box>
                        }
                        secondary={comment.text}
                      />
                    </ListItem>
                    {index < comments.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>

              {/* Форма добавления комментария */}
              <form onSubmit={handleSubmit(handleAddComment)}>
                <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                  <Controller
                    name="text"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        placeholder="Добавить комментарий..."
                        fullWidth
                        size="small"
                        error={!!errors.text}
                        helperText={errors.text?.message}
                      />
                    )}
                  />
                  <IconButton type="submit" color="primary">
                    <SendIcon />
                  </IconButton>
                </Box>
              </form>
            </Box>
          </Dialog>

          {/* Уведомления */}
          {error && (
            <Alert
              severity="error"
              sx={{
                position: 'absolute',
                top: 80,
                left: 16,
                right: 16,
                zIndex: 2
              }}
              onClose={() => setError(null)}
            >
              {error}
            </Alert>
          )}
        </Box>
      </Layout>
    </>
  );
};

export default StoryDetailPage;
