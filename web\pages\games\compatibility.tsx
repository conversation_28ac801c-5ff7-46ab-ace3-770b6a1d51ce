import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  LinearProgress,
  Grid,
  Avatar,
  Chip
} from '@mui/material';
import {
  ArrowBack,
  Psychology as PsychologyIcon,
  Favorite as HeartIcon,
  TrendingUp as TrendingIcon,
  EmojiEvents as TrophyIcon,
  Share as ShareIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для теста совместимости
interface Question {
  id: string;
  text: string;
  category: 'values' | 'lifestyle' | 'communication' | 'goals' | 'personality';
  options: {
    value: string;
    text: string;
    weight: number;
  }[];
}

interface TestAnswers {
  [questionId: string]: string;
}

interface CompatibilityResult {
  overallScore: number;
  categories: {
    values: number;
    lifestyle: number;
    communication: number;
    goals: number;
    personality: number;
  };
  strengths: string[];
  challenges: string[];
  recommendations: string[];
  matches: {
    userId: string;
    name: string;
    avatar: string;
    score: number;
  }[];
}

// Схема валидации
const testSchema = yup.object({
  answers: yup.object().test(
    'all-answered',
    'Ответьте на все вопросы',
    function(value) {
      return Object.keys(value || {}).length >= 15; // Минимум 15 вопросов
    }
  )
});

const CompatibilityTestPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [testCompleted, setTestCompleted] = useState(false);
  const [result, setResult] = useState<CompatibilityResult | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<{ answers: TestAnswers }>({
    resolver: yupResolver(testSchema),
    defaultValues: {
      answers: {}
    }
  });

  const watchedAnswers = watch('answers');

  // Загрузка вопросов при монтировании компонента
  useEffect(() => {
    loadQuestions();
  }, []);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения вопросов
      // const response = await getCompatibilityQuestions();
      
      // Мок данные
      const mockQuestions: Question[] = [
        {
          id: 'q1',
          text: 'Что для вас важнее всего в отношениях?',
          category: 'values',
          options: [
            { value: 'trust', text: 'Доверие и честность', weight: 5 },
            { value: 'passion', text: 'Страсть и романтика', weight: 4 },
            { value: 'stability', text: 'Стабильность и надежность', weight: 3 },
            { value: 'freedom', text: 'Свобода и независимость', weight: 2 }
          ]
        },
        {
          id: 'q2',
          text: 'Как вы предпочитаете проводить выходные?',
          category: 'lifestyle',
          options: [
            { value: 'home', text: 'Дома в уютной обстановке', weight: 3 },
            { value: 'nature', text: 'На природе, активно', weight: 4 },
            { value: 'social', text: 'В компании друзей', weight: 5 },
            { value: 'culture', text: 'Посещая культурные мероприятия', weight: 4 }
          ]
        },
        {
          id: 'q3',
          text: 'Как вы решаете конфликты?',
          category: 'communication',
          options: [
            { value: 'discuss', text: 'Открыто обсуждаю проблему', weight: 5 },
            { value: 'time', text: 'Даю время остыть, потом говорю', weight: 4 },
            { value: 'avoid', text: 'Стараюсь избегать конфликтов', weight: 2 },
            { value: 'compromise', text: 'Ищу компромисс', weight: 4 }
          ]
        },
        {
          id: 'q4',
          text: 'Какие у вас планы на будущее?',
          category: 'goals',
          options: [
            { value: 'family', text: 'Создать семью и иметь детей', weight: 5 },
            { value: 'career', text: 'Сосредоточиться на карьере', weight: 3 },
            { value: 'travel', text: 'Путешествовать по миру', weight: 4 },
            { value: 'balance', text: 'Найти баланс во всем', weight: 4 }
          ]
        },
        {
          id: 'q5',
          text: 'Как вы выражаете любовь?',
          category: 'personality',
          options: [
            { value: 'words', text: 'Словами и комплиментами', weight: 4 },
            { value: 'actions', text: 'Поступками и заботой', weight: 5 },
            { value: 'gifts', text: 'Подарками и сюрпризами', weight: 3 },
            { value: 'time', text: 'Проводя время вместе', weight: 5 }
          ]
        }
      ];

      // Добавляем еще вопросы для полного теста
      const additionalQuestions: Question[] = Array.from({ length: 10 }, (_, i) => ({
        id: `q${i + 6}`,
        text: `Дополнительный вопрос ${i + 1} для более точного анализа совместимости`,
        category: ['values', 'lifestyle', 'communication', 'goals', 'personality'][i % 5] as any,
        options: [
          { value: `opt1_${i}`, text: `Вариант 1`, weight: Math.floor(Math.random() * 5) + 1 },
          { value: `opt2_${i}`, text: `Вариант 2`, weight: Math.floor(Math.random() * 5) + 1 },
          { value: `opt3_${i}`, text: `Вариант 3`, weight: Math.floor(Math.random() * 5) + 1 },
          { value: `opt4_${i}`, text: `Вариант 4`, weight: Math.floor(Math.random() * 5) + 1 }
        ]
      }));

      setQuestions([...mockQuestions, ...additionalQuestions]);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке вопросов');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId: string, value: string) => {
    setValue(`answers.${questionId}`, value);
  };

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push('/games');
    }
  };

  const handleSubmitTest = async (data: { answers: TestAnswers }) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для анализа совместимости
      // const response = await analyzeCompatibility(data.answers);
      
      // Мок результат
      const mockResult: CompatibilityResult = {
        overallScore: 87,
        categories: {
          values: 92,
          lifestyle: 78,
          communication: 85,
          goals: 90,
          personality: 89
        },
        strengths: [
          'Схожие жизненные ценности',
          'Отличная коммуникация',
          'Общие цели в отношениях'
        ],
        challenges: [
          'Разные предпочтения в досуге',
          'Различные подходы к решению проблем'
        ],
        recommendations: [
          'Обсуждайте планы на выходные заранее',
          'Найдите компромисс в способах проведения времени',
          'Развивайте общие интересы'
        ],
        matches: [
          {
            userId: 'user1',
            name: 'Анна Петрова',
            avatar: '/avatars/anna.jpg',
            score: 94
          },
          {
            userId: 'user2',
            name: 'Елена Сидорова',
            avatar: '/avatars/elena.jpg',
            score: 89
          },
          {
            userId: 'user3',
            name: 'Мария Иванова',
            avatar: '/avatars/maria.jpg',
            score: 85
          }
        ]
      };

      setResult(mockResult);
      setTestCompleted(true);
      setSuccess('Тест завершен! Получите 50 очков опыта.');
    } catch (err: any) {
      setError(err.message || 'Ошибка при анализе результатов');
    } finally {
      setLoading(false);
    }
  };

  const handleRetakeTest = () => {
    setTestCompleted(false);
    setResult(null);
    setCurrentStep(0);
    setValue('answers', {});
  };

  const handleShareResult = async () => {
    if (!result) return;

    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Мой результат теста совместимости',
          text: `Я прошел тест совместимости и получил ${result.overallScore}% совместимости!`,
          url: window.location.href
        });
      } else {
        await navigator.clipboard.writeText(
          `Я прошел тест совместимости в Likes & Love и получил ${result.overallScore}% совместимости! ${window.location.origin}/games/compatibility`
        );
        setSuccess('Ссылка скопирована в буфер обмена');
      }
    } catch (err) {
      console.error('Ошибка при шаринге:', err);
    }
  };

  const getProgressPercentage = () => {
    const answeredCount = Object.keys(watchedAnswers || {}).length;
    return (answeredCount / questions.length) * 100;
  };

  const getCategoryColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  if (!user) {
    return (
      <Layout title="Тест совместимости">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  if (testCompleted && result) {
    return (
      <>
        <Head>
          <title>Результат теста совместимости - Likes & Love</title>
          <meta name="description" content="Результаты теста совместимости в приложении знакомств Likes & Love" />
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <Layout title="Результат теста">
          <Container maxWidth="md">
            <Box sx={{ py: 3 }}>
              {/* Заголовок */}
              <Box sx={{ mb: 4, textAlign: 'center' }}>
                <IconButton onClick={() => router.push('/games')} sx={{ mb: 2 }}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 2 }}>
                  Результат теста совместимости
                </Typography>
                <Typography variant="h6" color="text.secondary">
                  Ваш уровень совместимости
                </Typography>
              </Box>

              {/* Общий результат */}
              <Card sx={{ mb: 4, textAlign: 'center', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                <CardContent sx={{ color: 'white', py: 4 }}>
                  <Typography variant="h2" fontWeight="bold" sx={{ mb: 2 }}>
                    {result.overallScore}%
                  </Typography>
                  <Typography variant="h5" sx={{ mb: 2 }}>
                    Высокая совместимость
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 3 }}>
                    <Button
                      variant="contained"
                      onClick={handleShareResult}
                      startIcon={<ShareIcon />}
                      sx={{ backgroundColor: 'white', color: 'primary.main' }}
                    >
                      Поделиться
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleRetakeTest}
                      startIcon={<RefreshIcon />}
                      sx={{ borderColor: 'white', color: 'white' }}
                    >
                      Пройти снова
                    </Button>
                  </Box>
                </CardContent>
              </Card>

              {/* Детальные результаты по категориям */}
              <Card sx={{ mb: 4 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                    Результаты по категориям
                  </Typography>
                  <Grid container spacing={2}>
                    {Object.entries(result.categories).map(([category, score]) => (
                      <Grid item xs={12} sm={6} key={category}>
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                              {category === 'values' ? 'Ценности' :
                               category === 'lifestyle' ? 'Образ жизни' :
                               category === 'communication' ? 'Общение' :
                               category === 'goals' ? 'Цели' : 'Личность'}
                            </Typography>
                            <Typography variant="body2" fontWeight="bold">
                              {score}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={score}
                            color={getCategoryColor(score) as any}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>

              {/* Сильные стороны и вызовы */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" color="success.main" sx={{ mb: 2 }}>
                        Сильные стороны
                      </Typography>
                      {result.strengths.map((strength, index) => (
                        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <HeartIcon color="success" sx={{ fontSize: 16 }} />
                          <Typography variant="body2">{strength}</Typography>
                        </Box>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" color="warning.main" sx={{ mb: 2 }}>
                        Области для развития
                      </Typography>
                      {result.challenges.map((challenge, index) => (
                        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <TrendingIcon color="warning" sx={{ fontSize: 16 }} />
                          <Typography variant="body2">{challenge}</Typography>
                        </Box>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Рекомендации */}
              <Card sx={{ mb: 4 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                    Рекомендации
                  </Typography>
                  {result.recommendations.map((recommendation, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                      <PsychologyIcon color="primary" sx={{ fontSize: 16, mt: 0.5 }} />
                      <Typography variant="body2">{recommendation}</Typography>
                    </Box>
                  ))}
                </CardContent>
              </Card>

              {/* Совместимые пользователи */}
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                    Наиболее совместимые пользователи
                  </Typography>
                  <Grid container spacing={2}>
                    {result.matches.map((match) => (
                      <Grid item xs={12} sm={4} key={match.userId}>
                        <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                          <Avatar 
                            src={match.avatar} 
                            sx={{ width: 60, height: 60, mx: 'auto', mb: 1 }}
                          />
                          <Typography variant="subtitle2" fontWeight="bold">
                            {match.name}
                          </Typography>
                          <Chip 
                            label={`${match.score}% совместимость`}
                            color="success"
                            size="small"
                            sx={{ mt: 1 }}
                          />
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Тест совместимости - Likes & Love</title>
        <meta name="description" content="Пройдите тест совместимости и узнайте, насколько вы подходите другим пользователям в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Тест совместимости">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Тест совместимости
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Ответьте на вопросы, чтобы узнать свой уровень совместимости
              </Typography>
            </Box>

            {/* Прогресс */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body2">
                    Вопрос {currentStep + 1} из {questions.length}
                  </Typography>
                  <Typography variant="body2">
                    {Math.round(getProgressPercentage())}% завершено
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={getProgressPercentage()}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Вопросы */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : questions.length > 0 && (
              <form onSubmit={handleSubmit(handleSubmitTest)}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                      {questions[currentStep]?.text}
                    </Typography>

                    <Controller
                      name={`answers.${questions[currentStep]?.id}`}
                      control={control}
                      render={({ field }) => (
                        <FormControl component="fieldset" fullWidth>
                          <RadioGroup
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              handleAnswerChange(questions[currentStep].id, e.target.value);
                            }}
                          >
                            {questions[currentStep]?.options.map((option) => (
                              <FormControlLabel
                                key={option.value}
                                value={option.value}
                                control={<Radio />}
                                label={option.text}
                                sx={{ mb: 1 }}
                              />
                            ))}
                          </RadioGroup>
                        </FormControl>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Навигация */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                  <Button onClick={handleBack}>
                    {currentStep === 0 ? 'Назад к играм' : 'Предыдущий'}
                  </Button>
                  
                  {currentStep < questions.length - 1 ? (
                    <Button
                      onClick={handleNext}
                      variant="contained"
                      disabled={!watchedAnswers?.[questions[currentStep]?.id]}
                    >
                      Следующий
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading || Object.keys(watchedAnswers || {}).length < questions.length}
                      startIcon={loading ? <CircularProgress size={20} /> : <TrophyIcon />}
                    >
                      {loading ? 'Анализ...' : 'Получить результат'}
                    </Button>
                  )}
                </Box>
              </form>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CompatibilityTestPage;
