import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Upgrade as UpgradeIcon,
  History as HistoryIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Support as SupportIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSubscription } from '../../src/contexts/SubscriptionContext';
import { formatCurrency, getDaysUntilTrialEnd, isTrialActive } from '../../src/services/subscriptionService';

const SubscriptionPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const {
    currentSubscription,
    usage,
    loading,
    error,
    loadCurrentSubscription,
    loadUsage,
    cancelSubscription,
    reactivateSubscription
  } = useSubscription();

  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  const handleCancelSubscription = async () => {
    try {
      setActionLoading(true);
      await cancelSubscription({
        reason: cancelReason || 'Не указана',
        effectiveDate: 'end_of_period'
      });
      setSuccess('Подписка будет отменена в конце текущего периода');
      setShowCancelDialog(false);
      setCancelReason('');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(false);
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      setActionLoading(true);
      await reactivateSubscription();
      setSuccess('Подписка успешно возобновлена');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(false);
    }
  };

  const getSubscriptionStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'cancelled':
        return 'warning';
      case 'expired':
        return 'error';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  const getSubscriptionStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'cancelled':
        return 'Отменена';
      case 'expired':
        return 'Истекла';
      case 'pending':
        return 'Ожидает';
      case 'suspended':
        return 'Приостановлена';
      default:
        return status;
    }
  };

  const formatUsagePercentage = (used: number, limit: number | 'unlimited') => {
    if (limit === 'unlimited') return 100;
    return Math.min((used / limit) * 100, 100);
  };

  const formatUsageText = (used: number, limit: number | 'unlimited') => {
    if (limit === 'unlimited') return `${used} / ∞`;
    return `${used} / ${limit}`;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Моя подписка - Likes & Love</title>
        <meta 
          name="description" 
          content="Управление подпиской в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <StarIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Моя подписка
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<HistoryIcon />}
                  onClick={() => router.push('/subscription/history')}
                >
                  История
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    loadCurrentSubscription();
                    loadUsage();
                  }}
                  disabled={loading}
                >
                  Обновить
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка информации о подписке...
                </Typography>
              </Box>
            ) : !currentSubscription ? (
              <Fade in timeout={600}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <StarIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    У вас нет активной подписки
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Оформите подписку, чтобы получить доступ к премиум-функциям
                  </Typography>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<UpgradeIcon />}
                    onClick={() => router.push('/subscription/plans')}
                  >
                    Выбрать план
                  </Button>
                </Box>
              </Fade>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {/* Current Subscription Card */}
                  <Grid item xs={12} md={8}>
                    <Card elevation={3}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h5" gutterBottom>
                              {currentSubscription.plan.displayName}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {currentSubscription.plan.description}
                            </Typography>
                          </Box>
                          <Chip
                            label={getSubscriptionStatusText(currentSubscription.status)}
                            color={getSubscriptionStatusColor(currentSubscription.status) as any}
                            variant="filled"
                          />
                        </Box>

                        {/* Trial Information */}
                        {isTrialActive(currentSubscription) && (
                          <Alert severity="info" sx={{ mb: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <InfoIcon />
                              <Typography variant="body2">
                                Пробный период: осталось {getDaysUntilTrialEnd(currentSubscription)} дней
                              </Typography>
                            </Box>
                          </Alert>
                        )}

                        {/* Subscription Details */}
                        <Grid container spacing={2} sx={{ mb: 3 }}>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <PaymentIcon fontSize="small" color="action" />
                              <Typography variant="body2" color="text.secondary">
                                Тариф
                              </Typography>
                            </Box>
                            <Typography variant="body1">
                              {formatCurrency(
                                currentSubscription.billingCycle === 'monthly' 
                                  ? currentSubscription.plan.pricing.monthly.price
                                  : currentSubscription.plan.pricing.yearly.price
                              )} / {currentSubscription.billingCycle === 'monthly' ? 'месяц' : 'год'}
                            </Typography>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <ScheduleIcon fontSize="small" color="action" />
                              <Typography variant="body2" color="text.secondary">
                                Следующий платеж
                              </Typography>
                            </Box>
                            <Typography variant="body1">
                              {currentSubscription.nextBillingDate 
                                ? new Date(currentSubscription.nextBillingDate).toLocaleDateString('ru-RU')
                                : 'Не запланирован'
                              }
                            </Typography>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <SecurityIcon fontSize="small" color="action" />
                              <Typography variant="body2" color="text.secondary">
                                Автопродление
                              </Typography>
                            </Box>
                            <Typography variant="body1">
                              {currentSubscription.autoRenew ? 'Включено' : 'Отключено'}
                            </Typography>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <TrendingUpIcon fontSize="small" color="action" />
                              <Typography variant="body2" color="text.secondary">
                                Активна с
                              </Typography>
                            </Box>
                            <Typography variant="body1">
                              {new Date(currentSubscription.startDate).toLocaleDateString('ru-RU')}
                            </Typography>
                          </Grid>
                        </Grid>

                        {/* Plan Features */}
                        <Typography variant="h6" gutterBottom>
                          Возможности плана
                        </Typography>
                        <List dense>
                          {currentSubscription.plan.features.slice(0, 6).map((feature, index) => (
                            <ListItem key={index} sx={{ px: 0 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                <CheckIcon color="success" fontSize="small" />
                              </ListItemIcon>
                              <ListItemText 
                                primary={feature.name}
                                secondary={feature.description}
                              />
                            </ListItem>
                          ))}
                          {currentSubscription.plan.features.length > 6 && (
                            <ListItem sx={{ px: 0 }}>
                              <ListItemText 
                                primary={`И еще ${currentSubscription.plan.features.length - 6} возможностей...`}
                                sx={{ fontStyle: 'italic' }}
                              />
                            </ListItem>
                          )}
                        </List>
                      </CardContent>

                      <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            startIcon={<UpgradeIcon />}
                            onClick={() => router.push('/subscription/plans')}
                          >
                            Изменить план
                          </Button>
                          <Button
                            variant="outlined"
                            startIcon={<SettingsIcon />}
                            onClick={() => router.push('/subscription/payment')}
                          >
                            Способы оплаты
                          </Button>
                        </Box>

                        {currentSubscription.status === 'active' ? (
                          <Button
                            variant="outlined"
                            color="warning"
                            startIcon={<CancelIcon />}
                            onClick={() => setShowCancelDialog(true)}
                            disabled={actionLoading}
                          >
                            Отменить
                          </Button>
                        ) : currentSubscription.status === 'cancelled' ? (
                          <Button
                            variant="contained"
                            color="success"
                            startIcon={<CheckIcon />}
                            onClick={handleReactivateSubscription}
                            disabled={actionLoading}
                          >
                            Возобновить
                          </Button>
                        ) : null}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* Usage Statistics */}
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Использование
                        </Typography>
                        
                        {usage ? (
                          <Box>
                            {/* Daily Likes */}
                            <Box sx={{ mb: 3 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2">Лайки сегодня</Typography>
                                <Typography variant="body2">
                                  {formatUsageText(usage.limits.dailyLikes.used, usage.limits.dailyLikes.limit)}
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={formatUsagePercentage(usage.limits.dailyLikes.used, usage.limits.dailyLikes.limit)}
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>

                            {/* Super Likes */}
                            <Box sx={{ mb: 3 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2">Супер-лайки</Typography>
                                <Typography variant="body2">
                                  {formatUsageText(usage.limits.dailySuperLikes.used, usage.limits.dailySuperLikes.limit)}
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={formatUsagePercentage(usage.limits.dailySuperLikes.used, usage.limits.dailySuperLikes.limit)}
                                color="secondary"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>

                            {/* Boosts */}
                            <Box sx={{ mb: 3 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2">Буст профиля</Typography>
                                <Typography variant="body2">
                                  {formatUsageText(usage.limits.dailyBoosts.used, usage.limits.dailyBoosts.limit)}
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={formatUsagePercentage(usage.limits.dailyBoosts.used, usage.limits.dailyBoosts.limit)}
                                color="warning"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>

                            {/* Video Call Minutes */}
                            <Box sx={{ mb: 3 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2">Видеозвонки (мин)</Typography>
                                <Typography variant="body2">
                                  {formatUsageText(usage.limits.videoCallMinutes.used, usage.limits.videoCallMinutes.limit)}
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={formatUsagePercentage(usage.limits.videoCallMinutes.used, usage.limits.videoCallMinutes.limit)}
                                color="info"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>

                            <Divider sx={{ my: 2 }} />

                            <Typography variant="caption" color="text.secondary">
                              Лимиты обновляются ежедневно в 00:00 МСК
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Статистика использования недоступна
                          </Typography>
                        )}
                      </CardContent>

                      <CardActions>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<SupportIcon />}
                          onClick={() => router.push('/help')}
                        >
                          Поддержка
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                </Grid>
              </Fade>
            )}

            {/* Cancel Subscription Dialog */}
            <Dialog
              open={showCancelDialog}
              onClose={() => setShowCancelDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WarningIcon color="warning" />
                  Отмена подписки
                </Box>
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1" gutterBottom>
                  Вы уверены, что хотите отменить подписку? Доступ к премиум-функциям будет сохранен до конца текущего периода.
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Причина отмены (необязательно):
                </Typography>
                <Box sx={{ mt: 1 }}>
                  <Button
                    variant={cancelReason === 'Слишком дорого' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setCancelReason('Слишком дорого')}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    Слишком дорого
                  </Button>
                  <Button
                    variant={cancelReason === 'Не использую' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setCancelReason('Не использую')}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    Не использую
                  </Button>
                  <Button
                    variant={cancelReason === 'Нашел пару' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setCancelReason('Нашел пару')}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    Нашел пару
                  </Button>
                  <Button
                    variant={cancelReason === 'Другое' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setCancelReason('Другое')}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    Другое
                  </Button>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowCancelDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleCancelSubscription}
                  color="warning"
                  variant="contained"
                  disabled={actionLoading}
                >
                  {actionLoading ? <CircularProgress size={20} /> : 'Отменить подписку'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SubscriptionPage;
