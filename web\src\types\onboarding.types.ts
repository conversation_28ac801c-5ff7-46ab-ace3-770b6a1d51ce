export interface OnboardingStep {
  id: number;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
}

export interface BasicInfo {
  firstName: string;
  lastName: string;
  birthDate: string;
  gender: 'male' | 'female' | 'other';
  location: {
    city: string;
    country: string;
    latitude?: number;
    longitude?: number;
  };
  bio?: string;
  height?: number;
  education?: string;
  jobTitle?: string;
  company?: string;
}

export interface Interest {
  id: string;
  name: string;
  category: string;
  icon?: string;
}

export interface UserInterests {
  selectedInterests: string[];
  customInterests?: string[];
}

export interface PhotoUpload {
  id: string;
  file: File;
  preview: string;
  isMain: boolean;
  order: number;
  uploadProgress?: number;
  isUploaded: boolean;
  url?: string;
}

export interface UserPhotos {
  photos: PhotoUpload[];
  mainPhotoId?: string;
}

export interface MatchPreferences {
  ageRange: [number, number];
  maxDistance: number;
  genderPreference: 'male' | 'female' | 'both';
  relationshipGoals: 'casual' | 'serious' | 'friendship' | 'networking';
  dealBreakers: {
    smoking: boolean;
    drinking: boolean;
    hasChildren: boolean;
    wantsChildren: boolean;
  };
  importantTraits: string[];
}

export interface OnboardingData {
  currentStep: number;
  basicInfo: Partial<BasicInfo>;
  interests: UserInterests;
  photos: UserPhotos;
  preferences: Partial<MatchPreferences>;
  isCompleted: boolean;
  completedAt?: string;
}

export interface OnboardingContextType {
  data: OnboardingData;
  currentStep: number;
  steps: OnboardingStep[];
  updateBasicInfo: (info: Partial<BasicInfo>) => void;
  updateInterests: (interests: UserInterests) => void;
  updatePhotos: (photos: UserPhotos) => void;
  updatePreferences: (preferences: Partial<MatchPreferences>) => void;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  completeOnboarding: () => Promise<void>;
  saveProgress: () => Promise<void>;
  loading: boolean;
  error: string | null;
}

export interface OnboardingValidationErrors {
  basicInfo?: {
    firstName?: string;
    lastName?: string;
    birthDate?: string;
    gender?: string;
    location?: string;
    bio?: string;
  };
  interests?: {
    selectedInterests?: string;
  };
  photos?: {
    photos?: string;
    mainPhoto?: string;
  };
  preferences?: {
    ageRange?: string;
    maxDistance?: string;
    genderPreference?: string;
    relationshipGoals?: string;
  };
}

export interface InterestCategory {
  id: string;
  name: string;
  interests: Interest[];
}

export interface LocationSuggestion {
  id: string;
  name: string;
  country: string;
  latitude: number;
  longitude: number;
}

export interface OnboardingApiResponse {
  success: boolean;
  message: string;
  data?: OnboardingData;
  errors?: OnboardingValidationErrors;
}

export interface UploadPhotoResponse {
  success: boolean;
  message: string;
  photo?: {
    id: string;
    url: string;
    thumbnailUrl: string;
  };
  error?: string;
}
