import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardMedia,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Avatar,
  Chip
} from '@mui/material';
import {
  ArrowBack,
  Send as SendIcon,
  CardGiftcard as GiftIcon,
  Person as PersonIcon,
  Message as MessageIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';

// Типы для отправки подарка
interface SendGiftForm {
  giftId: string;
  recipientId: string;
  message: string;
  isAnonymous: boolean;
  paymentMethod: string;
}

interface Gift {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  category: string;
}

interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
}

// Схема валидации
const sendGiftSchema = yup.object({
  giftId: yup.string().required('Выберите подарок'),
  recipientId: yup.string().required('Выберите получателя'),
  message: yup.string().max(200, 'Максимум 200 символов'),
  isAnonymous: yup.boolean(),
  paymentMethod: yup.string().required('Выберите способ оплаты')
});

const SendGiftPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [matches, setMatches] = useState<User[]>([]);
  const [selectedGift, setSelectedGift] = useState<Gift | null>(null);
  const [selectedRecipient, setSelectedRecipient] = useState<User | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger
  } = useForm<SendGiftForm>({
    resolver: yupResolver(sendGiftSchema),
    defaultValues: {
      giftId: '',
      recipientId: '',
      message: '',
      isAnonymous: false,
      paymentMethod: ''
    }
  });

  const watchedGiftId = watch('giftId');
  const watchedRecipientId = watch('recipientId');
  const watchedMessage = watch('message');
  const watchedIsAnonymous = watch('isAnonymous');

  const steps = [
    'Выберите подарок',
    'Выберите получателя',
    'Добавьте сообщение',
    'Оплата'
  ];

  const paymentMethods = [
    { id: 'card', name: 'Банковская карта', icon: '💳' },
    { id: 'yandex', name: 'Яндекс.Деньги', icon: '🟡' },
    { id: 'tinkoff', name: 'Тинькофф', icon: '🟨' },
    { id: 'sberbank', name: 'Сбербанк', icon: '🟢' }
  ];

  // Загрузка данных при монтировании компонента
  useEffect(() => {
    loadGifts();
    loadMatches();
  }, []);

  // Обновление выбранного подарка
  useEffect(() => {
    if (watchedGiftId) {
      const gift = gifts.find(g => g.id === watchedGiftId);
      setSelectedGift(gift || null);
    }
  }, [watchedGiftId, gifts]);

  // Обновление выбранного получателя
  useEffect(() => {
    if (watchedRecipientId) {
      const recipient = matches.find(m => m.id === watchedRecipientId);
      setSelectedRecipient(recipient || null);
    }
  }, [watchedRecipientId, matches]);

  const loadGifts = async () => {
    try {
      // Здесь будет вызов API для получения подарков
      // const response = await getGifts();
      
      // Мок данные
      const mockGifts: Gift[] = [
        {
          id: 'gift1',
          name: 'Букет роз',
          description: 'Красивый букет красных роз',
          image: '/gifts/roses.jpg',
          price: 500,
          category: 'flowers'
        },
        {
          id: 'gift2',
          name: 'Коробка конфет',
          description: 'Элитные шоколадные конфеты',
          image: '/gifts/chocolates.jpg',
          price: 300,
          category: 'sweets'
        },
        {
          id: 'gift3',
          name: 'Кофе премиум',
          description: 'Ароматный кофе высшего качества',
          image: '/gifts/coffee.jpg',
          price: 200,
          category: 'drinks'
        }
      ];

      setGifts(mockGifts);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке подарков');
    }
  };

  const loadMatches = async () => {
    try {
      // Здесь будет вызов API для получения совпадений
      // const response = await getMatches();
      
      // Мок данные
      const mockMatches: User[] = [
        {
          id: 'user1',
          name: 'Анна Петрова',
          avatar: '/avatars/anna.jpg',
          isOnline: true
        },
        {
          id: 'user2',
          name: 'Михаил Иванов',
          avatar: '/avatars/mikhail.jpg',
          isOnline: false
        },
        {
          id: 'user3',
          name: 'Елена Сидорова',
          avatar: '/avatars/elena.jpg',
          isOnline: true
        }
      ];

      setMatches(mockMatches);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке совпадений');
    }
  };

  const handleNext = async () => {
    const isStepValid = await trigger();
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    if (activeStep === 0) {
      router.push('/gifts');
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };

  const handleSendGift = async (data: SendGiftForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отправки подарка
      // await sendGift(data);

      setSuccess('Подарок успешно отправлен!');
      
      // Перенаправляем на страницу подарков через 2 секунды
      setTimeout(() => {
        router.push('/gifts');
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке подарка');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (!user) {
    return (
      <Layout title="Отправить подарок">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Отправить подарок - Likes & Love</title>
        <meta name="description" content="Отправьте виртуальный подарок в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Отправить подарок">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Отправить подарок
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Удивите своего совпадения приятным сюрпризом
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleSendGift)}>
              <Stepper activeStep={activeStep} orientation={isMobile ? 'vertical' : 'horizontal'}>
                {steps.map((label, index) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                    {isMobile && (
                      <StepContent>
                        {/* Шаг 1: Выбор подарка */}
                        {index === 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                              Выберите подарок
                            </Typography>
                            <Grid container spacing={2}>
                              {gifts.map((gift) => (
                                <Grid item xs={12} sm={6} key={gift.id}>
                                  <Card 
                                    sx={{ 
                                      cursor: 'pointer',
                                      border: watchedGiftId === gift.id ? 2 : 1,
                                      borderColor: watchedGiftId === gift.id ? 'primary.main' : 'divider'
                                    }}
                                    onClick={() => setValue('giftId', gift.id)}
                                  >
                                    <CardMedia
                                      component="img"
                                      height="120"
                                      image={gift.image}
                                      alt={gift.name}
                                    />
                                    <CardContent>
                                      <Typography variant="subtitle1" fontWeight="bold">
                                        {gift.name}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        {gift.description}
                                      </Typography>
                                      <Typography variant="h6" color="primary">
                                        {formatPrice(gift.price)}
                                      </Typography>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              ))}
                            </Grid>
                            <Box sx={{ mt: 2 }}>
                              <Button onClick={handleNext} disabled={!watchedGiftId}>
                                Далее
                              </Button>
                            </Box>
                          </Box>
                        )}

                        {/* Шаг 2: Выбор получателя */}
                        {index === 1 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                              Выберите получателя
                            </Typography>
                            <Controller
                              name="recipientId"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth error={!!errors.recipientId}>
                                  <InputLabel>Получатель</InputLabel>
                                  <Select {...field} label="Получатель">
                                    {matches.map((match) => (
                                      <MenuItem key={match.id} value={match.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                          <Avatar src={match.avatar} sx={{ width: 32, height: 32 }} />
                                          <Box>
                                            <Typography variant="body1">{match.name}</Typography>
                                            {match.isOnline && (
                                              <Chip label="Онлайн" size="small" color="success" />
                                            )}
                                          </Box>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {errors.recipientId && (
                                    <Typography variant="caption" color="error">
                                      {errors.recipientId.message}
                                    </Typography>
                                  )}
                                </FormControl>
                              )}
                            />
                            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                              <Button onClick={handleBack}>Назад</Button>
                              <Button onClick={handleNext} disabled={!watchedRecipientId}>
                                Далее
                              </Button>
                            </Box>
                          </Box>
                        )}

                        {/* Шаг 3: Сообщение */}
                        {index === 2 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                              Добавьте сообщение
                            </Typography>
                            <Controller
                              name="message"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Сообщение (необязательно)"
                                  multiline
                                  rows={3}
                                  fullWidth
                                  error={!!errors.message}
                                  helperText={errors.message?.message || `${field.value?.length || 0}/200`}
                                  sx={{ mb: 2 }}
                                />
                              )}
                            />
                            <Controller
                              name="isAnonymous"
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={<Switch {...field} />}
                                  label="Отправить анонимно"
                                />
                              )}
                            />
                            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                              <Button onClick={handleBack}>Назад</Button>
                              <Button onClick={handleNext}>Далее</Button>
                            </Box>
                          </Box>
                        )}

                        {/* Шаг 4: Оплата */}
                        {index === 3 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                              Способ оплаты
                            </Typography>
                            <Controller
                              name="paymentMethod"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth error={!!errors.paymentMethod} sx={{ mb: 2 }}>
                                  <InputLabel>Способ оплаты</InputLabel>
                                  <Select {...field} label="Способ оплаты">
                                    {paymentMethods.map((method) => (
                                      <MenuItem key={method.id} value={method.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                          <span>{method.icon}</span>
                                          {method.name}
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {errors.paymentMethod && (
                                    <Typography variant="caption" color="error">
                                      {errors.paymentMethod.message}
                                    </Typography>
                                  )}
                                </FormControl>
                              )}
                            />

                            {/* Сводка заказа */}
                            {selectedGift && selectedRecipient && (
                              <Card sx={{ mb: 2 }}>
                                <CardContent>
                                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                                    Сводка заказа
                                  </Typography>
                                  <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                                    <img 
                                      src={selectedGift.image} 
                                      alt={selectedGift.name}
                                      style={{ width: 60, height: 60, borderRadius: 8 }}
                                    />
                                    <Box>
                                      <Typography variant="subtitle1" fontWeight="bold">
                                        {selectedGift.name}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary">
                                        для {selectedRecipient.name}
                                      </Typography>
                                      {watchedIsAnonymous && (
                                        <Chip label="Анонимно" size="small" color="info" />
                                      )}
                                    </Box>
                                  </Box>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="h6">Итого:</Typography>
                                    <Typography variant="h6" color="primary" fontWeight="bold">
                                      {formatPrice(selectedGift.price)}
                                    </Typography>
                                  </Box>
                                </CardContent>
                              </Card>
                            )}

                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button onClick={handleBack}>Назад</Button>
                              <Button
                                type="submit"
                                variant="contained"
                                disabled={loading}
                                startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                              >
                                {loading ? 'Отправка...' : 'Отправить подарок'}
                              </Button>
                            </Box>
                          </Box>
                        )}
                      </StepContent>
                    )}
                  </Step>
                ))}
              </Stepper>

              {/* Desktop версия */}
              {!isMobile && (
                <Box sx={{ mt: 4 }}>
                  {/* Контент для каждого шага */}
                  {activeStep === 0 && (
                    <Box>
                      <Typography variant="h6" sx={{ mb: 3 }}>
                        Выберите подарок
                      </Typography>
                      <Grid container spacing={3}>
                        {gifts.map((gift) => (
                          <Grid item xs={12} sm={6} md={4} key={gift.id}>
                            <Card 
                              sx={{ 
                                cursor: 'pointer',
                                border: watchedGiftId === gift.id ? 2 : 1,
                                borderColor: watchedGiftId === gift.id ? 'primary.main' : 'divider',
                                '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                              }}
                              onClick={() => setValue('giftId', gift.id)}
                            >
                              <CardMedia
                                component="img"
                                height="200"
                                image={gift.image}
                                alt={gift.name}
                              />
                              <CardContent>
                                <Typography variant="h6" fontWeight="bold">
                                  {gift.name}
                                </Typography>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  {gift.description}
                                </Typography>
                                <Typography variant="h6" color="primary">
                                  {formatPrice(gift.price)}
                                </Typography>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}

                  {activeStep === 1 && (
                    <Box>
                      <Typography variant="h6" sx={{ mb: 3 }}>
                        Выберите получателя
                      </Typography>
                      <Controller
                        name="recipientId"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth error={!!errors.recipientId} sx={{ maxWidth: 400 }}>
                            <InputLabel>Получатель</InputLabel>
                            <Select {...field} label="Получатель">
                              {matches.map((match) => (
                                <MenuItem key={match.id} value={match.id}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Avatar src={match.avatar} sx={{ width: 32, height: 32 }} />
                                    <Box>
                                      <Typography variant="body1">{match.name}</Typography>
                                      {match.isOnline && (
                                        <Chip label="Онлайн" size="small" color="success" />
                                      )}
                                    </Box>
                                  </Box>
                                </MenuItem>
                              ))}
                            </Select>
                            {errors.recipientId && (
                              <Typography variant="caption" color="error">
                                {errors.recipientId.message}
                              </Typography>
                            )}
                          </FormControl>
                        )}
                      />
                    </Box>
                  )}

                  {activeStep === 2 && (
                    <Box>
                      <Typography variant="h6" sx={{ mb: 3 }}>
                        Добавьте сообщение
                      </Typography>
                      <Controller
                        name="message"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Сообщение (необязательно)"
                            multiline
                            rows={4}
                            fullWidth
                            error={!!errors.message}
                            helperText={errors.message?.message || `${field.value?.length || 0}/200`}
                            sx={{ mb: 3, maxWidth: 600 }}
                          />
                        )}
                      />
                      <Controller
                        name="isAnonymous"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={<Switch {...field} />}
                            label="Отправить анонимно"
                          />
                        )}
                      />
                    </Box>
                  )}

                  {activeStep === 3 && (
                    <Box>
                      <Typography variant="h6" sx={{ mb: 3 }}>
                        Оплата и подтверждение
                      </Typography>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Controller
                            name="paymentMethod"
                            control={control}
                            render={({ field }) => (
                              <FormControl fullWidth error={!!errors.paymentMethod}>
                                <InputLabel>Способ оплаты</InputLabel>
                                <Select {...field} label="Способ оплаты">
                                  {paymentMethods.map((method) => (
                                    <MenuItem key={method.id} value={method.id}>
                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <span>{method.icon}</span>
                                        {method.name}
                                      </Box>
                                    </MenuItem>
                                  ))}
                                </Select>
                                {errors.paymentMethod && (
                                  <Typography variant="caption" color="error">
                                    {errors.paymentMethod.message}
                                  </Typography>
                                )}
                              </FormControl>
                            )}
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          {/* Сводка заказа */}
                          {selectedGift && selectedRecipient && (
                            <Card>
                              <CardContent>
                                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                                  Сводка заказа
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                                  <img 
                                    src={selectedGift.image} 
                                    alt={selectedGift.name}
                                    style={{ width: 60, height: 60, borderRadius: 8 }}
                                  />
                                  <Box>
                                    <Typography variant="subtitle1" fontWeight="bold">
                                      {selectedGift.name}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      для {selectedRecipient.name}
                                    </Typography>
                                    {watchedIsAnonymous && (
                                      <Chip label="Анонимно" size="small" color="info" />
                                    )}
                                  </Box>
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="h6">Итого:</Typography>
                                  <Typography variant="h6" color="primary" fontWeight="bold">
                                    {formatPrice(selectedGift.price)}
                                  </Typography>
                                </Box>
                              </CardContent>
                            </Card>
                          )}
                        </Grid>
                      </Grid>
                    </Box>
                  )}

                  {/* Кнопки навигации */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 4 }}>
                    <Button onClick={handleBack}>
                      {activeStep === 0 ? 'Отмена' : 'Назад'}
                    </Button>
                    {activeStep < steps.length - 1 ? (
                      <Button
                        onClick={handleNext}
                        variant="contained"
                        disabled={
                          (activeStep === 0 && !watchedGiftId) ||
                          (activeStep === 1 && !watchedRecipientId)
                        }
                      >
                        Далее
                      </Button>
                    ) : (
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                      >
                        {loading ? 'Отправка...' : 'Отправить подарок'}
                      </Button>
                    )}
                  </Box>
                </Box>
              )}
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SendGiftPage;
