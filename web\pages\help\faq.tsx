import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  useTheme,
  useMediaQuery,
  IconButton,
  Breadcrumbs,
  Link,
  Divider,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Search as SearchIcon,
  QuestionAnswer as QuestionAnswerIcon,
  ExpandMore as ExpandMoreIcon,
  ArrowBack,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Home as HomeIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';

// Типы для FAQ
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
  notHelpful: number;
  tags: string[];
}

interface FAQCategory {
  id: string;
  name: string;
  count: number;
}

const FAQPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<string | false>(false);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQ[]>([]);

  // Категории FAQ
  const categories: FAQCategory[] = [
    { id: 'all', name: 'Все вопросы', count: 0 },
    { id: 'account', name: 'Аккаунт', count: 8 },
    { id: 'profile', name: 'Профиль', count: 12 },
    { id: 'matching', name: 'Знакомства', count: 15 },
    { id: 'messaging', name: 'Сообщения', count: 10 },
    { id: 'safety', name: 'Безопасность', count: 7 },
    { id: 'subscription', name: 'Подписка', count: 6 },
    { id: 'technical', name: 'Технические', count: 5 }
  ];

  // FAQ данные
  const faqs: FAQ[] = [
    {
      id: 'faq-1',
      question: 'Как создать аккаунт в Likes & Love?',
      answer: 'Для создания аккаунта скачайте приложение, нажмите "Регистрация" и следуйте инструкциям. Вы можете зарегистрироваться через email, номер телефона или социальные сети (VK, Google, Яндекс).',
      category: 'account',
      helpful: 245,
      notHelpful: 12,
      tags: ['регистрация', 'аккаунт', 'создание']
    },
    {
      id: 'faq-2',
      question: 'Как удалить свой аккаунт?',
      answer: 'Перейдите в Настройки → Аккаунт → Удалить аккаунт. Введите пароль для подтверждения. Обратите внимание, что это действие необратимо - все ваши данные, сообщения и совпадения будут удалены навсегда.',
      category: 'account',
      helpful: 189,
      notHelpful: 8,
      tags: ['удаление', 'аккаунт', 'деактивация']
    },
    {
      id: 'faq-3',
      question: 'Как добавить фото в профиль?',
      answer: 'Откройте свой профиль, нажмите на пустое место для фото или на "+" и выберите фото из галереи или сделайте новое. Вы можете добавить до 9 фотографий. Первое фото будет главным.',
      category: 'profile',
      helpful: 156,
      notHelpful: 5,
      tags: ['фото', 'профиль', 'загрузка']
    },
    {
      id: 'faq-4',
      question: 'Почему мои фото не прошли модерацию?',
      answer: 'Фото должны соответствовать нашим правилам: четко видно ваше лицо, нет обнаженного тела, нет рекламы или контактной информации, нет групповых фото где непонятно кто вы. Также запрещены фото с детьми, животными как главными объектами.',
      category: 'profile',
      helpful: 134,
      notHelpful: 15,
      tags: ['модерация', 'фото', 'правила']
    },
    {
      id: 'faq-5',
      question: 'Как работают лайки и совпадения?',
      answer: 'Когда вы лайкаете профиль, а этот человек лайкает вас в ответ - происходит совпадение (match). После этого вы можете писать друг другу сообщения. Если лайк не взаимный, совпадения не будет.',
      category: 'matching',
      helpful: 298,
      notHelpful: 7,
      tags: ['лайки', 'совпадения', 'match']
    },
    {
      id: 'faq-6',
      question: 'Можно ли отменить лайк?',
      answer: 'Да, с Premium подпиской вы можете использовать функцию "Отменить" для отмены последнего действия (лайк или пропуск). Бесплатные пользователи не могут отменять действия.',
      category: 'matching',
      helpful: 167,
      notHelpful: 23,
      tags: ['отмена', 'лайк', 'premium']
    },
    {
      id: 'faq-7',
      question: 'Почему я не получаю совпадения?',
      answer: 'Причин может быть несколько: неполный профиль, некачественные фото, слишком строгие фильтры поиска, малая активность. Попробуйте улучшить профиль, добавить больше фото и расширить критерии поиска.',
      category: 'matching',
      helpful: 445,
      notHelpful: 34,
      tags: ['совпадения', 'алгоритм', 'советы']
    },
    {
      id: 'faq-8',
      question: 'Как начать разговор в чате?',
      answer: 'После совпадения откройте чат и напишите первое сообщение. Лучше всего начать с комментария к фото или информации из профиля. Избегайте банальных "Привет, как дела?"',
      category: 'messaging',
      helpful: 234,
      notHelpful: 11,
      tags: ['чат', 'сообщения', 'знакомство']
    },
    {
      id: 'faq-9',
      question: 'Как пожаловаться на пользователя?',
      answer: 'Откройте профиль пользователя, нажмите на три точки в правом верхнем углу и выберите "Пожаловаться". Выберите причину жалобы и опишите ситуацию. Мы рассмотрим жалобу в течение 24 часов.',
      category: 'safety',
      helpful: 178,
      notHelpful: 6,
      tags: ['жалоба', 'безопасность', 'модерация']
    },
    {
      id: 'faq-10',
      question: 'Что включает Premium подписка?',
      answer: 'Premium включает: неограниченные лайки, просмотр кто лайкнул вас, отмену последнего действия, дополнительные фильтры поиска, приоритет в показе, отсутствие рекламы и другие функции.',
      category: 'subscription',
      helpful: 312,
      notHelpful: 18,
      tags: ['premium', 'подписка', 'функции']
    }
  ];

  // Обновляем количество в категории "Все"
  categories[0].count = faqs.length;

  useEffect(() => {
    let filtered = faqs;

    // Фильтр по категории
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Фильтр по поиску
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query) ||
        faq.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredFAQs(filtered);
  }, [selectedCategory, searchQuery]);

  const handleFAQChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedFAQ(isExpanded ? panel : false);
  };

  const handleHelpful = (faqId: string, isHelpful: boolean) => {
    // Здесь будет вызов API для отметки полезности
    console.log(`FAQ ${faqId} marked as ${isHelpful ? 'helpful' : 'not helpful'}`);
  };

  return (
    <>
      <Head>
        <title>Часто задаваемые вопросы - Likes & Love</title>
        <meta name="description" content="Ответы на часто задаваемые вопросы о приложении знакомств Likes & Love. FAQ по регистрации, профилю, знакомствам и безопасности." />
        <meta name="keywords" content="FAQ, вопросы, ответы, помощь, знакомства" />
      </Head>

      <Layout title="Часто задаваемые вопросы">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/help" 
                onClick={(e) => { e.preventDefault(); router.push('/help'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HelpIcon fontSize="small" />
                Помощь
              </Link>
              <Typography color="text.primary">FAQ</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <QuestionAnswerIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Часто задаваемые вопросы
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Найдите быстрые ответы на популярные вопросы
              </Typography>
            </Box>

            {/* Поиск */}
            <Box sx={{ mb: 4 }}>
              <TextField
                fullWidth
                placeholder="Поиск по вопросам..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ maxWidth: 600 }}
              />
            </Box>

            {/* Категории */}
            <Box sx={{ mb: 4 }}>
              <Tabs
                value={selectedCategory}
                onChange={(e, newValue) => setSelectedCategory(newValue)}
                variant={isMobile ? "scrollable" : "standard"}
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                {categories.map((category) => (
                  <Tab
                    key={category.id}
                    value={category.id}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {category.name}
                        <Chip label={category.count} size="small" />
                      </Box>
                    }
                  />
                ))}
              </Tabs>
            </Box>

            {/* FAQ список */}
            <Box>
              {filteredFAQs.length === 0 ? (
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 6 }}>
                    <QuestionAnswerIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary">
                      {searchQuery ? 'Ничего не найдено' : 'Нет вопросов в этой категории'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {searchQuery ? 'Попробуйте изменить поисковый запрос' : 'Выберите другую категорию'}
                    </Typography>
                  </CardContent>
                </Card>
              ) : (
                filteredFAQs.map((faq) => (
                  <Accordion
                    key={faq.id}
                    expanded={expandedFAQ === faq.id}
                    onChange={handleFAQChange(faq.id)}
                    sx={{ mb: 1 }}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6" fontWeight="medium">
                        {faq.question}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body1" sx={{ mb: 3 }}>
                        {faq.answer}
                      </Typography>
                      
                      {/* Теги */}
                      <Box sx={{ mb: 3 }}>
                        {faq.tags.map((tag) => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>

                      {/* Оценка полезности */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Был ли этот ответ полезен?
                        </Typography>
                        <Button
                          size="small"
                          startIcon={<ThumbUpIcon />}
                          onClick={() => handleHelpful(faq.id, true)}
                          sx={{ minWidth: 'auto' }}
                        >
                          {faq.helpful}
                        </Button>
                        <Button
                          size="small"
                          startIcon={<ThumbDownIcon />}
                          onClick={() => handleHelpful(faq.id, false)}
                          sx={{ minWidth: 'auto' }}
                        >
                          {faq.notHelpful}
                        </Button>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Box>

            {/* Не нашли ответ */}
            <Card sx={{ mt: 6, textAlign: 'center' }}>
              <CardContent sx={{ py: 4 }}>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Не нашли ответ на свой вопрос?
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  Свяжитесь с нашей службой поддержки, и мы поможем вам
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => router.push('/help/contact')}
                >
                  Связаться с поддержкой
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default FAQPage;
