import axios from 'axios';
import { config } from '../config';
import {
  ProfilePhoto,
  ProfileVerification,
  ProfileVisitor,
  BlockedUser,
  ProfileStats,
  PhotoUploadData,
  PhotoUploadResponse,
  VerificationRequest,
  VerificationResponse,
  ProfileDeletionRequest,
  ProfileDeletionResponse,
  BlockUserRequest,
  BlockUserResponse,
  PaginatedResponse,
  FilterOptions
} from '../types/profile.types';

const profileApi = axios.create({
  baseURL: `${config.api.baseUrl}/profile`,
  withCredentials: true,
});

// Photos API
export const getProfilePhotos = async (): Promise<ProfilePhoto[]> => {
  const { data } = await profileApi.get('/photos');
  return data.photos;
};

export const uploadProfilePhoto = async (uploadData: PhotoUploadData): Promise<PhotoUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append('photo', uploadData.file);
    if (uploadData.isMain !== undefined) {
      formData.append('isMain', uploadData.isMain.toString());
    }
    if (uploadData.order !== undefined) {
      formData.append('order', uploadData.order.toString());
    }

    const { data } = await profileApi.post('/photos/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      success: true,
      message: data.message || 'Фотография загружена успешно',
      photo: data.photo
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка загрузки фотографии',
      error: error.response?.data?.error
    };
  }
};

export const deleteProfilePhoto = async (photoId: string): Promise<void> => {
  await profileApi.delete(`/photos/${photoId}`);
};

export const setMainProfilePhoto = async (photoId: string): Promise<void> => {
  await profileApi.post(`/photos/${photoId}/set-main`);
};

export const reorderProfilePhotos = async (photoIds: string[]): Promise<void> => {
  await profileApi.post('/photos/reorder', { photoIds });
};

// Verification API
export const getProfileVerifications = async (): Promise<ProfileVerification[]> => {
  const { data } = await profileApi.get('/verifications');
  return data.verifications;
};

export const requestProfileVerification = async (request: VerificationRequest): Promise<VerificationResponse> => {
  try {
    let requestData: any = { type: request.type };

    if (request.type === 'document' && request.data?.documentPhoto) {
      const formData = new FormData();
      formData.append('type', request.type);
      formData.append('documentType', request.data.documentType || '');
      formData.append('documentPhoto', request.data.documentPhoto);

      const { data } = await profileApi.post('/verifications/request', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        message: data.message || 'Запрос на верификацию отправлен',
        verification: data.verification
      };
    } else {
      if (request.data) {
        requestData = { ...requestData, ...request.data };
      }

      const { data } = await profileApi.post('/verifications/request', requestData);

      return {
        success: true,
        message: data.message || 'Запрос на верификацию отправлен',
        verification: data.verification
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка запроса верификации',
      error: error.response?.data?.error
    };
  }
};

export const cancelVerificationRequest = async (verificationId: string): Promise<void> => {
  await profileApi.delete(`/verifications/${verificationId}`);
};

// Visitors API
export const getProfileVisitors = async (filters?: FilterOptions): Promise<PaginatedResponse<ProfileVisitor>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await profileApi.get(`/visitors?${params.toString()}`);
  return data;
};

export const markVisitorsAsRead = async (visitorIds: string[]): Promise<void> => {
  await profileApi.post('/visitors/mark-read', { visitorIds });
};

export const markAllVisitorsAsRead = async (): Promise<void> => {
  await profileApi.post('/visitors/mark-all-read');
};

// Blocked users API
export const getBlockedUsers = async (filters?: FilterOptions): Promise<PaginatedResponse<BlockedUser>> => {
  const params = new URLSearchParams();
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await profileApi.get(`/blocked?${params.toString()}`);
  return data;
};

export const blockUser = async (request: BlockUserRequest): Promise<BlockUserResponse> => {
  try {
    const { data } = await profileApi.post('/blocked', request);
    return {
      success: true,
      message: data.message || 'Пользователь заблокирован'
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка блокировки пользователя',
      error: error.response?.data?.error
    };
  }
};

export const unblockUser = async (userId: string): Promise<void> => {
  await profileApi.delete(`/blocked/${userId}`);
};

// Profile deletion API
export const deleteProfile = async (request: ProfileDeletionRequest): Promise<ProfileDeletionResponse> => {
  try {
    const { data } = await profileApi.post('/delete', request);
    return {
      success: true,
      message: data.message || 'Профиль будет удален',
      scheduledDeletionDate: data.scheduledDeletionDate
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'Ошибка удаления профиля',
      error: error.response?.data?.error
    };
  }
};

export const cancelProfileDeletion = async (): Promise<void> => {
  await profileApi.post('/delete/cancel');
};

// Profile stats API
export const getProfileStats = async (): Promise<ProfileStats> => {
  const { data } = await profileApi.get('/stats');
  return data.stats;
};

// Profile visibility API
export const setProfileVisibility = async (isVisible: boolean): Promise<void> => {
  await profileApi.post('/visibility', { isVisible });
};

export const setProfileOnlineStatus = async (isOnline: boolean): Promise<void> => {
  await profileApi.post('/online-status', { isOnline });
};

// Profile boost API
export const boostProfile = async (duration: number): Promise<{ success: boolean; message: string; expiresAt: string }> => {
  const { data } = await profileApi.post('/boost', { duration });
  return data;
};

export const getBoostStatus = async (): Promise<{ isActive: boolean; expiresAt?: string }> => {
  const { data } = await profileApi.get('/boost/status');
  return data;
};

// Profile completion API
export const getProfileCompletion = async (): Promise<{
  percentage: number;
  missingFields: string[];
  suggestions: string[];
}> => {
  const { data } = await profileApi.get('/completion');
  return data;
};
