import React from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Typography,
  LinearProgress,
  useTheme,
  useMediaQuery,
  Chip
} from '@mui/material';
import {
  CheckCircle,
  RadioButtonUnchecked,
  FiberManualRecord
} from '@mui/icons-material';
import { OnboardingStep } from '../../src/types/onboarding.types';

interface OnboardingProgressProps {
  steps: OnboardingStep[];
  currentStep: number;
  variant?: 'horizontal' | 'vertical' | 'minimal';
  showLabels?: boolean;
  showDescription?: boolean;
}

const OnboardingProgress: React.FC<OnboardingProgressProps> = ({
  steps,
  currentStep,
  variant = 'horizontal',
  showLabels = true,
  showDescription = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Автоматически переключаемся на минимальный вариант на мобильных устройствах
  const displayVariant = isMobile ? 'minimal' : variant;

  const getStepIcon = (step: OnboardingStep) => {
    if (step.isCompleted) {
      return <CheckCircle color="success" />;
    } else if (step.isActive) {
      return <FiberManualRecord color="primary" />;
    } else {
      return <RadioButtonUnchecked color="disabled" />;
    }
  };

  const getProgressPercentage = () => {
    const completedSteps = steps.filter(step => step.isCompleted).length;
    return (completedSteps / steps.length) * 100;
  };

  if (displayVariant === 'minimal') {
    return (
      <Box sx={{ width: '100%', mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Шаг {currentStep} из {steps.length}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {Math.round(getProgressPercentage())}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={getProgressPercentage()}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: theme.palette.grey[200],
            '& .MuiLinearProgress-bar': {
              borderRadius: 4,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
            }
          }}
        />
        {showLabels && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              {steps[currentStep - 1]?.title}
            </Typography>
            {showDescription && (
              <Typography variant="body2" color="text.secondary">
                {steps[currentStep - 1]?.description}
              </Typography>
            )}
          </Box>
        )}
      </Box>
    );
  }

  if (displayVariant === 'vertical') {
    return (
      <Box sx={{ width: '100%' }}>
        <Stepper activeStep={currentStep - 1} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.id} completed={step.isCompleted}>
              <StepLabel
                icon={getStepIcon(step)}
                sx={{
                  '& .MuiStepLabel-label': {
                    fontWeight: step.isActive ? 600 : 400,
                    color: step.isActive ? theme.palette.primary.main : 'inherit'
                  }
                }}
              >
                <Box>
                  <Typography variant="subtitle1">
                    {step.title}
                  </Typography>
                  {showDescription && (
                    <Typography variant="body2" color="text.secondary">
                      {step.description}
                    </Typography>
                  )}
                  {step.isActive && (
                    <Chip
                      label="Текущий шаг"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  )}
                </Box>
              </StepLabel>
              {showDescription && (
                <StepContent>
                  <Typography variant="body2" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepContent>
              )}
            </Step>
          ))}
        </Stepper>
      </Box>
    );
  }

  // Horizontal variant (default)
  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      {/* Progress bar */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Прогресс регистрации
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {Math.round(getProgressPercentage())}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={getProgressPercentage()}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: theme.palette.grey[200],
            '& .MuiLinearProgress-bar': {
              borderRadius: 3,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
            }
          }}
        />
      </Box>

      {/* Steps */}
      <Stepper activeStep={currentStep - 1} alternativeLabel>
        {steps.map((step, index) => (
          <Step key={step.id} completed={step.isCompleted}>
            <StepLabel
              icon={getStepIcon(step)}
              sx={{
                '& .MuiStepLabel-label': {
                  fontWeight: step.isActive ? 600 : 400,
                  color: step.isActive ? theme.palette.primary.main : 'inherit',
                  fontSize: isMobile ? '0.75rem' : '0.875rem'
                }
              }}
            >
              {showLabels && (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography 
                    variant={isMobile ? "caption" : "body2"}
                    sx={{ 
                      fontWeight: step.isActive ? 600 : 400,
                      color: step.isActive ? theme.palette.primary.main : 'inherit'
                    }}
                  >
                    {step.title}
                  </Typography>
                  {showDescription && !isMobile && (
                    <Typography variant="caption" color="text.secondary" display="block">
                      {step.description}
                    </Typography>
                  )}
                </Box>
              )}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Current step info */}
      {showLabels && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            {steps[currentStep - 1]?.title}
          </Typography>
          {showDescription && (
            <Typography variant="body2" color="text.secondary">
              {steps[currentStep - 1]?.description}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default OnboardingProgress;
