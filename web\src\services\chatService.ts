import axios from 'axios';
import { config } from '../config';
import {
  Conversation,
  ChatMessage,
  SendMessageRequest,
  SendMessageResponse,
  CreateConversationRequest,
  CreateConversationResponse,
  CallRequest,
  CallResponse,
  CallSession,
  ChatFilter,
  PaginatedResponse,
  ChatSettings,
  GroupChatInfo,
  GroupChatMember,
  StickerPack,
  ChatStatistics
} from '../types/chat.types';

const chatApi = axios.create({
  baseURL: `${config.api.baseUrl}/chat`,
  withCredentials: true,
});

// Conversations API
export const getConversations = async (filter?: ChatFilter): Promise<PaginatedResponse<Conversation>> => {
  const params = new URLSearchParams();
  if (filter) {
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
  }

  const { data } = await chatApi.get(`/conversations?${params.toString()}`);
  return data;
};

export const getConversation = async (conversationId: string): Promise<Conversation> => {
  const { data } = await chatApi.get(`/conversations/${conversationId}`);
  return data.conversation;
};

export const createConversation = async (request: CreateConversationRequest): Promise<CreateConversationResponse> => {
  try {
    const { data } = await chatApi.post('/conversations', request);
    return {
      success: true,
      conversation: data.conversation
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Ошибка создания беседы'
    };
  }
};

export const archiveConversation = async (conversationId: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/archive`);
};

export const unarchiveConversation = async (conversationId: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/unarchive`);
};

export const muteConversation = async (conversationId: string, duration?: number): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/mute`, { duration });
};

export const unmuteConversation = async (conversationId: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/unmute`);
};

export const pinConversation = async (conversationId: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/pin`);
};

export const unpinConversation = async (conversationId: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/unpin`);
};

export const deleteConversation = async (conversationId: string): Promise<void> => {
  await chatApi.delete(`/conversations/${conversationId}`);
};

// Messages API
export const getMessages = async (
  conversationId: string, 
  page: number = 1, 
  limit: number = 50
): Promise<PaginatedResponse<ChatMessage>> => {
  const { data } = await chatApi.get(`/conversations/${conversationId}/messages?page=${page}&limit=${limit}`);
  return data;
};

export const sendMessage = async (request: SendMessageRequest): Promise<SendMessageResponse> => {
  try {
    let requestData: any = {
      conversationId: request.conversationId,
      text: request.text,
      type: request.type,
      replyToMessageId: request.replyToMessageId,
      metadata: request.metadata
    };

    if (request.attachments && request.attachments.length > 0) {
      const formData = new FormData();
      Object.entries(requestData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value.toString());
          }
        }
      });

      request.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });

      const { data } = await chatApi.post('/messages', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        message: data.message
      };
    } else {
      const { data } = await chatApi.post('/messages', requestData);
      return {
        success: true,
        message: data.message
      };
    }
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Ошибка отправки сообщения'
    };
  }
};

export const markMessagesAsRead = async (conversationId: string, messageIds: string[]): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/mark-read`, { messageIds });
};

export const deleteMessage = async (messageId: string): Promise<void> => {
  await chatApi.delete(`/messages/${messageId}`);
};

export const editMessage = async (messageId: string, newText: string): Promise<void> => {
  await chatApi.put(`/messages/${messageId}`, { text: newText });
};

export const addMessageReaction = async (messageId: string, emoji: string): Promise<void> => {
  await chatApi.post(`/messages/${messageId}/reactions`, { emoji });
};

export const removeMessageReaction = async (messageId: string, reactionId: string): Promise<void> => {
  await chatApi.delete(`/messages/${messageId}/reactions/${reactionId}`);
};

export const forwardMessage = async (messageId: string, conversationIds: string[]): Promise<void> => {
  await chatApi.post(`/messages/${messageId}/forward`, { conversationIds });
};

// Calls API
export const initiateCall = async (request: CallRequest): Promise<CallResponse> => {
  try {
    const { data } = await chatApi.post('/calls/initiate', request);
    return {
      success: true,
      session: data.session
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Ошибка инициации звонка'
    };
  }
};

export const acceptCall = async (sessionId: string): Promise<void> => {
  await chatApi.post(`/calls/${sessionId}/accept`);
};

export const declineCall = async (sessionId: string): Promise<void> => {
  await chatApi.post(`/calls/${sessionId}/decline`);
};

export const endCall = async (sessionId: string): Promise<void> => {
  await chatApi.post(`/calls/${sessionId}/end`);
};

export const getCallSession = async (sessionId: string): Promise<CallSession> => {
  const { data } = await chatApi.get(`/calls/${sessionId}`);
  return data.session;
};

export const getCallHistory = async (conversationId?: string): Promise<CallSession[]> => {
  const params = conversationId ? `?conversationId=${conversationId}` : '';
  const { data } = await chatApi.get(`/calls/history${params}`);
  return data.sessions;
};

// Typing indicators
export const setTyping = async (conversationId: string, isTyping: boolean): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/typing`, { isTyping });
};

// User blocking
export const blockUser = async (userId: string): Promise<void> => {
  await chatApi.post(`/users/${userId}/block`);
};

export const unblockUser = async (userId: string): Promise<void> => {
  await chatApi.post(`/users/${userId}/unblock`);
};

export const getBlockedUsers = async (): Promise<string[]> => {
  const { data } = await chatApi.get('/users/blocked');
  return data.blockedUserIds;
};

// Reporting
export const reportConversation = async (conversationId: string, reason: string, details?: string): Promise<void> => {
  await chatApi.post(`/conversations/${conversationId}/report`, { reason, details });
};

export const reportMessage = async (messageId: string, reason: string, details?: string): Promise<void> => {
  await chatApi.post(`/messages/${messageId}/report`, { reason, details });
};

// Settings
export const getChatSettings = async (): Promise<ChatSettings> => {
  const { data } = await chatApi.get('/settings');
  return data.settings;
};

export const updateChatSettings = async (settings: Partial<ChatSettings>): Promise<void> => {
  await chatApi.put('/settings', settings);
};

// Group chats
export const createGroupChat = async (
  name: string, 
  description: string, 
  participantIds: string[]
): Promise<Conversation> => {
  const { data } = await chatApi.post('/groups', {
    name,
    description,
    participantIds
  });
  return data.conversation;
};

export const getGroupInfo = async (groupId: string): Promise<GroupChatInfo> => {
  const { data } = await chatApi.get(`/groups/${groupId}/info`);
  return data.groupInfo;
};

export const updateGroupInfo = async (
  groupId: string, 
  updates: Partial<GroupChatInfo>
): Promise<void> => {
  await chatApi.put(`/groups/${groupId}/info`, updates);
};

export const getGroupMembers = async (groupId: string): Promise<GroupChatMember[]> => {
  const { data } = await chatApi.get(`/groups/${groupId}/members`);
  return data.members;
};

export const addGroupMembers = async (groupId: string, userIds: string[]): Promise<void> => {
  await chatApi.post(`/groups/${groupId}/members`, { userIds });
};

export const removeGroupMember = async (groupId: string, userId: string): Promise<void> => {
  await chatApi.delete(`/groups/${groupId}/members/${userId}`);
};

export const updateMemberRole = async (
  groupId: string, 
  userId: string, 
  role: 'admin' | 'moderator' | 'member'
): Promise<void> => {
  await chatApi.put(`/groups/${groupId}/members/${userId}/role`, { role });
};

export const leaveGroup = async (groupId: string): Promise<void> => {
  await chatApi.post(`/groups/${groupId}/leave`);
};

// Stickers
export const getStickerPacks = async (): Promise<StickerPack[]> => {
  const { data } = await chatApi.get('/stickers/packs');
  return data.packs;
};

export const purchaseStickerPack = async (packId: string): Promise<void> => {
  await chatApi.post(`/stickers/packs/${packId}/purchase`);
};

export const getOwnedStickerPacks = async (): Promise<StickerPack[]> => {
  const { data } = await chatApi.get('/stickers/owned');
  return data.packs;
};

// Statistics
export const getChatStatistics = async (): Promise<ChatStatistics> => {
  const { data } = await chatApi.get('/statistics');
  return data.statistics;
};

// Search
export const searchMessages = async (
  query: string, 
  conversationId?: string
): Promise<ChatMessage[]> => {
  const params = new URLSearchParams({ query });
  if (conversationId) {
    params.append('conversationId', conversationId);
  }
  
  const { data } = await chatApi.get(`/search/messages?${params.toString()}`);
  return data.messages;
};

export const searchConversations = async (query: string): Promise<Conversation[]> => {
  const { data } = await chatApi.get(`/search/conversations?query=${encodeURIComponent(query)}`);
  return data.conversations;
};

// File upload
export const uploadChatFile = async (file: File, type: 'image' | 'voice' | 'video' | 'file'): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  const { data } = await chatApi.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return data.url;
};

// Voice messages
export const transcribeVoiceMessage = async (messageId: string): Promise<string> => {
  const { data } = await chatApi.post(`/messages/${messageId}/transcribe`);
  return data.transcription;
};

// Message scheduling
export const scheduleMessage = async (
  conversationId: string,
  text: string,
  scheduledAt: string
): Promise<void> => {
  await chatApi.post('/messages/schedule', {
    conversationId,
    text,
    scheduledAt
  });
};

export const getScheduledMessages = async (): Promise<ChatMessage[]> => {
  const { data } = await chatApi.get('/messages/scheduled');
  return data.messages;
};

export const cancelScheduledMessage = async (messageId: string): Promise<void> => {
  await chatApi.delete(`/messages/scheduled/${messageId}`);
};

// WebSocket connection for real-time messaging
export const connectToChat = (conversationId: string, onMessage: (message: ChatMessage) => void) => {
  // WebSocket implementation would go here
  // This is a placeholder for the real-time connection
  console.log(`Connecting to chat ${conversationId}`);
};

export const disconnectFromChat = (conversationId: string) => {
  // WebSocket disconnection would go here
  console.log(`Disconnecting from chat ${conversationId}`);
};
