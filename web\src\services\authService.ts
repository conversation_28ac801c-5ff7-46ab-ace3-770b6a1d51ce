import axios from 'axios';
import { config } from '../config';
import {
  User,
  LoginCredentials,
  RegisterData,
  EmailVerificationData,
  PhoneVerificationData,
  TwoFactorSetupData,
  TwoFactorVerificationData,
  SocialCallbackData,
  LogoutData,
  VerificationResult
} from '../types/auth.types';

const authApi = axios.create({
  baseURL: `${config.api.baseUrl}/auth`,
  withCredentials: true,
});

export const login = async (credentials: LoginCredentials): Promise<User> => {
  const { data } = await authApi.post('/login', credentials);
  return data.user;
};

export const register = async (userData: RegisterData): Promise<User> => {
  const { data } = await authApi.post('/register', userData);
  return data.user;
};

export const logout = async (): Promise<void> => {
  await authApi.post('/logout');
};

export const getCurrentUser = async (): Promise<User> => {
  const { data } = await authApi.get('/me');
  return data.user;
};

export const verifyEmail = async (token: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/verify-email', { token });
  return data;
};

export const verifyPhone = async (code: string, phoneNumber: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/verify-phone', { code, phoneNumber });
  return data;
};

export const requestPasswordReset = async (email: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/forgot-password', { email });
  return data;
};

export const resetPassword = async (token: string, password: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/reset-password', { token, password });
  return data;
};

export const socialLogin = async (provider: string, code: string): Promise<User> => {
  const { data } = await authApi.post(`/social/${provider}`, { code });
  return data.user;
};

export const verifyEmailWithData = async (verificationData: EmailVerificationData): Promise<VerificationResult> => {
  try {
    const { data } = await authApi.post('/verify-email', verificationData);
    return {
      loading: false,
      success: true,
      message: data.message || 'Email успешно подтвержден'
    };
  } catch (error: any) {
    return {
      loading: false,
      success: false,
      message: error.response?.data?.message || 'Ошибка подтверждения email'
    };
  }
};

export const verifyPhoneWithData = async (verificationData: PhoneVerificationData): Promise<VerificationResult> => {
  try {
    const { data } = await authApi.post('/verify-phone', verificationData);
    return {
      loading: false,
      success: true,
      message: data.message || 'Телефон успешно подтвержден'
    };
  } catch (error: any) {
    return {
      loading: false,
      success: false,
      message: error.response?.data?.message || 'Ошибка подтверждения телефона'
    };
  }
};

export const setupTwoFactor = async (): Promise<TwoFactorSetupData> => {
  const { data } = await authApi.post('/2fa/setup');
  return data;
};

export const verifyTwoFactorCode = async (verificationData: TwoFactorVerificationData): Promise<VerificationResult> => {
  try {
    const { data } = await authApi.post('/2fa/verify', verificationData);
    return {
      loading: false,
      success: true,
      message: data.message || 'Двухфакторная аутентификация настроена'
    };
  } catch (error: any) {
    return {
      loading: false,
      success: false,
      message: error.response?.data?.message || 'Неверный код'
    };
  }
};

export const handleSocialCallback = async (callbackData: SocialCallbackData): Promise<User> => {
  const { data } = await authApi.post('/social/callback', callbackData);
  return data.user;
};

export const logoutWithData = async (logoutData?: LogoutData): Promise<void> => {
  await authApi.post('/logout', logoutData || {});
};
