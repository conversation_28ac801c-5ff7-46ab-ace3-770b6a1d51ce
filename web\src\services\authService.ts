import axios from 'axios';
import { API_URL } from '@/config/constants';
import { User, LoginCredentials, RegisterData } from '@/types/auth.types';

const authApi = axios.create({
  baseURL: `${API_URL}/auth`,
  withCredentials: true,
});

export const login = async (credentials: LoginCredentials): Promise<User> => {
  const { data } = await authApi.post('/login', credentials);
  return data.user;
};

export const register = async (userData: RegisterData): Promise<User> => {
  const { data } = await authApi.post('/register', userData);
  return data.user;
};

export const logout = async (): Promise<void> => {
  await authApi.post('/logout');
};

export const getCurrentUser = async (): Promise<User> => {
  const { data } = await authApi.get('/me');
  return data.user;
};

export const verifyEmail = async (token: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/verify-email', { token });
  return data;
};

export const verifyPhone = async (code: string, phoneNumber: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/verify-phone', { code, phoneNumber });
  return data;
};

export const requestPasswordReset = async (email: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/forgot-password', { email });
  return data;
};

export const resetPassword = async (token: string, password: string): Promise<{ success: boolean; message: string }> => {
  const { data } = await authApi.post('/reset-password', { token, password });
  return data;
};

export const socialLogin = async (provider: string, code: string): Promise<User> => {
  const { data } = await authApi.post(`/social/${provider}`, { code });
  return data.user;
};
